import fetch from "eem-base/utils/fetch";

// 获取产品数据类型计量参数列表
export function queryProductList(params) {
  return fetch({
    url: `/eem-service/v1/product`,
    method: "GET",
    params
  });
}

// 单位成本分析趋势
export function queryUnitObjectCostTrend(data) {
  return fetch({
    url: `/eem-service/v1/unitObjectCost/trend`,
    method: "POST",
    data
  });
}

// 单位成本概览
export function queryUnitObjectCostView(data) {
  return fetch({
    url: `/eem-service/v1/unitObjectCost/view`,
    method: "POST",
    data
  });
}

// 单位成本占比
export function queryUnitObjectCostPercent(data) {
  return fetch({
    url: `/eem-service/v1/unitObjectCost/percent`,
    method: "POST",
    data
  });
}
