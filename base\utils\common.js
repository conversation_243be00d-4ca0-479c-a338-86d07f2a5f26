import _ from "lodash";
import moment from "moment";
import { FullScreenLoading } from "@omega/http/loading.js";
import ElementUI from "element-ui";
import { api } from "@altair/knight";
import omegaI18n from "@omega/i18n";
import fetch from "./fetch.js";

const loading = new FullScreenLoading();

const InvalidValue = [
  undefined,
  "",
  null,
  -2147483648,
  "NaN",
  "Infinity",
  2147483648
];
function toFixed2(value, precision) {
  precision = precision || 0;
  var pow = Math.pow(10, precision);
  return (Math.round(value * pow) / pow).toFixed(precision);
}
function formatterDate(formatStr = "YYYY-MM-DD HH:mm:ss") {
  return function (row, column, cellValue) {
    //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
    if (cellValue) {
      return moment(cellValue).format(formatStr);
    } else {
      return formatNullCol(cellValue);
    }
  };
}

//格式化数字列
function formatNumber(precision = 2) {
  return function (row, column, cellValue) {
    if (cellValue) {
      if (!_.isNumber(cellValue)) {
        //先转换成数字
        cellValue = parseFloat(cellValue);
      }

      return toFixed2(cellValue, precision); //保留两位小数
    } else {
      return formatNullCol(cellValue);
    }
  };
}

function formatNullCol(cellValue) {
  if (cellValue === 0 || cellValue === "") {
    return cellValue;
  } else {
    return "--";
  }
}

//格式化文本列
function formatText() {
  return function (row, column, cellValue) {
    if (["", undefined, null, NaN].includes(cellValue)) {
      return "--";
    } else {
      return cellValue;
    }
  };
}

/***表格列自定义formatter函数 ***/
export const tableColumsFormatter = {
  formatDateColumn: formatterDate("YYYY-MM-DD HH:mm:ss"), // 格式化日期格式数据
  formatDateCol: formatterDate, //柯里化格式化表格时间列, 可以传入自定义时间格式字符串
  formatNumberColumn: formatNumber(), //
  formatNumberCol: formatNumber, //柯里化格式化表格数值列, 可以传入保留的小数位
  formatNullCol: formatNullCol,
  formatTextCol: formatText //柯里化格式化表格数值列, 可以传入保留的小数位
};

/***表单自定义formatter函数 ***/
const formRules = {
  check_pattern_name: {
    // pattern: /^[a-z]+$/,
    // pattern: /((?=[\x21-\x7e]+)[^A-Za-z0-9])/,
    // pattern: /^((?!`~!@#$%^&*()_+-=[]{}\|;:\'"<,>.?\/).)*$/,
    pattern: /^((?![`~!@$%^&*()+=[\]{}\\|;:'"<,>.?/]).)*$/, //如果把-加进去就表示数字也算特殊字符了，所以-不能加进去
    message: $T("请不要输入特殊字符"),
    trigger: ["blur", "change"]
  },
  check_stringLessThan140: {
    min: 1,
    max: 140,
    message: $T("长度在 1 到 {0} 个字符", 140),
    trigger: ["blur", "change"]
  },
  check_name: {
    min: 1,
    max: 50,
    message: $T("长度在 1 到 {0} 个字符", 50),
    trigger: ["blur", "change"]
  },
  check_stringLessThan255: {
    min: 1,
    max: 255,
    message: $T("长度在 1 到 {0} 个字符", 255),
    trigger: ["blur", "change"]
  },
  check_strongPassword: {
    pattern:
      /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@#$%^&*()+=[\]{}\\|;:'"<,>.?/]).{8,18}$/,
    trigger: ["blur", "change"],
    message: $T("密码需含有大写、小写字母、数字和特殊字符，且长度为8~18位")
  },
  pattern_name: {
    // pattern: /^[a-z]+$/,
    // pattern: /((?=[\x21-\x7e]+)[^A-Za-z0-9])/,
    // pattern: /^((?!`~!@#$%^&*()_+-=[]{}\|;:\'"<,>.?\/).)*$/,
    pattern: /^((?![`~!@$%^&*+=[\]{}|;:'"<,>.?]).)*$/, //如果把-加进去就表示数字也算特殊字符了，所以-不能加进去
    message: $T("请不要输入特殊字符"),
    trigger: ["blur", "change"]
  },
  check_space: {
    pattern: /^[^\s]+$/,
    message: $T("请不要输入空格"),
    trigger: ["blur", "change"]
  },
  check_phone: {
    pattern: /^1[3|4|5|6|7|8|9]\d{9}$/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的手机号")
  },
  check_stringLessThan50: {
    min: 1,
    max: 50,
    message: $T("长度在 1 到 {0} 个字符", 50),
    trigger: ["blur", "change"]
  },
  check_telephone: {
    pattern: /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的固定电话 例：010-88888888-123")
  },
  check_phoneandtelephone: {
    // pattern: /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?|(1[3|4|5|7|8][0-9]\d{8})$/,
    pattern: /^(0\d{2,3}-)\d{7,8}(-(\d{3,}))?$|(1[3|4|5|7|8][0-9]\d{8})$/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的手机号或固定电话 例：010-88888888-123")
  },
  check_phone_or_telephone: {
    pattern:
      /(^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$)|(^1[3|4|5|7|8][0-9]\d{8}$)/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的手机号或固定电话")
  },
  check_numberInt: {
    min: 0,
    max: 999999999,
    step: 2,
    precision: 0,
    controlsPosition: "",
    placeholder: $T("请输入内容")
  },
  check_numberFloat: {
    min: 0,
    max: 999999999999.99,
    step: 2,
    precision: 2,
    controlsPosition: "",
    placeholder: $T("请输入内容")
  },
  check_numberFloat11: {
    min: 0,
    // eslint-disable-next-line no-loss-of-precision
    max: 999999999999.999999,
    step: 2,
    precision: 6,
    controlsPosition: "",
    placeholder: $T("请输入内容")
  },
  // 只能选今天以前（包含今天）
  pickerOptions_earlierThanYesterd11: {
    disabledDate(time) {
      return time.getTime() > moment().valueOf();
    }
  },
  // 只能选今天以前（不包含今天）
  pickerOptions_earlierThanYesterd: {
    disabledDate(time) {
      return time.getTime() > moment().subtract(1, "d").valueOf();
    }
  },
  // 只能选明天以前（包含明天）
  pickerOptions_addOneDay: {
    disabledDate(time) {
      return time.getTime() > moment().add(1, "d").valueOf();
    }
  },
  // 只能选今天以后（包含今天）
  pickerOptions_laterThanYesterd11: {
    disabledDate(time) {
      return time.getTime() < moment().subtract(1, "d").valueOf();
    }
  },
  // 只能选今天以后（不包含今天）
  pickerOptions_laterThanYesterd: {
    disabledDate(time) {
      return time.getTime() < moment().valueOf();
    }
  },
  pickerOptions_laterThanTodayEarlierThan28: {
    disabledDate(time) {
      return time.getTime() <= Date.now() || time.getDate() > 28;
    }
  },
  pickerOptions_laterThanYesterday: {
    disabledDate(time) {
      return time.getTime() <= moment(Date.now()).subtract(1, "days");
    }
  },
  pickerOptions_earlierThanTomorrow: {
    disabledDate(time) {
      return time.getTime() > Date.now();
    }
  }
};

const util = {
  formatNumber: function (config, value) {
    if (_.has(config, "precision") && _.isNumber(config.precision)) {
      value = tableColumsFormatter.formatNumberWithPrecision(
        value,
        config.precision
      );
    }

    return value;
  },
  formatNumberWithPrecision: function (value, precision) {
    if (_.isNumber(precision)) {
      if (!_.isNumber(value)) {
        //先转换成数字
        value = parseFloat(value);
      }
      if (isNaN(value)) {
        //如果为空直接返回--
        return "--";
      }
      value = toFixed2(value, precision); //不为空的话就保留小数位
    }

    return value;
  },
  formatBoolean: function (config, value) {
    if (_.has(config, "trueText") && _.has(config, "falseText")) {
      value = value ? config.trueText : config.falseText;
    }
    return value;
  }
};

// 檢查导出名称，没有后缀的话默认补上xlsx
function checkFileName(name) {
  if (name.includes(".")) {
    return name;
  }
  return name + ".xlsx";
}

/**
 * 是否为开发环境
 */
function isDev() {
  return process.env.NODE_ENV === "development";
}

// 毫秒数转化
function secondsFormat(sec, format) {
  let hour = Math.floor(sec / 3600000);
  let minute = Math.floor((sec - hour * 3600000) / 60000);
  let second = Math.floor((sec - hour * 3600000 - minute * 60000) / 1000);
  if (hour < 10) {
    hour = "0" + hour;
  }
  // if (minute < 10) {
  //   minute = "0" + minute;
  // }
  // if (second < 10) {
  //   second = "0" + second;
  // }
  if (
    format.indexOf("hh") !== -1 &&
    format.indexOf("mm") !== -1 &&
    format.indexOf("ss") !== -1
  ) {
    return format.replace(
      /(.*)hh(.*)mm(.*)ss(.*)/,
      "$1" + hour + "$2" + minute + "$3" + second + "$4"
    );
  } else if (
    format.indexOf("hh") !== -1 &&
    format.indexOf("mm") !== -1 &&
    format.indexOf("ss") === -1
  ) {
    return format.replace(
      /(.*)hh(.*)mm(.*)/,
      "$1" + hour + "$2" + minute + "$3"
    );
  } else if (
    format.indexOf("hh") !== -1 &&
    format.indexOf("mm") === -1 &&
    format.indexOf("ss") === -1
  ) {
    return format.replace(/(.*)hh(.*)/, "$1" + hour + "$2");
  } else if (
    format.indexOf("hh") === -1 &&
    format.indexOf("mm") !== -1 &&
    format.indexOf("ss") !== -1
  ) {
    minute += hour * 60;
    return format.replace(
      /(.*)mm(.*)ss(.*)/,
      "$1" + minute + "$2" + second + "$3"
    );
  } else if (
    format.indexOf("hh") === -1 &&
    format.indexOf("mm") === -1 &&
    format.indexOf("ss") !== -1
  ) {
    second += hour * 3600 + minute * 60;
    return format.replace(/(.*)ss(.*)/, "$1" + second + "$2");
  }
}

function downExcel(url, data, token, projectId, callback) {
  const user = api.getUser()?._user;
  loading.showLoading();
  fetch
    .post(url, data, {
      responseType: "arraybuffer",
      headers: {
        "User-ID": isDev() ? user.id : undefined,
        "Accept-Language":
          omegaI18n.locale === "en" ? "en-US,en;q=0.9" : "zh-CN,zh;q=0.9"
      }
    })
    .then(res => {
      // 判断响应头，如果响应头是json格式的说明有异常
      if (res.headers["content-type"].indexOf("application/json") > -1) {
        loading.hideLoading();
        const blob = new Blob([res.data]);
        // 然后使用fileReader对象进行数据转换和输出;
        const reader = new FileReader();
        reader.readAsText(blob, "utf-8");

        reader.onload = function () {
          const resData = JSON.parse(reader.result || "{}"); //输出文字
          const errMsg = resData.msg || $T("导出文件异常！");
          ElementUI.Message({
            message: errMsg,
            showClose: true,
            type: "error"
          });
        };
        return;
      }

      const url = window.URL.createObjectURL(
        new Blob([res.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;

      // 获取导出文件的名称
      const fileNameStr = res.headers["content-disposition"];
      var reg = /filename=(.*)/;
      var arr = reg.exec(fileNameStr);
      var fileName = $T("导出文件");
      if (arr && arr[1].trim()) {
        if (arr[1].trim().indexOf('"') !== -1) {
          fileName = decodeURIComponent(
            arr[1].trim().slice(1, arr[1].length - 1)
          );
        } else {
          fileName = decodeURIComponent(arr[1].trim());
        }
      }
      fileName = checkFileName(fileName);
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      loading.hideLoading();
      callback && callback(true);
    })
    .catch(function () {
      loading.hideLoading();
      ElementUI.Message({
        message: $T("请检查网络是否连接正常"),
        showClose: true,
        type: "error"
      });
      callback && callback(false);
    });
}
//适用于get请求，带有token验证
function downExcelGET(url, data) {
  const user = api.getUser()?._user;
  loading.showLoading();
  fetch
    .get(url, {
      params: data,
      headers: {
        "User-ID": isDev() ? user.id : undefined,
        "Accept-Language":
          omegaI18n.locale === "en" ? "en-US,en;q=0.9" : "zh-CN,zh;q=0.9"
      },
      responseType: "arraybuffer"
    })
    .then(res => {
      // 判断响应头，如果响应头是json格式的说明有异常
      if (res.headers["content-type"].indexOf("application/json") > -1) {
        loading.hideLoading();
        const blob = new Blob([res.data]);
        // 然后使用fileReader对象进行数据转换和输出;
        const reader = new FileReader();
        reader.readAsText(blob, "utf-8");

        reader.onload = function () {
          const resData = JSON.parse(reader.result || "{}"); //输出文字
          const errMsg = resData.msg || $T("导出文件异常！");
          ElementUI.Message({
            message: errMsg,
            showClose: true,
            type: "error"
          });
        };
        return;
      }

      const url = window.URL.createObjectURL(
        new Blob([res.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;

      // 获取导出文件的名称
      const fileNameStr = res.headers["content-disposition"];
      var reg = /filename=(.*)/;
      var arr = reg.exec(fileNameStr);
      var fileName = $T("导出文件");
      if (arr && arr[1].trim()) {
        if (arr[1].trim().indexOf('"') !== -1) {
          fileName = decodeURIComponent(
            arr[1].trim().slice(1, arr[1].length - 1)
          );
        } else {
          fileName = decodeURIComponent(arr[1].trim());
        }
      }
      fileName = checkFileName(fileName);
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      loading.hideLoading();
    })
    .catch(function () {
      loading.hideLoading();
      ElementUI.Message({
        message: $T("请检查网络是否连接正常"),
        showClose: true,
        type: "error"
      });
    });
}

// 千位加逗号
function formatThousand(num) {
  if (num === 0) return "0";
  if (!num || _.isNaN(_.toNumber(num))) return "--";
  return String(num).replace(/\d+/, n => {
    return n.replace(/\B(?=((\d{3})+\b))/g, ",");
  });
}

// 16进制颜色转rgba
function hexToRgb(hex, alpha) {
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  let color = hex?.toLowerCase();

  if (!reg.test(color ?? "")) {
    return color;
  }

  if (color.length === 4) {
    color = `#${color[1]}${color[1]}${color[2]}${color[2]}${color[3]}${color[3]}`;
  }

  const colorRes = [
    parseInt(`0x${color[1]}${color[2]}`),
    parseInt(`0x${color[3]}${color[4]}`),
    parseInt(`0x${color[5]}${color[6]}`)
  ];

  if (alpha != null) {
    return `rgba(${colorRes.join(",")},${alpha})`;
  }

  return `rgb(${colorRes.join(",")})`;
}

// 通过key查找值,未找到时输出defaultValue
const findDataByKey = (object, path, defaultValue = "--") => {
  var val = _.get(object, path, defaultValue);
  if (InvalidValue.includes(val)) {
    return defaultValue;
  }
  return val;
};

// 将数字四舍五入,输出数字
const roundNumber = (val, precision = 2, defaultValue = undefined) => {
  if (InvalidValue.includes(val)) {
    return defaultValue;
  }
  const num = _.round(val, precision);
  return _.isNaN(num) ? defaultValue : num;
};

// 处理后端返回的单位，比如M3转成M³
const formatSymbol = symbol => {
  var value;
  switch (symbol) {
    case "m3":
      value = "m³";
      break;
    case "M3":
      value = "m³";
      break;

    default:
      value = symbol;
      break;
  }
  return value;
};

//年月日变化
const dateTypeChange = (time, id) => {
  if (id === 1) {
    return moment(time).startOf("day").valueOf();
  } else if (id === 2) {
    return moment(time).startOf("week").valueOf();
  } else if (id === 3) {
    return moment(time).startOf("month").valueOf();
  } else if (id === 5) {
    return moment(time).startOf("year").valueOf();
  } else if (id === 7) {
    return moment(time).startOf("hour").valueOf();
  }
};

// 前一时段
const goToTime = function (time, id) {
  if (id === 1) {
    return moment(time).subtract(1, "d").valueOf();
  } else if (id === 2) {
    return moment(time).subtract(1, "w").valueOf();
  } else if (id === 3) {
    return moment(time).subtract(1, "M").valueOf();
  } else if (id === 5) {
    return moment(time).subtract(1, "y").valueOf();
  } else if (id === 7) {
    return moment(time).subtract(1, "h").valueOf();
  }
};
// 后一时段
const backToTime = function (time, id) {
  if (id === 1) {
    return moment(time).add(1, "d").valueOf();
  } else if (id === 2) {
    return moment(time).add(1, "w").valueOf();
  } else if (id === 3) {
    return moment(time).add(1, "M").valueOf();
  } else if (id === 5) {
    return moment(time).add(1, "y").valueOf();
  } else if (id === 7) {
    return moment(time).add(1, "h").valueOf();
  }
};

// daterange控件的时间初始化
const initDateRange = function (type = "d") {
  const start = moment().startOf(type).valueOf();
  const end = moment().endOf(type).valueOf() + 1;
  return [start, end];
};

export default {
  ...tableColumsFormatter,
  ...formRules,
  ...util,
  checkFileName,
  isDev,
  secondsFormat,
  downExcel,
  downExcelGET,
  formatThousand,
  formatNum: formatThousand,
  hexToRgb,
  findDataByKey,
  toFixed2,
  roundNumber,
  formatSymbol,
  dateTypeChange,
  goToTime,
  backToTime,
  initDateRange
};
