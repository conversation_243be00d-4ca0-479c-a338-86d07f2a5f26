import common from "eem-base/utils/common";
import customApi from "@/api/custom.js";
import moment from "moment";
import store from "@/store";

const projectEnergy = async () => {
  const res = await customApi.queryProjectEnergyList();
  return res.data || [];
};
const getProduct = async () => {
  const res = await customApi.queryProductList();
  return res.data || [];
};
/**
 * 创建表格列配置
 * @param  val  选择的节点
 */
export const createColumnArr = async val => {
  let ElTableColumnArr = [];

  if (val.modelLabel === "district") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属区域"),
        prop: "hierarchy",
        formatter: common.formatTextCol()
      },
      {
        label: $T("项目编号"),
        prop: "code",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属公司"),
        prop: "enterprisename",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "project") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "project",
        formatter: common.formatTextCol()
      },
      {
        label: $T("编号"),
        prop: "code",
        formatter: common.formatTextCol()
      },
      {
        label: $T("面积（㎡）"),
        prop: "area",
        formatter: function (val, cell, cellVal) {
          if (val.floorarea && val.floorarea !== 0) {
            return val.floorarea;
          } else if (val.area && val.area !== 0) {
            return val.area;
          } else if (cellVal === 0) {
            return cellVal;
          } else {
            return "--";
          }
        }
      },
      {
        label: $T("人数（人）"),
        prop: "population",
        formatter: common.formatTextCol()
      },
      {
        label: $T("合作截止时间"),
        prop: "cooperativedeadline",
        formatter: common.formatDateCol("YYYY-MM-DD")
      }
    ];
  } else if (val.modelLabel === "sectionarea") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "sectionarea",
        formatter: common.formatTextCol()
      },
      {
        label: $T("编号"),
        prop: "code",
        formatter: common.formatTextCol()
      },
      {
        label: $T("面积（㎡）"),
        prop: "area",
        formatter: function (val, cell, cellVal) {
          if (val.floorarea && val.floorarea !== 0) {
            return val.floorarea;
          } else if (val.area && val.area !== 0) {
            return val.area;
          } else if (cellVal === 0) {
            return cellVal;
          } else {
            return "--";
          }
        }
      },
      {
        label: $T("人数（人）"),
        prop: "population",
        formatter: common.formatTextCol()
      },
      {
        label: $T("合作截止时间"),
        prop: "cooperativedeadline",
        formatter: common.formatDateCol("YYYY-MM-DD")
      }
    ];
  } else if (val.modelLabel === "building") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "building",
        formatter: common.formatTextCol()
      },
      {
        label: $T("地址"),
        prop: "address",
        formatter: common.formatTextCol()
      },
      {
        label: $T("面积（㎡）"),
        prop: "area",
        formatter: common.formatTextCol()
      },
      {
        label: $T("人数（人）"),
        prop: "population",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "floor") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "floor",
        formatter: common.formatTextCol()
      },
      {
        label: $T("地址"),
        prop: "address",
        formatter: common.formatTextCol()
      },
      {
        label: $T("面积（㎡）"),
        prop: "area",
        formatter: common.formatTextCol()
      },
      {
        label: $T("人数（人）"),
        prop: "population",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "room") {
    if ([1, 2, 3, 4, 5].includes(val.roomtype)) {
      // 配电设备
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("编号"),
          prop: "code",
          formatter: common.formatTextCol()
        },
        {
          label: $T("型号"),
          prop: "model",
          formatter: common.formatTextCol()
        },
        {
          label: $T("投运时间"),
          prop: "commissiondate",
          formatter: common.formatDateCol("YYYY-MM-DD")
        }
      ];
    } else if (val.roomtype === 6) {
      const energys = await projectEnergy();
      const products = await getProduct();
      const enumerations = [
        ...energys.map(i => ({ id: i.energytype, text: i.name })),
        ...products.map(i => ({
          id: i.producttype,
          text: i.name
        })),
        ...store.state.enumerations.rawmaterialtype,
        ...store.state.enumerations.burnproducemattertype
      ];
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("管道类型"),
          prop: "pipetype",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.pipetype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          label: $T("物料类型"),
          prop: "energytype",
          formatter: (row, col, cellValue) => {
            let obj = enumerations.find(item => item.id === cellValue);
            return obj ? obj.text : "--";
          }
        }
      ];
    } else {
      // 普通房间
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("所属层级"),
          prop: "room",
          formatter: common.formatTextCol()
        },
        {
          label: $T("安装位置"),
          prop: "location",
          formatter: common.formatTextCol()
        },
        {
          label: $T("厂家"),
          prop: "manufactor",
          formatter: common.formatTextCol()
        },
        {
          label: $T("型号"),
          prop: "model",
          formatter: common.formatTextCol()
        }
      ];
    }
  } else if (["powerdiscabinet", "arraycabinet"].includes(val.modelLabel)) {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("编号"),
        prop: "code",
        formatter: common.formatTextCol()
      },
      {
        label: $T("品牌"),
        prop: "brand",
        formatter: common.formatTextCol()
      },
      {
        label: $T("型号"),
        prop: "model",
        formatter: common.formatTextCol()
      },
      {
        label: $T("投运时间"),
        prop: "commissiondate",
        formatter: common.formatDateCol("YYYY-MM-DD")
      }
    ];
  } else if (
    [
      "oilcompany",
      "oilproductionplant",
      "operationarea",
      "oilproductioncrew",
      "gasmine"
    ].includes(val.modelLabel)
  ) {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "floor",
        formatter: val =>
          val.oilcompany ||
          val.oilproductionplant ||
          val.operationarea ||
          val.oilproductioncrew ||
          val.gasmine ||
          val.gasoperationarea ||
          "--"
      }
    ];
  } else if (val.modelLabel === "platform") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "platform",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "gasoperationarea") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "gasoperationarea",
        formatter: common.formatTextCol()
      },
      {
        label: $T("地理位置"),
        prop: "geographicalposition",
        formatter: common.formatTextCol()
      },
      {
        label: $T("投运时间"),
        prop: "usagedate",
        formatter: val => {
          const time = val.usagedate || val.operationdate;
          return time ? moment(time).format("YYYY-MM-DD") : "--";
        }
      }
    ];
  } else if (val.modelLabel === "purificationcompany") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "purificationcompany",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "waterinjectionstation") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "waterinjectionstation",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "gasgatheringstation") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "gasgatheringstation",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "oiltransferstation") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "oiltransferstation",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "dehydratingstation") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "dehydratingstation",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "purificationplant") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "purificationplant",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "combinedstation") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "combinedstation",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "gasplatform") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "gasplatform",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "oilwell") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "oilwell",
        formatter: common.formatTextCol()
      },
      {
        label: $T("电机型号"),
        prop: "motortype",
        formatter: common.formatTextCol()
      },
      {
        label: $T("投运时间"),
        prop: "operationdate",
        formatter: common.formatDateCol("YYYY-MM-DD")
      },
      {
        label: $T("使用状态"),
        prop: "usagestate",
        formatter: (row, col, cellValue) => {
          let obj = store.state.enumerations.usagestate.find(
            item => item.id === cellValue
          );
          return obj ? obj.text : "--";
        }
      }
    ];
  } else if (val.modelLabel === "waterinjectionplatform") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      },
      {
        label: $T("所属层级"),
        prop: "waterinjectionplatform",
        formatter: common.formatTextCol()
      }
    ];
  } else if (val.modelLabel === "virtualbuildingnode") {
    ElTableColumnArr = [
      {
        label: $T("节点名称"),
        prop: "name",
        formatter: common.formatTextCol()
      }
    ];
  } else {
    if (val.modelLabel === "manuequipment") {
      // 用能设备
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("所属层级"),
          prop: "room",
          formatter: common.formatTextCol()
        },
        {
          label: $T("安装位置"),
          prop: "location",
          formatter: common.formatTextCol()
        },
        {
          label: $T("厂家"),
          prop: "manufactor",
          formatter: common.formatTextCol()
        },
        {
          label: $T("型号"),
          prop: "model",
          formatter: common.formatTextCol()
        }
      ];
    } else if (
      ["meteorologicalmonitor", "airconditioner", "virtualdevicenode"].includes(
        val.modelLabel
      )
    ) {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        }
      ];
    } else if (val.modelLabel === "civicpipe") {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("所属层级"),
          prop: "project",
          formatter: common.formatTextCol()
        }
      ];
    } else if (val.modelLabel === "pipeline") {
      const energys = await projectEnergy();
      const products = await getProduct();
      const enumerations = [
        ...energys.map(i => ({ id: i.energytype, text: i.name })),
        ...products.map(i => ({
          id: i.producttype,
          text: i.name
        })),
        ...store.state.enumerations.rawmaterialtype,
        ...store.state.enumerations.burnproducemattertype
      ];
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("管道类型"),
          prop: "pipetype",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.pipetype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          label: $T("物料类型"),
          prop: "energytype",
          formatter: (row, col, cellValue) => {
            let obj = enumerations.find(item => item.id === cellValue);
            return obj ? obj.text : "--";
          }
        }
      ];
    } else if (val.modelLabel === "mechanicalminingmachine") {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("电机型号"),
          prop: "motortype",
          formatter: common.formatTextCol()
        },
        {
          label: $T("投运时间"),
          prop: "operationdate",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: $T("使用状态"),
          prop: "usagestate",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.usagestate.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        }
      ];
    } else if (val.modelLabel === "heatingfurnace") {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("投运时间"),
          prop: "operationdate",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: $T("功能类型"),
          prop: "functiontype",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.pumpfunctiontype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          label: $T("使用状态"),
          prop: "usagestate",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.usagestate.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        }
      ];
    } else if (val.modelLabel === "gascompressor") {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("投运时间"),
          prop: "operationdate",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: $T("功能类型"),
          prop: "functiontype",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.gasplatformfunctiontype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          label: $T("使用状态"),
          prop: "usagestate",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.usagestate.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        }
      ];
    } else if (val.modelLabel === "oilcommonequipment") {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("投运时间"),
          prop: "operationdate",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: $T("使用状态"),
          prop: "usagestate",
          formatter: (row, col, cellValue) => {
            let obj = store.state.enumerations.usagestate.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        }
      ];
    } else if (val.modelLabel === "waterinjectionwell") {
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("水井编号"),
          prop: "number",
          formatter: common.formatTextCol()
        }
      ];
    } else {
      // 配电设备
      ElTableColumnArr = [
        {
          label: $T("节点名称"),
          prop: "name",
          formatter: common.formatTextCol()
        },
        {
          label: $T("编号"),
          prop: "code",
          formatter: common.formatTextCol()
        },
        {
          label: $T("型号"),
          prop: "model",
          formatter: common.formatTextCol()
        },
        {
          label: $T("投运时间"),
          prop: "commissiondate",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: $T("厂家"),
          prop: "manufactor",
          formatter: common.formatTextCol()
        }
      ];
    }
  }
  return ElTableColumnArr;
};
