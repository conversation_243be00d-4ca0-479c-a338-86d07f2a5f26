<template>
  <div class="fullfilled relative table-border">
    <el-checkbox
      class="checkedAll"
      :indeterminate="isIndeterminate"
      v-model="checkedAll"
      @change="handleCheckAllChange"
    ></el-checkbox>
    <div
      :class="{
        fullfilled: true,
        light: lightTheme,
        boxShadow: tableScrollLeft
      }"
      id="projectConfig_handsontable"
      ref="projectConfig_handsontable"
    ></div>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import omegaI18n from "@omega/i18n";
export default {
  name: "batchConfigHandsontable",
  props: {
    data: Array,
    columns: Array,
    clearFilterHandle: Number,
    clearSortHandle: Number,
    clearHot: Number
  },
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    importing() {
      return (
        this.$store.getters["importProgress/importing"](5) ||
        this.$store.getters["importProgress/importing"](15)
      );
    },
    hotLanguage() {
      if (omegaI18n.locale === "en") {
        return "en-US";
      }
      return "zh-CN";
    }
  },
  data() {
    return {
      tableScrollLeft: false,
      checkedAll: false,
      isIndeterminate: false,
      tableData: [],
      handsontableFilter: false
    };
  },
  watch: {
    data: {
      handler: function (val) {
        this.tableData = this._.cloneDeep(val);
        this.initTable();
      },
      deep: true,
      immediate: true
    },
    tableData: {
      handler: function (val) {
        const list = this.tableData.filter(item => item.deleteStatus);
        if (!list?.length) {
          this.checkedAll = false;
          this.isIndeterminate = false;
        } else if (list.length === this.tableData.length) {
          this.checkedAll = true;
          this.isIndeterminate = false;
        } else {
          this.checkedAll = true;
          this.isIndeterminate = true;
        }
        this.$emit("update:deleteDisable", !this.checkedAll);
      },
      deep: true,
      immediate: true
    },
    clearFilterHandle() {
      this.clearFilter();
    },
    clearSortHandle() {
      if (!this.hot) {
        return;
      }
      const columnSorting = this.hot.getPlugin("columnSorting");
      const sortConfig = columnSorting?.getSortConfig();
      if (!sortConfig?.length) return;
      columnSorting.clearSort();
    },
    clearHot() {
      if (!this.hot) return;
      this.hot.destroy();
      this.hot = null;
    }
  },
  methods: {
    async initTable() {
      const hotElement = this.$refs.projectConfig_handsontable;
      if (!hotElement) {
        return;
      }
      const tableColumns = [
        {
          data: "deleteStatus",
          type: "checkbox",
          readOnly: false
        },
        ...this.columns,
        {
          className: "htLeft",
          data: "recordStatus",
          type: "text",
          columnSorting: true,
          readOnly: true
        },
        {
          className: "htLeft",
          data: "tree_id",
          type: "text",
          columnSorting: true,
          readOnly: true
        }
      ];
      const tableHeaders = [
        "",
        ...this.columns.map(i => i.label),
        $T("记录状态"),
        "",
        ""
      ];
      const colWidths = [
        50,
        ...this.columns.map(i => {
          return i.minWidth ?? 150;
        }),
        0.001,
        0.001
      ];

      const hotSettings = {
        // autoColumnSize: true,
        fixedColumnsLeft: 3,
        colWidths: colWidths,
        wordWrap: true, // 单元格文字是否换行展示
        data: this.tableData,
        columns: tableColumns,
        readOnly: false,
        stretchH: "all",
        autoWrapRow: true,
        height: "100%",
        maxRows: this.tableData.length,
        rowHeaders: true,
        colHeaders: tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: "vertical", //设置自定填充垂直方向
        manualRowMove: true, //当值为true时，行可拖拽至指定行
        language: this.hotLanguage,
        columnSorting: true,
        afterChange: (val, source) => {
          if (source !== "edit" && source !== "CopyPaste.paste") return;
          const isCheck = val.find(i => i[1] === "deleteStatus");
          if (isCheck) {
            this.changeDataDeleteStatus();
          }
          // 过滤掉勾选的修改
          const newValue = val.filter(item => {
            return item[1] !== "deleteStatus";
          });
          // 修改后将这些行置为未保存状态
          let indexs = newValue.map(i => i[0]);
          this.setTableRowStatus(indexs);
        },
        beforeColumnSort: () => {
          const filtersPlugin = this.hot.getPlugin("filters");
          if (filtersPlugin.isEnabled() && this.handsontableFilter) {
            this.$message.warning($T("请点击重置全部过滤"));
            return false;
          }
          return true;
        },
        beforeFilter: filter => {
          this.handsontableFilter = !!filter?.length;
          // 将此次排序好的数据先保存起来
          return true;
        },
        cells: (row, col, prop) => {
          let cellProperties = {};
          // 生成配置中禁用所有操作
          if (this.importing) {
            cellProperties.readOnly = true;
            return cellProperties;
          }
          if (
            this.tableData[row]?.recordStatus === 2 &&
            prop !== "deleteStatus"
          ) {
            // 已生成配置行不允许修改
            cellProperties.readOnly = true;
          }
          return cellProperties;
        },
        afterGetCellMeta: (row, col, cellProperties) => {
          const rowData = this.hot?.getDataAtRow(row);
          if (!rowData) return;
          const recordStatus = rowData[rowData.length - 2];
          cellProperties.className =
            recordStatus === 2 ? "complete" : "notGenerated";
        },
        beforeRowMove: (row, newDestination) => {
          const filtersPlugin = this.hot.getPlugin("filters");
          if (filtersPlugin.isEnabled() && this.handsontableFilter) {
            this.$message.warning($T("请点击重置全部过滤"));
            return false;
          }
          const columnSorting = this.hot.getPlugin("columnSorting");
          const sortConfig = columnSorting.getSortConfig();
          if (sortConfig?.length) {
            // 自己做排序,将排序去掉
            const tableData = this.getTableData();
            this.customSorting(tableData, row, newDestination);
            columnSorting.clearSort();
            return false;
          }
        }
      };

      if (this.hot) {
        this.hot.updateSettings(hotSettings);
      } else {
        // eslint-disable-next-line no-undef
        this.hot = new Handsontable(hotElement, hotSettings);
      }
      await this.$nextTick();
      this.addScrollEvent();
    },
    changeDataDeleteStatus() {
      const tableData = this._.cloneDeep(this.tableData);
      const data = this.hot.getData();
      data.forEach(item => {
        const tree_id = item[item.length - 1];
        const obj = tableData.find(i => i.tree_id === tree_id);
        obj.deleteStatus = item[0];
      });
      this.tableData = tableData;
    },
    async handleCheckAllChange() {
      const tableData = this.getTableData();
      const data = this.hot.getData();
      const showIds = data.map(item => item[item.length - 1]);
      await this.$nextTick();
      tableData.forEach(item => {
        // 存在过滤条件只会将显示出来的勾上或者取消，其他的置空
        if (this.handsontableFilter) {
          const tree_id = item.tree_id;
          if (showIds.includes(tree_id)) {
            item.deleteStatus = this.checkedAll;
          } else {
            item.deleteStatus = false;
          }
          return tableData;
        } else {
          item.deleteStatus = this.checkedAll;
        }
      });
      this.tableData = tableData;
      this.hot.loadData(this.tableData);
      setTimeout(() => {
        const filtersPlugin = this.hot.getPlugin("filters");
        filtersPlugin.filter();
      }, 300);
    },
    getTableData() {
      // getData才能获取排序后的数据
      const data = this.hot.getData();
      const tableData = this._.cloneDeep(this.tableData);
      if (this.handsontableFilter) {
        return tableData;
      }
      const newTableData = data.map(item => {
        const tree_id = item[item.length - 1];
        return tableData.find(i => i.tree_id === tree_id);
      });
      return newTableData;
    },
    async clearFilter() {
      if (!this.hot) return;
      this.hot.updateSettings({ filters: false });
      await this.$nextTick();
      this.handsontableFilter = false;
      if (!this.hot) return;
      this.hot.updateSettings({ filters: true });
    },
    setTableRowStatus(indexs) {
      if (!indexs?.length) return;
      indexs.forEach(index => {
        const row = this.hot.getDataAtRow(index);
        const tree_id = row[row.length - 1];
        const tableItem = this.tableData.find(i => i.tree_id === tree_id);
        tableItem.recordStatus = -1;
      });
    },
    async customSorting(tableData, row, newDestination) {
      const list = [];
      row.forEach(index => {
        const data = this._.cloneDeep(tableData[index]);
        data.selectRow = true;
        list.push(data);
        tableData[index].deleteFlag = true;
      });
      this.hot.deselectCell();
      tableData.splice(newDestination, 0, ...list);
      const filterTableData = tableData.filter(i => !i.deleteFlag);
      const rowIndexs = filterTableData
        .map((item, index) => {
          return item.selectRow ? index : -1;
        })
        .filter(index => index !== -1);
      filterTableData.forEach(item => {
        item.selectRow = null;
        item.deleteFlag = null;
      });
      this.tableData = filterTableData;
      this.initTable();
      await this.$nextTick();
      this.hot.selectRows(...rowIndexs);
    },
    scrollEvent() {
      this.tableScrollLeft = !!this.scrollDom.scrollLeft;
    },
    addScrollEvent() {
      if (this.scrollDom) {
        this.scrollDom.removeEventListener("scroll", this.scrollEvent);
        this.scrollDom = null;
      }
      const scrollDom = this.$refs.projectConfig_handsontable
        .getElementsByClassName("ht_master")[0]
        .getElementsByClassName("wtHolder")[0];
      this.scrollDom = scrollDom;
      scrollDom.addEventListener("scroll", this.scrollEvent);
    },
    // 清除已删除的数据
    async clearDeleteData() {
      this.tableData = this.tableData.filter(i => !i.deleteStatus);
      this.hot.loadData(this.tableData);
      await this.$nextTick();
      setTimeout(() => {
        const filtersPlugin = this.hot.getPlugin("filters");
        filtersPlugin.filter();
      }, 300);
    }
  },
  mounted() {
    this.initTable();
    this.resizeObserver = new ResizeObserver(
      this._.debounce(() => {
        this.initTable();
      }, 300)
    );
    this.resizeObserver.observe(this.$refs.projectConfig_handsontable);
  },
  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(this.$refs.projectConfig_handsontable);
    }
    this.scrollDom.removeEventListener("scroll", this.scrollEvent);
  }
};
</script>

<style lang="scss" scoped>
#projectConfig_handsontable :deep() {
  .complete {
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: var(--Sta1); /* 自定义颜色 */
      opacity: 0.1; /* 透明度 0-1 */
    }
  }
  .notGenerated {
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: var(--Sta2); /* 自定义颜色 */
      opacity: 0.1; /* 透明度 0-1 */
    }
  }
}
#projectConfig_handsontable :deep() .handsontable th {
  text-align: left;
}
.table-border {
  border: 1px solid var(--B1);
  border-top: none;
}
#projectConfig_handsontable :deep() tr td:first-child {
  border-left: none;
}
#projectConfig_handsontable :deep() .ht_master.handsontable tr td:last-child {
  border-right: none;
}
#projectConfig_handsontable :deep() tr th:first-child {
  border-left: none;
}
#projectConfig_handsontable
  :deep()
  .ht_clone_top.handsontable
  tr
  th:last-child {
  border-right: none;
}
#projectConfig_handsontable :deep() table thead tr th {
  height: 30px;
  line-height: 30px;
  background-color: var(--BG);
  color: var(--T1);
  border-color: var(--B1);
}
#projectConfig_handsontable :deep() table tbody tr th {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}

#projectConfig_handsontable :deep() table tbody tr td {
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}
#projectConfig_handsontable :deep() .handsontableHandle {
  color: var(--Sta3);
}
#projectConfig_handsontable :deep() .required {
  color: var(--Sta3);
}
#projectConfig_handsontable :deep() .autocompleteEditor.handsontable {
  padding-right: 0;
}

#projectConfig_handsontable :deep(table thead tr th) {
  height: 30px;
  line-height: 30px;
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}
#projectConfig_handsontable :deep(.handsontable th) {
  text-align: left;
}
#projectConfig_handsontable :deep(table tbody tr th) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}

#projectConfig_handsontable :deep(table tbody tr td) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}

#projectConfig_handsontable :deep(table thead tr th .changeType) {
  margin-top: 8px;
}
#projectConfig_handsontable :deep(table thead tr th .colHeader) {
  font-weight: 700;
}
#projectConfig_handsontable :deep(.handsontable .changeType) {
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}
#projectConfig_handsontable :deep(.handsontable .changeType:hover) {
  background-color: var(--BG1);
  color: var(--T1);
  border-color: var(--B1);
}
#projectConfig_handsontable :deep(.handsontableHandle) {
  color: var(--ZS);
}

#projectConfig_handsontable
  :deep(.ht_clone_top_left_corner.handsontable .htCore tr > th:nth-child(2)) {
  .changeType {
    display: none;
  }
  span.colHeader.columnSorting:before {
    display: none;
  }
}

#projectConfig_handsontable :deep() {
  .handsontable span.colHeader.columnSorting:before {
    top: 8px;
    width: 14px;
    height: 14px;
    padding-left: 0;
    transform: translate(6px, 0px);
    background-image: url("../assets/initial.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.descending:before {
    background-image: url("../assets/descending.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.ascending:before {
    background-image: url("../assets/ascending.png");
    background-size: 100% 100%;
  }
}

#projectConfig_handsontable.light :deep() {
  .handsontable span.colHeader.columnSorting:before {
    background-image: url("../assets/initial_light.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.descending:before {
    background-image: url("../assets/descending_light.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.ascending:before {
    background-image: url("../assets/ascending_light.png");
    background-size: 100% 100%;
  }
}

#projectConfig_handsontable.boxShadow :deep() {
  .ht_clone_left.handsontable .wtHolder .wtHider .wtSpreader .htCore tbody {
    box-shadow: var(--S3);
  }
}
#projectConfig_handsontable :deep() .handsontable tr {
  background-color: var(--BG1);
}
#projectConfig_handsontable :deep() .columnSorting.sortAction:hover {
  text-decoration: initial;
}

#projectConfig_handsontable :deep(.ht_clone_left.handsontable) {
  padding-right: 10px;
}
.checkedAll {
  position: absolute;
  z-index: 111;
  left: 55px;
  top: 8px;
}
</style>
<style lang="scss">
#hot-display-license-info {
  display: none;
}
.handsontable .ht_master table td.htCustomMenuRenderer {
  background-color: var(--BG);
}
.htFiltersConditionsMenu table tbody tr td {
  background-color: var(--BG);
}
.htDropdownMenu table tbody tr td {
  background-color: var(--BG1);
}
.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {
  background-color: var(--BG2);
}
.handsontable .htUISelectCaption {
  background-color: var(--BG4);
}
.handsontable .htUIInput.htUIButtonOK input {
  background-color: var(--ZS);
}

.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody {
  & > tr:nth-child(1),
  & > tr:nth-child(2),
  & > tr:nth-child(3),
  & > tr:nth-child(4),
  & > tr:nth-child(5),
  & > tr:nth-child(6),
  & > tr:nth-child(7),
  & > tr:nth-child(8),
  & > tr:nth-child(9),
  & > tr:nth-child(10) {
    display: none;
  }
}
ul.zTreeDragUL {
  position: fixed;
  list-style: none;
  padding: 4px;
  background-color: var(--BG4);
}
</style>
