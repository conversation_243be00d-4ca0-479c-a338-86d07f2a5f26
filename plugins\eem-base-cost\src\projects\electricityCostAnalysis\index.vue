<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full flex flex-col">
        <customElSelect
          v-model="ElSelect_treeType.value"
          v-bind="ElSelect_treeType"
          v-on="ElSelect_treeType.event"
          :prefix_in="$T('节点树类型')"
          v-if="multidimensional"
          class="mb-J3"
        >
          <ElOption
            v-for="item in ElOption_treeType.options_in"
            :key="item[ElOption_treeType.key]"
            :label="item[ElOption_treeType.label]"
            :value="item[ElOption_treeType.value]"
            :disabled="item[ElOption_treeType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto cetTree"
          :selectNode.sync="CetTree_leftTree.selectNode"
          :checkedNodes.sync="CetTree_leftTree.checkedNodes"
          :searchText_in.sync="CetTree_leftTree.searchText_in"
          v-bind="CetTree_leftTree"
          v-on="CetTree_leftTree.event"
        >
          <span
            class="custom-tree-node el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
            <div class="icon" v-if="node?.data?.changeStatus">
              <el-tooltip effect="light" :content="effTimeFormat(node.data)">
                <omega-icon symbolId="collect-lin" />
              </el-tooltip>
            </div>
          </span>
        </CetTree>
      </div>
    </template>
    <template #container>
      <div class="fullheight flex flex-col">
        <div class="mb-J3 flex flex-row items-center">
          <customElSelect
            class="mr-J1"
            v-model="ElSelect_scheme.value"
            v-bind="ElSelect_scheme"
            v-on="ElSelect_scheme.event"
            :prefix_in="$T('分时方案')"
          >
            <ElOption
              v-for="item in ElOption_scheme.options_in"
              :key="item[ElOption_scheme.key]"
              :label="item[ElOption_scheme.label]"
              :value="item[ElOption_scheme.value]"
              :disabled="item[ElOption_scheme.disabled]"
            ></ElOption>
          </customElSelect>
          <time-tool
            :typeID="14"
            :val.sync="startTime"
            @change="changeQueryTime"
            :timeType_in="timeType"
            :shortcuts="pickerOptions.shortcuts"
          ></time-tool>
        </div>
        <div class="flex-auto flex flex-col">
          <div class="chartBox flex flex-col h-[385px] bg-BG1 rounded-Ra p-J4">
            <div class="mb-J3 text-H3 font-bold">
              {{ $T("用电成本和平均电价趋势") }}
            </div>
            <CetChart
              v-bind="CetChart_electrictyCost"
              class="flex-auto"
            ></CetChart>
            <span class="avgCost" v-if="ElSelect_scheme.value">
              <el-tooltip :content="toolTipContent">
                <i class="el-icon-question mr-J0 text-ZS"></i>
              </el-tooltip>
              {{ $T("平均电价") }}
              <span>({{ avgUnit }})</span>
            </span>
          </div>
          <div class="flex-row flex h-[300px] mt-J3">
            <div class="flex flex-col flex-[1] bg-BG1 p-J4 rounded-Ra">
              <div class="mb-J3 text-H3 font-bold">
                {{ $T("平均电价分析") }}
              </div>
              <div class="flex-auto proportion-item avg-cost flex flex-col">
                <div class="cost-item mb-J1">
                  <span>{{ $T("时间") }}:</span>
                  &emsp;
                  <span>{{ selectTime }}</span>
                </div>
                <div class="cost-item mb-J1">
                  <span>{{ $T("平均电价") }}:</span>
                  &emsp;
                  <span>
                    {{
                      formatNumberWithPrecision(electrictyCostView.avgCost, 2)
                    }}
                  </span>
                  <span>{{ electrictyCostView.unit }}</span>
                </div>
                <div class="flex-auto compare flex flex-col">
                  <div class="flex flex-row">
                    <el-tooltip placement="top" :content="tbName">
                      <div
                        class="preText flex-auto text-ellipsis p-J0 text-T5 bg-BG2"
                        :class="{
                          'mr-J1': queryTime.aggregationCycle === 14
                        }"
                      >
                        {{ tbName }}
                      </div>
                    </el-tooltip>

                    <span
                      v-if="queryTime.aggregationCycle === 14"
                      class="lastText ml-J1 flex-auto p-J0 text-T5 bg-BG2"
                    >
                      {{ hbName }}
                    </span>
                  </div>
                  <div class="flex-auto flex flex-row">
                    <div
                      class="flex-auto relative"
                      :class="{
                        'mr-J1': queryTime.aggregationCycle === 14
                      }"
                    >
                      <CetChart v-bind="CetChart_avgcost1"></CetChart>
                      <div class="label">
                        <el-tooltip
                          :content="
                            _.isEmpty(tbLabel)
                              ? ''
                              : tbLabel.price + tbLabel.unit
                          "
                          effect="light"
                        >
                          <div class="price text-ellipsis">
                            <span>{{ tbLabel.price || "--" }}</span>
                          </div>
                        </el-tooltip>
                        <div class="percent">
                          <el-tooltip :content="tbLabel.percent" effect="light">
                            <span class="text-ellipsis">
                              {{ tbLabel.percent || "--" }}
                            </span>
                          </el-tooltip>
                          <img :src="tbLabel.src" v-if="tbLabel.src" alt="" />
                        </div>
                      </div>
                    </div>
                    <div
                      class="flex-auto ml-J1 relative"
                      v-if="queryTime.aggregationCycle === 14"
                    >
                      <CetChart v-bind="CetChart_avgcost2"></CetChart>
                      <div class="label">
                        <el-tooltip
                          :content="
                            _.isEmpty(hbLabel)
                              ? ''
                              : hbLabel.price + hbLabel.unit
                          "
                          effect="light"
                        >
                          <div class="price text-ellipsis">
                            <span>{{ hbLabel.price || "--" }}</span>
                          </div>
                        </el-tooltip>
                        <div class="percent">
                          <el-tooltip :content="hbLabel.percent" effect="light">
                            <span class="text-ellipsis">
                              {{ hbLabel.percent || "--" }}
                            </span>
                          </el-tooltip>
                          <img :src="hbLabel.src" v-if="hbLabel.src" alt="" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex flex-col ml-J3 flex-[2] bg-BG1 p-J4 rounded-Ra">
              <div class="mb-J3 text-H3 font-bold">
                {{ $T("分时用电成本") }}
              </div>
              <div class="flex-auto proportion-item flex flex-col">
                <div class="cost-item mb-J2">
                  <span>{{ $T("时间") }}:</span>
                  &emsp;
                  <span>{{ selectTime }}</span>
                </div>
                <div class="tsBox flex-auto flex">
                  <CetTable
                    class="flex-auto"
                    :data.sync="CetTable_cost.data"
                    :dynamicInput.sync="CetTable_cost.dynamicInput"
                    v-bind="CetTable_cost"
                    v-on="CetTable_cost.event"
                  >
                    <template v-for="item in columnArr">
                      <ElTableColumn
                        :key="item.label"
                        v-bind="item"
                      ></ElTableColumn>
                    </template>
                  </CetTable>
                  <div class="tsChart ml-J3 w-[360px]">
                    <CetChart v-bind="CetChart_timecost"></CetChart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CetAside>
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import TimeTool from "@/components/TimeTool.vue";
import omegaI18n from "@omega/i18n";

export default {
  name: "electricityCostAnalysis",
  components: {
    TimeTool
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    language() {
      return omegaI18n.locale === "en";
    },
    tbName() {
      if (this.queryTime.aggregationCycle === 17) {
        return $T("上年成本");
      } else if (this.queryTime.aggregationCycle === 14) {
        return $T("上年同月成本");
      }
      return $T("同比");
    },
    hbName() {
      if (this.queryTime.aggregationCycle === 14) {
        return $T("上月成本");
      }
      return $T("同比");
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    }
  },
  data() {
    return {
      CetTree_leftTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_leftTree_currentNode_out
        }
      },
      currentNode: null, // 当前点击树节点
      ElSelect_treeType: {
        value: -1,
        style: {},
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_scheme: {
        value: "",
        style: {
          width: "250px"
        },
        size: "small",
        event: {
          change: this.ElSelect_scheme_change_out
        }
      },
      ElOption_scheme: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      startTime: new Date().getTime(),
      queryTime: {},
      timeType: [
        {
          type: "month",
          text: $T("月"),
          typeID: 14,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          typeID: 17,
          number: 1,
          unit: "y"
        }
      ],
      // electrictyCost组件
      CetChart_electrictyCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      // 分时用电成本
      CetChart_timecost: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            confine: true,
            trigger: "item",
            extraCssText: "white-space: normal; word-break: break-all",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              type: "pie",
              radius: "65%",
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      },
      electrictyCostView: {
        avgCost: null, // 平均电价值
        unit: "" // 平均电价单位
      }, // 平均电价
      tbLabel: {}, // 同比数据
      hbLabel: {}, // 环比数据
      // 同比
      CetChart_avgcost1: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 环比
      CetChart_avgcost2: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      selectTime: "",
      // cost表格组件
      CetTable_cost: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      columnsUnit: "",
      Columns_cost: [
        {
          prop: "identification", // 支持path a[0].b
          label: $T("时段"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "64" //绝对宽度
        },
        {
          prop: "value", // 支持path a[0].b
          "render-header": h => {
            let text = $T("实际成本");
            if (this.queryTime.aggregationCycle === 17) {
              text = $T("当年成本");
            } else if (this.queryTime.aggregationCycle === 14) {
              text = $T("当月成本");
            }
            return h("span", text + this.columnsUnit);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatNumberCol(),
          minWidth: "120"
        },
        {
          prop: "tbValue", // 支持path a[0].b
          "render-header": h => {
            let text = $T("同比");
            if (this.queryTime.aggregationCycle === 17) {
              text = $T("上年成本");
            } else if (this.queryTime.aggregationCycle === 14) {
              text = $T("上年同月成本");
            }
            return h("span", text + this.columnsUnit);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatNumberCol(),
          width: "150"
        },
        {
          prop: "hbValue", // 支持path a[0].b
          "render-header": h => {
            let text = $T("同比");
            if (this.queryTime.aggregationCycle === 14) {
              text = $T("上月成本");
            }
            return h("span", text + this.columnsUnit);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatNumberCol(),
          width: "120"
        }
      ],
      columnArr: [], // 表格列
      avgUnit: "",
      pickerOptions: {
        // 添加回到今天的快捷键
        shortcuts: [
          {
            text: this.$T("当月"),
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          }
        ]
      },
      toolTipContent: "" // 平均电价计算提示框内容
    };
  },
  watch: {},
  methods: {
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const childSelectState = this._.get(node, "data.childSelectState", null);
      const correlationState = this._.get(node, "data.correlationState", null);
      if (childSelectState === 2 || correlationState === 2) {
        return "#989898";
      }
      return;
    },

    formatNumberWithPrecision: common.formatNumberWithPrecision,
    ElSelect_scheme_change_out() {
      this.getAllData();
    },
    ElSelect_treeType_change_out(val) {
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    // leftTree 输出
    CetTree_leftTree_currentNode_out: _.debounce(function (val) {
      if (!val) return;
      if (val.childSelectState === 2 && val.correlationState === 2) {
        return this.$message.warning(
          $T("没有该节点权限，该节点没有关联当前方案")
        );
      } else if (val.childSelectState === 1 && val.correlationState === 2) {
        return this.$message.warning($T("当前节点未关联成本核算方案！"));
      } else if (val.childSelectState === 2 && val.correlationState === 1) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.currentNode = this._.cloneDeep(val);
      const params = {
        objectId: val.id,
        objectLabel: val.modelLabel
      };
      customApi.queryTimeShareList(params).then(res => {
        if (res.code === 0) {
          this.ElOption_scheme.options_in = res.data;
          this.ElSelect_scheme.value = res.data[0] && res.data[0].id;
          this.getAllData();
        }
      });
    }),
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.queryTime = {
        aggregationCycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const yearStr = this.language ? "YYYY" : "YYYY年";
      this.selectTime =
        timeOption.typeID === 14
          ? date.format(mothStr)
          : timeOption.typeID === 17
          ? date.format(yearStr)
          : "--";
      if (this.queryTime.aggregationCycle === 14) {
        this.toolTipContent = $T("日峰平谷电度电费之和/日峰平谷总电量");
        this.pickerOptions.shortcuts[0].text = this.$T("当月");
      } else if (this.queryTime.aggregationCycle === 17) {
        this.toolTipContent = $T("月峰平谷电度电费之和/月峰平谷总电量");
        this.pickerOptions.shortcuts[0].text = this.$T("今年");
      }
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true);
      } else {
        this.getAllData();
      }
    },
    // 获取节点树类型下拉框
    async queryTreeType() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (
        res?.data || [
          {
            id: -1,
            name: $T("固定管理层级")
          }
        ]
      ).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    getTreeData1() {
      this.CetTree_leftTree.inputData_in = [];
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype",
                  tagid: 1
                }
              ]
            },
            modelLabel: "room"
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "airconditioner" },
          { modelLabel: "virtualbuildingnode" },
          { modelLabel: "virtualdevicenode" }
        ],
        treeReturnEnable: true
      };
      customApi.queryElectricityTree(data).then(res => {
        if (res.code === 0) {
          this.CetTree_leftTree.inputData_in = res.data;
          // 选中第一个有数据 childSelectState = 1 的节点并展开节点
          const obj = this._.find(this.dataTransform(res.data), {
            childSelectState: 1,
            correlationState: 1
          });
          this.CetTree_leftTree.selectNode = obj;
        }
      });
    },
    // 获取维度节点树数据
    async getTreeData2(keepSelectNode) {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        feeRateType: 2,
        feeSubRateType: 2,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        aggregationCycle: this.queryTime.aggregationCycle
      };
      const res = await customApi.queryCostcheckplanMultitree(queryData);
      this.CetTree_leftTree.inputData_in = res?.data || [];
      let obj;
      if (keepSelectNode) {
        obj = this._.find(this.dataTransform(res.data), [
          "tree_id",
          this.currentNode.tree_id
        ]);
      }
      if (!obj) {
        // 选中第一个有数据 childSelectState = 1 的节点并展开节点
        obj = this._.find(this.dataTransform(res.data), {
          childSelectState: 1,
          correlationState: 1
        });
      }
      this.CetTree_leftTree.selectNode = obj;
    },
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    getAllData() {
      this.CetChart_electrictyCost.options = {};
      this.electrictyCostView = {};
      this.tbLabel = {};
      this.hbLabel = {};
      this.columnArr = [];
      this.CetTable_cost.data = [];
      this.CetChart_avgcost1.options.series[0].data = [];
      this.CetChart_avgcost2.options.series[0].data = [];
      this.CetChart_timecost.options.series[0].data = [];
      if (!this.currentNode || !this.ElSelect_scheme.value) return;
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        energyType: 2,
        timeShareId: this.ElSelect_scheme.value,
        ...this.queryTime,
        dimConfigId: this.ElSelect_treeType.value
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        params.timeRanges = this.currentNode.effTimeList;
      }
      this.getElectricityCostValueTrend(params);
      this.getAverageElectricityPrice(params);
      this.getTsObjectCost(params);
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    // 用电成本趋势tooltip格式化
    formatTooltip(params) {
      if (!params[0].data.time) return;
      const cycle = this.queryTime.aggregationCycle;
      const formatStr =
        cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
      let str = this.$moment(params[0].data.time).format(formatStr) + "<br />";
      params.forEach(item => {
        const hideTooltip = item.data.hideTooltip;
        if (!hideTooltip) {
          str +=
            item.marker +
            item.seriesName +
            ": " +
            (item.data.value || item.data.value === 0
              ? Number(item.data.value).toFixed(2)
              : "--") +
            `(${item.data.unit})` +
            "<br />";
        }
      });
      return str;
    },
    getElectricityCostValueTrend(params) {
      const _this = this;
      const cycle = this.queryTime.aggregationCycle;
      customApi.queryElectricityCostValueTrend(params).then(res => {
        if (res.code === 0 && res.data.avgCostData.length) {
          // 处理x轴
          const xAxisData = [];
          // 处理series数据
          var series = [];
          let unit = ""; // 峰平谷单位
          this.avgUnit = ""; // 平均电价单位

          const timeAndTsIds = res.data?.timeAndTsIds ?? [];

          // tsCostData  分时 - 棒图
          res.data.tsCostData.forEach(data => {
            data.forEach(item => {
              const timeAndTsId = timeAndTsIds.find(
                tsIdItem => tsIdItem.logTime === item.time
              );
              const tsIds = timeAndTsId?.tsIds ?? [];

              const hideTooltip =
                item.identification && !tsIds.includes(item.identification);
              item.hideTooltip = hideTooltip;
            });
            series.push({
              name: data[0].identification,
              type: "bar",
              stack: $T("电"),
              barMaxWidth: 30,
              data
            });
          });

          // avgCostData  平均电价 - 折线图
          res.data.avgCostData.forEach(item => {
            xAxisData.push(
              this.getAxixs(item.time, this.queryTime.aggregationCycle)
            );
          });
          series.push({
            name: res.data.avgCostData[0].identification,
            type: "line",
            yAxisIndex: 1,
            data: res.data.avgCostData,
            markPoint: {
              data: [
                {
                  type: "max",
                  symbolSize: 30,
                  name: $T("平均电价最大值"),
                  label: {
                    formatter(params) {
                      return (
                        params.name +
                        ": " +
                        _this.formatNumberWithPrecision(params.value, 2)
                      );
                    },
                    position: "top",
                    padding: 8,
                    // backgroundColor: "rgba(38, 41, 56, 0.8)",
                    borderRadius: 4
                  }
                },
                {
                  type: "min",
                  symbolSize: 30,
                  name: $T("平均电价最小值"),
                  label: {
                    formatter(params) {
                      return (
                        params.name +
                        ": " +
                        _this.formatNumberWithPrecision(params.value, 2)
                      );
                    },
                    position: "top",
                    padding: 8,
                    // backgroundColor: "rgba(38, 41, 56, 0.8)",
                    borderRadius: 4
                  }
                }
              ]
            },
            smooth: true
          });
          unit = res.data.avgCostData[0].unit;
          this.avgUnit = unit;

          this.CetChart_electrictyCost.options = {
            toolbox: {
              top: -10,
              right: 30,
              feature: {
                saveAsImage: {}
              }
            },
            tooltip: {
              trigger: "axis",
              formatter(params) {
                return _this.formatTooltip(params);
              }
            },
            legend: {
              top: 0
            },
            grid: {
              top: "70",
              left: 16,
              right: this.language ? 60 : 40,
              bottom: "3%",
              containLabel: true
            },
            xAxis: {
              type: "category",
              name: cycle === 14 ? $T("天数") : $T("月份"),
              nameLocation: "end",
              data: xAxisData,
              axisPointer: {
                type: "shadow"
              },
              nameTextStyle: {
                padding: [10, 0, 0, 0],
                verticalAlign: "top"
              }
            },
            yAxis: [
              {
                type: "value",
                name: unit ? $T("成本({0})", unit) : "--",
                nameTextStyle: {
                  align: "left"
                }
              },
              {
                type: "value",
                name: this.avgUnit ? $T("平均电价({0})", this.avgUnit) : "--",
                scale: true,
                nameTextStyle: {
                  align: "left"
                  // verticalAlign: "middle"
                }
              }
            ],
            series
          };
        }
      });
    },
    // 平均电价分析
    getAverageElectricityPrice(params) {
      customApi.queryAverageElectricityPrice(params).then(res => {
        if (res.code === 0) {
          this.electrictyCostView = {
            avgCost: res.data.unitCostValue,
            unit: res.data.unit
          };
          // 同比
          const tbRate = Math.abs(res.data.tb);
          if (tbRate > 1) {
            this.CetChart_avgcost1.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost1.options.series[0].data = [
              tbRate,
              1 - tbRate
            ];
          }
          this.tbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueTb, 2) ||
              "--",
            percent:
              res.data.tb || res.data.tb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.tb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.tb > 0
                ? require("./assets/arrow_up.png")
                : res.data.tb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };

          // 环比
          const hbRate = Math.abs(res.data.hb);
          if (hbRate > 1) {
            this.CetChart_avgcost2.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost2.options.series[0].data = [
              hbRate,
              1 - hbRate
            ];
          }
          this.hbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueHb, 2) ||
              "--",
            percent:
              res.data.hb || res.data.hb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.hb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.hb > 0
                ? require("./assets/arrow_up.png")
                : res.data.hb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };
        }
      });
    },
    // 分时用电成本
    async getTsObjectCost(params) {
      const res = await customApi.queryTsObjectCost(params);
      if (res.code !== 0) {
        return;
      }
      // 表格头加单位
      let arr = [];
      if (this.queryTime.aggregationCycle === 14) {
        arr = this._.cloneDeep(this.Columns_cost);
      } else if (this.queryTime.aggregationCycle === 17) {
        arr = this._.dropRight(this._.cloneDeep(this.Columns_cost));
      }
      const unit =
        res.data && res.data.length ? "(" + res.data[0].unit + ")" : "";
      this.columnsUnit = unit;
      this.columnArr = arr;

      this.CetTable_cost.data = this._.cloneDeep(res.data);
      this.CetChart_timecost.options.series[0].data = [];
      res.data.forEach(item => {
        this.CetChart_timecost.options.series[0].data.push({
          value: item.value,
          name: item.identification,
          unit: item.unit
        });
      });
    },
    effTimeFormat(data) {
      const { effTimeList } = data;
      return effTimeList
        .map(item => {
          const { startTime, endTime } = item;
          return `${this.$moment(startTime).format(
            "YYYY-MM-DD"
          )}~${this.$moment(endTime).format("YYYY-MM-DD")}`;
        })
        .join(",");
    }
  },
  mounted() {
    this.queryTreeType();
  }
};
</script>

<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  padding: 0;
}
.chartBox {
  position: relative;
  .avgCost {
    position: absolute;
    top: 92px;
    right: 22px;
    font-size: 12px;
    height: 24px;
    @include background_color(BG1);
  }
}
.proportion-item {
  .cost-item {
    text-align: center;
    height: 28px;
    line-height: 28px;
    @include font_color(T5);
    @include background(BG2);
    span {
      font-size: 16px;
    }
  }
}
.avg-cost {
  .label {
    width: 60px;
    height: 50px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    .price,
    .percent {
      span {
        display: inline-block;
        max-width: calc(100% - 30px);
      }
    }
    .percent {
      img {
        width: 14px;
      }
    }
  }
}
.cetTree {
  .custom-tree-node {
    .icon {
      visibility: hidden;
      position: absolute;
      right: 0;
      top: 4px;
      @include background_color(BG1);
      .edit {
        @include font_color(ZS);
      }
      .delete {
        @include font_color(Sta3);
      }
    }
  }
  :deep(.el-tree-node__content) {
    position: relative;
    &:hover {
      .custom-tree-node .icon {
        visibility: visible;
      }
    }
  }
}
</style>
