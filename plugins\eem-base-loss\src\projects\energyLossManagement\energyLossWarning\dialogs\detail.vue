<template>
  <div class="h-full flex flex-col">
    <CetButton
      class="mb-J3"
      v-bind="CetButton_back"
      v-on="CetButton_back.event"
    ></CetButton>
    <div class="flex-auto overflow-auto">
      <div class="font-bold mb-J1">{{ $T("基本信息") }}</div>
      <div
        class="tableBox mb-J3"
        :style="{ height: language ? '85px' : '81px' }"
      >
        <CetTable
          :data.sync="CetTable_2.data"
          :dynamicInput.sync="CetTable_2.dynamicInput"
          v-bind="CetTable_2"
          v-on="CetTable_2.event"
        >
          <template v-for="item in Columns_2">
            <ElTableColumn
              :key="item.label"
              v-bind="item"
              headerAlign="left"
              align="left"
              showOverflowTooltip
            ></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_handleStatus">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row[ElTableColumn_handleStatus.prop] === 3
                    ? 'text-Sta1'
                    : 'text-Sta2'
                "
              >
                {{
                  scope.row[ElTableColumn_handleStatus.prop] === 3
                    ? $T("已处理")
                    : $T("待处理")
                }}
              </span>
            </template>
          </ElTableColumn>
          <template v-for="item in Columns_3">
            <ElTableColumn
              :key="item.label"
              v-bind="item"
              headerAlign="left"
              align="left"
              showOverflowTooltip
            ></ElTableColumn>
          </template>
        </CetTable>
      </div>
      <div class="font-bold mb-J1">{{ $T("损耗分析") }}</div>
      <div
        class="tableBox mb-J3"
        :style="{ height: language ? '85px' : '81px' }"
      >
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_status">
            <template slot-scope="scope">
              <span
                :class="
                  ElTableColumn_status.formatter(
                    scope.row,
                    ElTableColumn_status,
                    scope.row[ElTableColumn_status.prop]
                  ).styleClass
                "
              >
                {{
                  ElTableColumn_status.formatter(
                    scope.row,
                    ElTableColumn_status,
                    scope.row[ElTableColumn_status.prop]
                  ).text
                }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <Diagram class="flex-auto" v-bind="diagram" />
    </div>
  </div>
</template>
<script>
import Diagram from "../../components/diagram.vue";
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import omegaI18n from "@omega/i18n";
const language = omegaI18n.locale === "en";
export default {
  name: "energyLossWarningDetail",
  components: {
    Diagram
  },
  props: {
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    energytype_in: {
      type: Number
    }
  },
  computed: {
    language() {
      return omegaI18n.locale === "en";
    }
  },
  data(vm) {
    return {
      unit: "--",
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_1: [
        {
          prop: "lossLevel", // 支持path a[0].b
          label: $T("损耗级别"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            return `${cellValue + 1} ${$T("级损耗")}`;
          }
        },
        {
          prop: "roomName", // 支持path a[0].b
          label: $T("所属房间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "150", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "value", // 支持path a[0].b
          "render-header": h => {
            return h("span", $T("设备能耗") + `(${vm.unit})`);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "320" : "150", //该宽度会自适应
          formatter: common.formatNumberCol(2)
        },
        {
          prop: "endLoopCount", // 支持path a[0].b
          label: $T("下级设备数量(个)"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "380" : "150", //该宽度会自适应
          formatter: common.formatNumberCol(0)
        },
        {
          prop: "endDevInfos", // 支持path a[0].b
          label: $T("下级设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "300" : "150", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (cellValue && cellValue.length) {
              if (cellValue.length === 1) {
                return cellValue[0].endDevName;
              }
              return `${cellValue[0].endDevName} ${$T("等")}`;
            }
            return "--";
          }
        },
        {
          prop: "endLoopValue", // 支持path a[0].b
          "render-header": h => {
            return h("span", $T("下级设备能耗之和") + `(${vm.unit})`);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "460" : "200", //该宽度会自适应
          formatter: common.formatNumberCol(2)
        },
        {
          prop: "loss", // 支持path a[0].b
          "render-header": h => {
            return h("span", $T("损耗量") + `(${vm.unit})`);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "150", //该宽度会自适应
          formatter: common.formatNumberCol(2)
        },
        {
          prop: "lossRate", // 支持path a[0].b
          label: $T("损耗率") + "(%)", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "150", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (cellValue || cellValue === 0) {
              return `${(cellValue * 100).toFixed2(2)}`;
            }
            return "--";
          }
        }
      ],
      ElTableColumn_status: {
        prop: "status", // 支持path a[0].b
        label: $T("损耗评估"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "180" : "100", //该宽度会自适应
        formatter: (row, column, cellValue) => {
          let styleClass = "",
            text = "";
          if (row.loss < 0 || row.lossRate < 0) {
            text = "--";
            styleClass = "text-Sta3";
          } else if (cellValue) {
            text = $T("正常");
            styleClass = "text-Sta1";
          } else {
            text = $T("异常");
            styleClass = "text-Sta3";
          }
          return {
            styleClass,
            text
          };
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_2: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          width: "70" //绝对宽度
        },
        {
          prop: "logTime", // 支持path a[0].b
          label: $T("时间"), //列名
          width: "200", //该宽度会自适应
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          prop: "$energytype", // 支持path a[0].b
          label: $T("能源类型"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "$type", // 支持path a[0].b
          label: $T("报警类型"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "lossLevel", // 支持path a[0].b
          label: $T("损耗级别"), //列名
          minWidth: "100", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            return `${cellValue + 1} ` + $T("级损耗");
          }
        },
        {
          prop: "roomName", // 支持path a[0].b
          label: $T("房间"), //列名
          minWidth: "100", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "objectName", // 支持path a[0].b
          label: $T("设备"), //列名
          minWidth: "100", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "description", // 支持path a[0].b
          label: $T("描述"), //列名
          minWidth: "220", //该宽度会自适应
          formatter: common.formatTextCol()
        }
      ],
      Columns_3: [
        {
          prop: "operatorName", // 支持path a[0].b
          label: $T("处理人"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "remark", // 支持path a[0].b
          label: $T("处理结果"), //列名
          minWidth: language ? "220" : "100", //该宽度会自适应
          formatter: common.formatTextCol()
        }
      ],
      ElTableColumn_handleStatus: {
        prop: "status", // 支持path a[0].b
        label: $T("处理状态"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "200" : "110" //该宽度会自适应
      },
      diagram: {
        initTrigger_in: new Date().getTime(),
        unit_in: "",
        query_in: null
      },
      query_in: {
        aggregationCycle: null,
        endTime: null,
        startTime: null
      },
      CetButton_back: {
        visible_in: true,
        disable_in: false,
        title: $T("返回"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.back
        }
      }
    };
  },

  methods: {
    init() {
      let startTime = this._.get(this.inputData_in, "logTime"),
        cycle = this._.get(this.inputData_in, "cycle") || 12,
        cycleObj = {
          1: { value: 1, type: "s" },
          2: { value: 3, type: "s" },
          3: { value: 1, type: "m" },
          4: { value: 5, type: "m" },
          5: { value: 10, type: "m" },
          6: { value: 30, type: "m" },
          7: { value: 1, type: "h" },
          8: { value: 2, type: "h" },
          9: { value: 3, type: "h" },
          10: { value: 6, type: "h" },
          11: { value: 12, type: "h" },
          12: { value: 1, type: "d" },
          13: { value: 7, type: "d" },
          14: { value: 1, type: "M" },
          15: { value: 3, type: "M" },
          16: { value: 6, type: "M" },
          17: { value: 1, type: "y" },
          18: { value: 12, type: "m" }
        },
        endTime;
      if (cycle) {
        endTime = this.$moment(startTime)
          .add(cycleObj[cycle].value, cycleObj[cycle].type)
          .valueOf();
      } else {
        console.error("时间入参不全");
      }
      this.query_in = {
        aggregationCycle: cycle,
        endTime: endTime,
        startTime: startTime
      };
      this.CetTable_2.data = [this.inputData_in];
      this.getData();
      this.getUnit();
    },
    async getData() {
      let queryData = {
        modelLabel: this.inputData_in.objectLabel,
        id: this.inputData_in.objectId,
        energyType: this.energytype_in
      };
      Object.assign(queryData, this.query_in);
      const res = await customApi.lossConfigDeviceLossAnalysis(queryData, {
        keepTransformer: false
      });
      if (res.code !== 0) {
        return;
      }

      const tableData = res.data?.nodeList ?? [];
      const obj = tableData.find(
        item =>
          item.id === this.inputData_in.objectId &&
          item.modelLabel === this.inputData_in.objectLabel
      );
      if (obj) {
        obj.lossLevel = this.inputData_in.lossLevel;
        this.CetTable_1.data = [obj];
      } else {
        this.CetTable_1.data = [];
      }
    },
    getUnit() {
      // 查询基本单位
      const params = {
        projectUnitClassify: 1
      };
      const data = [this.energytype_in];
      customApi.getDefaultUnitSetting(data, params).then(res => {
        if (res.code === 0) {
          this.unit = common.formatSymbol(res.data?.[0]?.uniten || "--");
          this.diagram.unit_in = this.unit;
          let query_in = {
            energyType: this.energytype_in,
            id: this.inputData_in.objectId,
            modelLabel: this.inputData_in.objectLabel
          };
          Object.assign(query_in, this.query_in);
          this.diagram.query_in = query_in;
          this.diagram.initTrigger_in = new Date().getTime();
        }
      });
    },
    back() {
      this.$emit("back");
    }
  },
  mounted() {
    this.init();
  }
};
</script>
