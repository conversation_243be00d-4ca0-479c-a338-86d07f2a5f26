import fetch from "eem-base/utils/fetch";

// 查询维度节点树列表（包含id为-1的固定管理层级）
export function getAttributeDimensionTreeNodeConfig(data, params) {
  return fetch({
    url: `/eem-service/v1/attribute-dimension/tree/node-config`,
    method: "POST",
    data,
    params
  });
}

/**
 * 获取多维度节点树 可支持传入末端需要保留的节点
 * @param {*keepNodeTypes} array 传入末端保留的节点的modelLabel，默认不过滤
 * @returns
 */
export function dimensionTreeFilterByEnergytype(data) {
  return fetch({
    url: `/eem-service/v1/attribute-dimension/tree/filter-by-energytype`,
    method: "POST",
    data
  });
}

export function getNodeTreeSimple(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree/simple`,
    method: "POST",
    data
  });
}

export function lossConfigProjectTreeWithoutgroup(data, params) {
  return fetch({
    url: `/eem-service/v1/loss/config/project/tree/withoutgroup`,
    method: "POST",
    data,
    params
  });
}

/**
 * 获取节点树
 */
export function getNodeTree(data) {
  return fetch({
    url: `/eem-base/energy/v1/node/tree`,
    method: "POST",
    data
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return fetch({
    url: `/eem-base/energy/v1/node/root-node`,
    method: "GET"
  });
}

/**
 * 查询指定节点类型以及子级
 */
export function findTreeConfigNode(data) {
  return fetch({
    url: `/eem-base/energy/v1/tree-config/findNode`,
    method: "POST",
    data
  });
}

// 获取节点树-不经过权限过滤
export function nodeTreeSimpleNoAuth(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree/simple/no-auth-filter`,
    method: "POST",
    data
  });
}
