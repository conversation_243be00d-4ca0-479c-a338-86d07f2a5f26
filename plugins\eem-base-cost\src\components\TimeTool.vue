<template>
  <div class="clearfix">
    <el-radio-group
      v-show="showOption"
      v-model="currentTimeOption"
      @input="changTimeType"
      class="fl mr-J1"
    >
      <el-radio-button
        v-for="(item, index) in timeOptions"
        :key="`${index}_${item.typeID}`"
        :label="item"
      >
        {{ item.text }}
      </el-radio-button>
    </el-radio-group>

    <el-button
      class="fl mr-J0"
      style="padding: 9px"
      plain
      size="small"
      icon="el-icon-arrow-left"
      :disabled="disable_in"
      @click="queryPrv"
    ></el-button>
    <CustomElDatePicker
      value-format="timestamp"
      :clearable="false"
      class="fl"
      style="width: 200px !important"
      v-model="value"
      :picker-options="pickerOptions"
      :type="currentTimeOption.type"
      :disabled="disable_in"
    />
    <el-button
      class="fl ml-J0"
      plain
      size="small"
      style="padding: 9px"
      icon="el-icon-arrow-right"
      :disabled="nextDisabled || disable_in"
      @click="queryNext"
    ></el-button>
  </div>
</template>
<script>
import omegaI18n from "@omega/i18n";
const TIME_TYPE = [
  {
    type: "date",
    text: $T("日"),
    typeID: 12,
    number: 1,
    unit: "d"
  },
  // {
  //   type: "week",
  //   text: "周",
  //   typeID: 2,
  //   alias:'w'
  // },
  {
    type: "month",
    text: $T("月"),
    typeID: 14,
    number: 1,
    unit: "M"
  },
  {
    type: "year",
    text: $T("年"),
    number: 1,
    typeID: 17,
    unit: "y"
  }
];
export default {
  name: "TimeTool",
  props: {
    time: {
      type: [Number, Object],
      default: +new Date()
    },
    val: {
      type: [Number, Object, String]
    },
    typeID: {
      type: Number,
      default: 14
    },
    showOption: {
      type: Boolean,
      default: true
    },
    selectDisable: {
      type: Boolean,
      default: false
    },
    timeType_in: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isNextDisabled: {
      type: Boolean,
      default: false
    },
    disable_in: {
      type: Boolean,
      default: false
    },
    shortcuts: {
      type: Array
    }
  },

  watch: {
    val: {
      deep: true,
      handler: function (val) {
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$emit("update:val", val);
        this.$emit("change", {
          val,
          timeOption: this._.find(TIME_TYPE, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    },
    typeID: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$set(
          this,
          "currentTimeOption",
          this._.find(TIME_TYPE, { typeID: val })
        );
      }
    },
    showOption: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$emit("change", {
          val: this.value,
          timeOption: this._.find(TIME_TYPE, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    }
  },
  computed: {
    nextDisabled() {
      return this.isNextDisabled
        ? this.$moment(this.value)
            .startOf(this.currentTimeOption.unit)
            .valueOf() >=
            this.$moment().startOf(this.currentTimeOption.unit).valueOf()
        : false;
    },
    language() {
      return omegaI18n.locale === "en";
    }
  },
  data(vm) {
    return {
      pickerOptions: {
        disabledDate(time) {
          return vm.isNextDisabled ? time.getTime() > Date.now() : false;
        },
        shortcuts: vm.shortcuts
      },
      timeOptions: TIME_TYPE,
      value: +new Date(),
      currentTimeOption: this._.find(TIME_TYPE, { typeID: this.typeID })
    };
  },
  methods: {
    // 通过匹配key与value返回timeType对应对象
    findTimeType(key, value) {
      return this.timeOptions.find(item => {
        return item[key] === value;
      });
    },
    queryPrv() {
      var date = this.$moment(this.value);
      this.value = date.subtract(1, this.currentTimeOption.unit).valueOf();
    },
    queryNext() {
      var date = this.$moment(this.value);
      this.value = date.add(1, this.currentTimeOption.unit).valueOf();
    },
    changDate(val) {
      this.value = val;
      this.$emit("update:val", val);
      this.$emit("change", {
        val,
        timeOption: this._.find(TIME_TYPE, {
          typeID: this.currentTimeOption.typeID
        })
      });
    },
    changTimeType(val) {
      if (this.value > new Date().getTime()) {
        this.value = new Date().getTime();
      }
      this.$emit("change", { val: this.value, timeOption: val });
    }
  },
  mounted() {
    this.value = this.val;
    this.timeOptions =
      this.timeType_in.length > 0 ? this.timeType_in : TIME_TYPE;
  }
};
</script>
