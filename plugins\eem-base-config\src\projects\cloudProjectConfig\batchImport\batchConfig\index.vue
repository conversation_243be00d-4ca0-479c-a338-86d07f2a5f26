<template>
  <div class="fullfilled flex flex-row">
    <div
      class="treeBox flex flex-col bg-BG1 rounded-Ra1 p-J4"
      id="cloudProjectConfig_batchImport_treeBox"
    >
      <CustomElSelect
        class="mb-J1"
        v-model="ElSelect_status.value"
        v-bind="ElSelect_status"
        v-on="ElSelect_status.event"
        :prefix_in="$T('生成状态')"
      >
        <ElOption
          v-for="item in ElOption_status.options_in"
          :key="item[ElOption_status.key]"
          :label="item[ElOption_status.label]"
          :value="item[ElOption_status.value]"
          :disabled="item[ElOption_status.disabled]"
        ></ElOption>
      </CustomElSelect>
      <CetGiantTree
        class="flex-auto"
        v-bind="CetGiantTree_project"
        v-on="CetGiantTree_project.event"
      ></CetGiantTree>
    </div>
    <div class="flex-auto ml-J1 flex flex-col bg-BG1 rounded-Ra1 p-J4">
      <div class="tanleHandle">
        <div class="legend">
          <i class="notGenerated"></i>
          {{ $T("未生成配置") }}
        </div>
        <div class="legend ml-J3">
          <i class="complete"></i>
          {{ $T("已生成配置") }}
        </div>
        <CetButton
          class="ml-J3"
          v-bind="CetButton_reset"
          v-on="CetButton_reset.event"
        ></CetButton>
        <CetButton
          id="cloudProjectConfig_batchImport_delete"
          class="ml-J1"
          v-bind="CetButton_batchDel"
          v-on="CetButton_batchDel.event"
          :disable_in="handsontable.deleteDisable"
        ></CetButton>
      </div>
      <div
        class="flex-auto mt-J3 cloudProjectConfig_batchImport"
        id="cloudProjectConfig_batchImport_tableBox"
      >
        <Handsontable
          ref="handsontable"
          v-bind="handsontable"
          v-on="handsontable.event"
          :deleteDisable.sync="handsontable.deleteDisable"
        />
        <div
          :class="{
            emptyBox: true,
            'bg-BG1': true,
            light: lightTheme
          }"
          v-if="isEmpty"
        >
          <div class="img"></div>
          <div class="text">{{ $T("请从左边节点树拖入节点，进行设置！") }}</div>
        </div>
      </div>
    </div>
    <RepeatConfirm v-bind="repeatConfirm" />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import Handsontable from "./handsontable.vue";
import omegaTheme from "@omega/theme";
import RepeatConfirm from "./repeatConfirm.vue";
import { CustomElSelect } from "eem-base/components";
export default {
  name: "batchConfig",
  props: {
    energyType_in: Number,
    currentNode_in: Object,
    nodeLabel_in: String
  },
  components: {
    Handsontable,
    RepeatConfirm,
    CustomElSelect
  },
  data() {
    return {
      ElSelect_status: {
        value: 2,
        style: {},
        event: {
          change: this.ElSelect_status_change_out
        }
      },
      ElOption_status: {
        options_in: [
          {
            id: 0,
            name: $T("全部")
          },
          {
            id: 1,
            name: $T("已经生成配置")
          },
          {
            id: 2,
            name: $T("未生成配置")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_project: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          edit: {
            enable: true,
            drag: {
              prev: false,
              next: false,
              inner: false
            }
          },
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "text",
              children: "children"
            }
          },
          callback: {
            onDrop: this.dropTree2Dom
          }
        },
        event: {}
      },
      CetButton_batchDel: {
        visible_in: true,
        disable_in: true,
        title: $T("批量移除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchDel_statusTrigger_out
        }
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置全部过滤"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      handsontable: {
        data: [],
        columns: [
          {
            className: "htLeft",
            data: "deviceName",
            type: "text",
            label: `${$T("表计名称")} <span class='required'>*</span>`,
            columnSorting: true,
            readOnly: true
          },
          {
            className: "htLeft",
            data: "channelName",
            type: "text",
            label: `${$T("通道名称")} <span class='required'>*</span>`,
            readOnly: true
          }
        ],
        deleteDisable: true,
        clearFilterHandle: Date.now(),
        clearSortHandle: Date.now(),
        clearHot: Date.now(),
        event: {}
      },
      repeatConfirm: {
        visibleTrigger_in: Date.now(),
        confirmFn: null,
        repeatNodeNames: []
      }
    };
  },
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    isEmpty() {
      return !this.handsontable.data?.length;
    },
    lightTheme() {
      return omegaTheme.theme === "light";
    }
  },
  methods: {
    async init() {
      this.chooseComplete = false;
      this.ElSelect_status.value = 2;
      this.getTreeData();
      this.getLimit();
      await this.getColumn();
    },
    async getLimit() {
      const res = await customApi.organizationConfigLimitImportConfig();
      this.configLimit = res?.data ?? 100;
    },
    ElSelect_status_change_out() {
      this.getTreeData();
    },
    async getTreeData() {
      const queryData = {
        tenantId: this.projectTenantId,
        buildStatus: this.ElSelect_status.value
      };
      const res = await customApi.organizationConfigTree(queryData);
      const data = res?.data ?? [];
      this.CetGiantTree_project.inputData_in = data;
    },
    async getColumn() {
      // 查列选项
      const manageNameRes = await customApi.organizationConfigManageName();
      const manageNames = manageNameRes?.data ?? [];

      const roomConfigRes = await customApi.organizationConfigRoomConfig();
      const roomConfigs = roomConfigRes?.data ?? [];

      const columnsConfig = [];

      // 所属功能房 能源类型为电，房间选项为配电房名称下拉；其他能源类型下拉选项为管道房名称下拉
      const roomType = this.energyType_in === 2 ? 1 : 6;

      const roomConfig = roomConfigs.find(i => i.room.roomType === roomType);
      const typeList = roomConfig?.children?.map(i => i.name) ?? [];
      columnsConfig.push({
        allowInvalid: true,
        data: "componentType",
        source: typeList,
        strict: true,
        type: "autocomplete",
        label: `${$T("元件类型")} <span class='required'>*</span>`
      });

      const roomObj = manageNames.find(i => {
        return i.modelLabel === "room" && i.roomType === roomType;
      });
      columnsConfig.push({
        allowInvalid: true,
        data: "functionalRoomName",
        source: roomObj?.nameList ?? [],
        strict: false,
        type: "autocomplete",
        label: `${
          roomType == 1 ? $T("所属配电房") : $T("所属管道房")
        } <span class='required'>*</span>`
      });

      // 存一下，给后面保存的时候解析用
      this.roomConfig = roomConfig;

      this.handsontable.columns.push(...columnsConfig);
    },
    async dropTree2Dom(event, treeId, treeNodes, targetNode, moveType) {
      // 判断是否拖拽至目标区域
      let target = event.target,
        enter = target.className.includes("cloudProjectConfig_batchImport");
      while (target && !enter) {
        target = target.parentNode;
        enter = target?.className?.includes("cloudProjectConfig_batchImport");
      }
      if (!enter) return;
      // 如果已有过滤条件存在，则不进行添加节点
      if (!this.ifHandsontableFilter()) return;
      const nodes = this.getDevices(treeNodes);
      // 先查在数据库是否有重复的草稿或已生成的配置
      const deviceIds = nodes.map(i => i.nodeId);
      const queryData = {
        deviceIds,
        manageNode: {
          id: this.currentNode_in.id,
          modelLabel: this.currentNode_in.modelLabel
        },
        energyType: this.energyType_in,
        nodeLabel: this.nodeLabel_in
      };
      const res = await customApi.organizationConfigQueryImportConfig(
        queryData
      );
      const saved = res?.data;
      saved.forEach(item => {
        item.deleteStatus = false;
        // 解析元件类型
        const list = this.roomConfig?.children ?? [];
        item.componentType = list.find(
          i => i.modelLabel === item.componentType
        )?.name;
      });
      this.filterItems(nodes, saved);
    },
    addTableItems(nodes, saved) {
      if (!nodes?.length) return;
      const tableData = this.$refs.handsontable.getTableData();
      this.handsontable.clearSortHandle = Date.now();

      const list = nodes.map(item => {
        const savedItem = saved.find(i => i.deviceId === item.nodeId) ?? {};
        return {
          deviceId: item.nodeId,
          deviceName: item.text,
          channelId: item.channelId,
          channelName: item.channelName,
          componentType: savedItem?.componentType ?? null, //元件类型
          lineFunctionType: savedItem?.lineFunctionType ?? null, //线功能
          functionalRoomName: savedItem?.functionalRoomName ?? null, //功能房名称
          projectId: this.projectId,
          modelLabel: this.nodeLabel_in,
          energyType: this.energyType_in,
          recordStatus: savedItem?.recordStatus ?? -1, //状态
          deleteStatus: false
        };
      });
      tableData.push(...list);
      // 限制添加节点总数
      if (tableData.length > this.configLimit) {
        this.$message.warning(
          $T(`单次编辑行数不能超过{0}行`, this.configLimit)
        );
        return;
      }
      this.handsontable.data = tableData;
    },
    getDevices(value) {
      let nodes = [];
      if (!value?.length) return [];
      const loop = function (val) {
        if (!val?.length) return;
        val.forEach(item => {
          if (item.nodeType === 269619472) {
            const parentNode = item.getParentNode();
            item.channelId = parentNode.nodeId;
            item.channelName = parentNode.text;
            nodes.push(item);
          }
          if (item?.children?.length) {
            loop(item.children);
          }
        });
      };
      loop(value);
      return nodes;
    },
    ifHandsontableFilter() {
      const handsontableFilter = this.$refs.handsontable.handsontableFilter;
      if (handsontableFilter) {
        this.$message.warning($T("请点击重置全部过滤"));
        return false;
      }
      return true;
    },
    // 添加的节点去重
    filterItems(nodes, saved) {
      const nodeIds = nodes.map(i => i.nodeId);
      const tableData = this.$refs.handsontable.getTableData();
      const ids = tableData.map(i => i.deviceId);
      const repeatIds = this.getRepeatNum([...nodeIds, ...ids]);
      const repeatNodeNames = [];
      const addNodes = nodes.filter(i => {
        if (!repeatIds.includes(i.nodeId)) {
          return true;
        } else {
          repeatNodeNames.push(i.text);
        }
      });
      if (!repeatNodeNames?.length) {
        this.addTableItems(addNodes, saved);
        return;
      }
      this.repeatConfirm.confirmFn = () => {
        this.$message({
          type: "warning",
          dangerouslyUseHTMLString: true,
          message: $T("已去重后添加")
        });
        this.addTableItems(addNodes, saved);
      };
      this.repeatConfirm.repeatNodeNames = repeatNodeNames;
      this.repeatConfirm.visibleTrigger_in = Date.now();
    },
    // 查重复的数字
    getRepeatNum(arr) {
      const counts = this._.countBy(arr);
      let duplicateNumbers = [];
      Object.keys(counts).forEach(key => {
        if (counts[key] > 1) {
          duplicateNumbers.push(+key);
        }
      });
      return duplicateNumbers;
    },
    CetButton_batchDel_statusTrigger_out() {
      this.$confirm($T("是否确认删除?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          this.handsontableDelete();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    // 假删除
    async handsontableDelete() {
      const list = this.$refs.handsontable.getTableData();
      const deleteItems = [];
      list.forEach(item => {
        if (item.deleteStatus) deleteItems.push(item);
      });
      if (!deleteItems.length) {
        this.$message.warning($T("请选择移除项"));
        return;
      }
      this.$message.success($T("操作成功"));
      this.$refs.handsontable.clearDeleteData();
    },
    CetButton_reset_statusTrigger_out() {
      this.handsontable.clearFilterHandle = Date.now();
    }
  },
  mounted() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  width: 296px;
}
.tanleHandle {
  display: flex;
  justify-content: flex-end;
}
.cloudProjectConfig_batchImport {
  position: relative;
  .emptyBox {
    z-index: 9999;
    position: absolute;
    left: 0;
    top: 0;
    bottom: -2px;
    right: -2px;
    .img {
      position: absolute;
      top: 70px;
      left: 50%;
      width: 310px;
      height: 302px;
      transform: translate(-50%, 0);
      background-image: url(("../assets/empty.png"));
      background-size: 100% 100%;
    }
    &.light .img {
      background-image: url(("../assets/empty_light.png"));
      background-size: 100% 100%;
    }
    .text {
      position: absolute;
      top: 384px;
      left: 50%;
      transform: translate(-50%, 0);
      text-align: center;
      font-size: var(--H5);
    }
  }
}
.legend {
  line-height: 32px;
  .notGenerated {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px 2px 2px 2px;
    background-color: var(--Sta2);
  }
  .complete {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px 2px 2px 2px;
    background-color: var(--Sta1);
  }
}
</style>

<style lang="scss">
ul.zTreeDragUL {
  position: fixed;
  list-style: none;
  padding: 4px;
  background-color: var(--BG4);
  z-index: 99999;
}
</style>
