/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "/lossAnalysis",
        component: () => import("@/projects/lossAnalysis/index.vue")
      },
      {
        path: "/energyLossConfig",
        component: () =>
          import("@/projects/energyLossManagement/energyLossConfig/index.vue")
      },
      {
        path: "/energyLossOverview",
        component: () =>
          import("@/projects/energyLossManagement/energyLossOverview/index.vue")
      },
      {
        path: "/energyLossWarning",
        component: () =>
          import("@/projects/energyLossManagement/energyLossWarning/index.vue")
      },
      {
        path: "/networkEnergy",
        component: () => import("@/projects/networkEnergy/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
