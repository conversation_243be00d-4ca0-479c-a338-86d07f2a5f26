<template>
  <div class="fullfilled flex flex-col" :class="{ light: isLight }">
    <div class="title text-H3 font-bold">{{ $T("区域成本TOP5") }}</div>
    <div class="flex-auto flex flex-col" v-if="dataList?.length">
      <div v-for="item in 5" :key="item" class="flex-auto mt-J3">
        <template v-if="dataList?.length >= item">
          <div class="flex-row flex mb-J0 titleBox">
            <div
              class="icon mr-J3"
              :style="{
                backgroundImage: `url(${require(`../assets/top${item}${
                  isLight ? '_light' : ''
                }.png`)})`
              }"
            ></div>
            <el-tooltip :content="dataList[item - 1].objectName">
              <div class="name text-ellipsis mr-J3 flex-auto">
                {{ dataList[item - 1].objectName }}
              </div>
            </el-tooltip>
            <div class="flex-row flex">
              <div class="value" :style="{ color: topColors[item - 1] }">
                {{ valueFormat(dataList[item - 1].value, total) }}
              </div>
              <div class="unit ml-J1">{{ unit }}</div>
            </div>
          </div>
          <el-progress
            :class="['progress', `top${item}`]"
            :stroke-width="6"
            :percentage="calculateRadioValue(dataList[item - 1].value, total)"
            :define-back-color="isLight ? '#F8FAFB' : '#1F2B54'"
            :show-text="false"
            :color="progressColor(item - 1)"
          ></el-progress>
        </template>
      </div>
    </div>
    <el-empty
      class="flex-auto mt-J3"
      v-else-if="isLight"
      :image-size="216"
      image="static/assets/empty_min_light.png"
    ></el-empty>
    <el-empty
      v-else
      class="flex-auto mt-J3"
      :image-size="216"
      image="static/assets/empty_min.png"
    ></el-empty>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  name: "newCostAnalysisProportion",
  props: {
    params: Object
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    },
    total() {
      return this.dataList?.[0].value;
    }
  },
  data() {
    const topColors =
      omegaTheme.theme === "light"
        ? ["#FFCE20", "#4CA6FF", "#E9BD9D", "#70E09E", "#70E09E"]
        : ["#FBBC43", "#22BDFF", "#E9BD9D", "#7ABCFF", "#7ABCFF"];
    return {
      topColors: topColors,
      unit: "--",
      dataList: []
    };
  },
  watch: {
    params: {
      handler() {
        this.getData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getData() {
      if (!this.params || !this.params?.nodes?.length) {
        this.dataList = [];
        return;
      }
      const res = await customApi.realtimeCostGroupByNode(this.params);
      this.unit = res?.data?.[0]?.unitName ?? "--";
      const dataList = res?.data ?? [];
      this.dataList = dataList;
    },
    calculateRadioValue(value, total) {
      return (Math.abs(value) / total) * 100;
    },
    progressColor(index) {
      return this.topColors[index];
    },
    valueFormat(val) {
      if (val == null) return "--";
      return common.formatNum(val.toFixed(2));
    }
  }
};
</script>

<style lang="scss" scoped>
.progress {
  height: 6px;
  width: 100%;
  &.top1 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #fbbc43 0%, #ffe381 100%);
  }
  &.top2 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #17baff 0%, #64d1ff 100%);
  }
  &.top3 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #d68866 0%, #e7c1b1 100%);
  }
  &.top4 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #158aff 0%, #7ebfff 100%);
  }
  &.top5 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #158aff 0%, #7ebfff 100%);
  }
}
.light .progress {
  &.top1 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #ffce20 0%, #ffea9d 100%);
  }
  &.top2 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #4ca6ff 0%, #95caff 100%);
  }
  &.top3 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #d68866 0%, #e7c1b1 100%);
  }
  &.top4 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #70e09e 0%, #9cf5c1 100%);
  }
  &.top5 :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #70e09e 0%, #9cf5c1 100%);
  }
}
.titleBox {
  height: 30px;
  line-height: 30px;
  .icon {
    width: 28px;
    height: 30px;
    display: inline-block;
    background-size: 100% 100%;
  }
  .value {
    font-family: Barlow, Barlow;
    font-weight: bold;
    font-size: 24px;
  }
  .unit {
    font-size: 12px;
    transform: translate(0px, 4px);
  }
}
</style>
