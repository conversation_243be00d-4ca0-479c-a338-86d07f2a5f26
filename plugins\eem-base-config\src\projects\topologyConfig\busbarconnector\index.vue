<template>
  <div class="w-full h-full flex flex-row">
    <div
      class="tree-box w-[315px] box-border p-[24px] mr-J2 border-solid border-t-0 border-l-0 border-b-0 border-r-[2px] border-B1"
    >
      <BusbarconnectorTree
        ref="busbarconnectorTree"
        :apiName="'topologyManageBusbarTree'"
        @checkedNodesOut="checkedNodesOut"
        @initTableData="initTableData"
      />
    </div>
    <div class="content-box flex-auto p-[24px] box-border flex flex-col">
      <el-empty
        class="fullheight"
        v-show="isLight && emptyCheck"
        :image-size="524"
        image="static/assets/empty_max_light.png"
      ></el-empty>
      <el-empty
        v-show="!isLight && emptyCheck"
        class="fullheight"
        :image-size="524"
        image="static/assets/empty_max.png"
      ></el-empty>
      <div class="flex flex-row justify-end items-center" v-show="!emptyCheck">
        <el-tooltip
          :content="
            $T(
              '创建母联：当配电房存在联络柜时需要创建母联；母联命名规则：配电房名称-Ⅰ段和Ⅱ段母联'
            )
          "
          effect="light"
        >
          <i class="el-icon-question text-T2"></i>
        </el-tooltip>
        <CetButton
          class="ml-J2"
          v-bind="CetButton_reset"
          v-on="CetButton_reset.event"
        ></CetButton>
        <CetButton
          class="ml-J2"
          v-bind="CetButton_delete"
          v-on="CetButton_delete.event"
          :disable_in="busbarconnectorTable.deleteDisable"
          v-permission="'topologymanage_update'"
        ></CetButton>
        <CetButton
          class="ml-J2"
          v-bind="CetButton_preview"
          v-on="CetButton_preview.event"
        ></CetButton>
        <CetButton
          v-if="editMode"
          class="ml-J2"
          v-bind="CetButton_auto"
          v-on="CetButton_auto.event"
        ></CetButton>

        <el-popover placement="bottom" trigger="hover" width="1120">
          <img class="association-img" :src="associationImg" alt="" />
          <span class="text-T3 leading-[32px] cursor-pointer" slot="reference">
            <i v-if="editMode" class="el-icon-question ml-J1"></i>
          </span>
        </el-popover>
        <CetButton
          v-if="!editMode"
          class="ml-J2"
          v-bind="CetButton_association"
          v-on="CetButton_association.event"
          v-permission="'topologymanage_update'"
        ></CetButton>
        <CetButton
          v-if="editMode"
          class="ml-J2"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-if="editMode"
          class="ml-J2"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </div>
      <div class="flex-auto mt-J2" v-show="!emptyCheck">
        <BusbarconnectorTable
          ref="busbarconnectorTable"
          v-bind="busbarconnectorTable"
          v-on="busbarconnectorTable.event"
          :deleteDisable.sync="busbarconnectorTable.deleteDisable"
        />
      </div>
    </div>
    <TopologyChartPreview
      v-bind="topologyChartPreview"
      v-on="topologyChartPreview.event"
    />
    <MsgBox v-bind="msgBox" />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import BusbarconnectorTree from "../components/tree.vue";
import BusbarconnectorTable from "./busbarconnectorTable.vue";
import TopologyChartPreview from "../components/topologyChartPreview.vue";
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import MsgBox from "./msgBox.vue";
export default {
  name: "busbarconnectorPage",
  components: {
    BusbarconnectorTree,
    BusbarconnectorTable,
    TopologyChartPreview,
    MsgBox
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    emptyCheck() {
      return !this.checkedNodes?.length;
    },
    isLight() {
      return omegaTheme.theme === "light";
    },
    associationImg() {
      const currentTheme = omegaTheme.theme;
      if (["dark", "blue"].includes(currentTheme)) {
        return require("../assets/busbarconnector-association-dark.png");
      }
      return require("../assets/busbarconnector-association.png");
    }
  },
  data() {
    return {
      modified: false,
      treeStatusBack: null,
      checkedNodesBack: null,
      treeStatus: null,
      checkedNodes: null,
      editMode: false,
      limit: 3000,
      deleteList: [],
      isFillConnectData: false,
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("批量删除配置"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      CetButton_preview: {
        visible_in: true,
        disable_in: false,
        title: $T("效果预览"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preview_statusTrigger_out
        }
      },
      CetButton_auto: {
        visible_in: true,
        disable_in: false,
        title: $T("自动识别母线"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_auto_statusTrigger_out
        }
      },
      CetButton_association: {
        visible_in: true,
        disable_in: false,
        title: $T("创建母联"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_association_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置全部过滤"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      busbarconnectorTable: {
        data: [],
        columns: [],
        clearFilterHandle: Date.now(),
        deleteDisable: true,
        event: {
          dataChange: () => {
            this.modified = true;
          }
        }
      },
      topologyChartPreview: {
        inputData_in: null,
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        saveMode: false,
        event: {
          confirm_out: this.topologyChartPreview_confirm_out
        }
      },
      msgBox: {
        inputData_in: null,
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now()
      }
    };
  },
  methods: {
    /**
     * 节点树勾选输出
     * @param val 勾选节点数组
     * @param status 当前选择的关联状态
     */
    async checkedNodesOut(val, status) {
      const checkedNodes =
        val?.filter(i => {
          return !["project"].includes(i.modelLabel);
        }) ?? [];
      if (!checkedNodes?.length) {
        this.$message.warning($T("至少勾选一个房间节点"));
        this.checkedNodes = [];
        return;
      }
      // 如果选择节点超过最大限制，提示并回退上一次选择
      if (checkedNodes?.length > this.limit) {
        this.$message.warning($T("节点选择超出最大限制{0}", this.limit));
        this.treeCheckedGoBack(this.treeStatus, this.checkedNodes);
        return;
      }

      const canLeave = await this.abandonEditing();
      if (!canLeave) {
        this.treeCheckedGoBack(this.treeStatus, this.checkedNodes);
        return;
      }

      this.queryTable(status, checkedNodes);
    },

    async queryTable(status, checkedNodes) {
      if (this.treeStatusBack === null) {
        this.treeStatusBack = this._.cloneDeep(status);
        this.checkedNodesBack = this._.cloneDeep(checkedNodes);
      } else {
        this.treeStatusBack = this._.cloneDeep(this.treeStatus);
        this.checkedNodesBack = this._.cloneDeep(this.checkedNodes);
      }

      this.treeStatus = status;
      this.checkedNodes = checkedNodes;
      this.getTableData();
    },

    initTableData() {
      this.editMode = false;
      this.busbarconnectorTable.data = [];
    },

    async getTableData() {
      await this.getColumn();
      this.busbarconnectorTable.clearFilterHandle = Date.now();

      this.deleteList = [];

      const params = {
        isFillConnectData: this.editMode ? this.isFillConnectData : false,
        node:
          this.checkedNodes?.map(i => {
            return {
              id: i.id,
              modelLabel: i.modelLabel
            };
          }) ?? [],
        projectId: this.projectId,
        nodeConnectStatus: this.treeStatus
      };
      const res = await customApi.topologyManageBusbarConfigInfo(params);
      if (res.code !== 0) {
        // 回退一下状态与节点，避免下次点击的时候无法再回退了
        this.treeStatus = this._.cloneDeep(this.treeStatusBack);
        this.checkedNodes = this._.cloneDeep(this.checkedNodesBack);
        this.treeCheckedGoBack(this.treeStatusBack, this.checkedNodesBack);
        return;
      }
      const data = res.data || [];
      data.forEach((item, index) => {
        item.tree_id = index;
        item.deleteStatus = false;
      });
      this.busbarconnectorTable.data = data;
      this.modified = false;
    },

    async getColumn() {
      const params = {
        projectId: this.projectId
      };
      const res = await customApi.topologyManageNode(params);
      const data = res.data || [];

      /**
       * 按房间分好类，一个配电房底下只能选择其配电房下的母线、母联
       *
       * 母线、母联的下拉项需要将表格中当前房间下所有已选对应的母线、母联排查在外
       */

      const optionMap = {};
      data.forEach(({ modelLabel, roomId, nameList }) => {
        if (["busbarconnector", "busbarsection"].includes(modelLabel)) {
          if (!optionMap[roomId]) {
            optionMap[roomId] = {};
          }
          optionMap[roomId][modelLabel] = nameList;
        }
      });

      const vm = this;
      const columns = [
        {
          className: "htLeft",
          data: "roomName",
          type: "text",
          label: $T("所属配电房"),
          columnSorting: true,
          readOnly: true
        },
        {
          className: "htLeft",
          data: "busBarSectionFirstName",
          type: "autocomplete",
          strict: true,
          label: $T("母线1"),
          readOnly: !this.editMode,
          source: function (query, process) {
            /**
             * 改为母线只允许出现一次，直接把下拉选项限制一下即可
             * 后续可能会改回母线1+母线2不能重复，将注释部分打开即可
             */
            const rowData = this.instance.getSourceDataAtRow(this.row);
            const roomId = rowData.roomId;
            const tree_id = rowData.tree_id;
            const nameList = optionMap[roomId]?.busbarsection ?? [];

            const tableData = vm.$refs.busbarconnectorTable.getTableData();
            const filterNameList = tableData.reduce((prev, cur) => {
              if (cur.roomId === roomId) {
                if (cur.busBarSectionSecondName) {
                  prev.push(cur.busBarSectionSecondName);
                }
                if (cur.busBarSectionFirstName && cur.tree_id !== tree_id) {
                  prev.push(cur.busBarSectionFirstName);
                }
              }
              return prev;
            }, []);

            const list = nameList.filter(
              name => !filterNameList.includes(name)
            );
            process(list);

            // const { busBarSectionSecondName, roomId } =
            //   this.instance.getSourceDataAtRow(this.row);
            // const nameList = optionMap[roomId]?.busbarsection ?? [];

            // const list = nameList.filter(
            //   name => busBarSectionSecondName !== name
            // );
            // process(list);
          },
          validator: function (value, callback) {
            const { busBarSectionSecondName, tree_id, roomId } =
              this.instance.getSourceDataAtRow(this.row);

            // 母线1+母线2不能相同
            if (value === busBarSectionSecondName) {
              callback(false);
              return false;
            }

            const tableData = vm.$refs.busbarconnectorTable.getTableData();
            // 改为母线只允许出现一次，直接把下拉选项限制一下即可
            const list = tableData.filter(
              i =>
                value &&
                (i.busBarSectionSecondName === value ||
                  i.busBarSectionFirstName === value) &&
                i.tree_id !== tree_id &&
                i.roomId === roomId
            );

            //母线1+母线2填了之后 组合不允许重复
            // const list = tableData.filter(
            //   i =>
            //     i.busBarSectionSecondName === busBarSectionSecondName &&
            //     i.tree_id !== tree_id &&
            //     i.busBarSectionFirstName === value &&
            //     i.roomId === roomId
            // );
            if (list.length) {
              callback(false);
              return false;
            }

            const source = optionMap[roomId]?.busbarsection ?? [];
            callback(source.includes(value));
            return source.includes(value);
          }
        },
        {
          className: "htLeft",
          data: "busBarSectionSecondName",
          type: "autocomplete",
          strict: true,
          label: $T("母线2"),
          readOnly: !this.editMode,
          source: function (query, process) {
            /**
             * 改为母线只允许出现一次，直接把下拉选项限制一下即可
             * 后续可能会改回母线1+母线2不能重复，将注释部分打开、验证方法打开即可
             */
            const rowData = this.instance.getSourceDataAtRow(this.row);
            const roomId = rowData.roomId;
            const tree_id = rowData.tree_id;
            const nameList = optionMap[roomId]?.busbarsection ?? [];

            const tableData = vm.$refs.busbarconnectorTable.getTableData();
            const filterNameList = tableData.reduce((prev, cur) => {
              if (cur.roomId === roomId) {
                if (cur.busBarSectionSecondName && cur.tree_id !== tree_id) {
                  prev.push(cur.busBarSectionSecondName);
                }
                if (cur.busBarSectionFirstName) {
                  prev.push(cur.busBarSectionFirstName);
                }
              }
              return prev;
            }, []);

            const list = nameList.filter(
              name => !filterNameList.includes(name)
            );
            process(list);

            // const { busBarSectionFirstName, roomId } =
            //   this.instance.getSourceDataAtRow(this.row);
            // const nameList = optionMap[roomId]?.busbarsection ?? [];
            // const list = nameList.filter(
            //   name => busBarSectionFirstName !== name
            // );
            // process(list);
          },
          validator: function (value, callback) {
            const {
              busBarSectionFirstName,
              tree_id,
              roomId,
              busBarConnectName
            } = this.instance.getSourceDataAtRow(this.row);

            // 母线2和母联可以同时不选
            if (!value && !busBarConnectName) {
              callback(true);
              return true;
            }

            // 母线1+母线2不能相同
            if (value === busBarSectionFirstName) {
              callback(false);
              return false;
            }

            const tableData = vm.$refs.busbarconnectorTable.getTableData();
            // 改为母线只允许出现一次，直接把下拉选项限制一下即可
            const list = tableData.filter(
              i =>
                value &&
                (i.busBarSectionSecondName === value ||
                  i.busBarSectionFirstName === value) &&
                i.tree_id !== tree_id &&
                i.roomId === roomId
            );
            //母线1+母线2填了之后 组合不允许重复
            // const list = tableData.filter(
            //   i =>
            //     i.busBarSectionFirstName === busBarSectionFirstName &&
            //     i.tree_id !== tree_id &&
            //     i.busBarSectionSecondName === value &&
            //     i.roomId === roomId
            // );
            if (list.length) {
              callback(false);
              return false;
            }

            const source = optionMap[roomId]?.busbarsection ?? [];
            callback(source.includes(value));
            return source.includes(value);
          }
        },
        {
          className: "htLeft",
          data: "busBarConnectName",
          type: "autocomplete",
          strict: false,
          label: $T("创建母联"),
          readOnly: !this.editMode,
          source: function (query, process) {
            const rowData = this.instance.getSourceDataAtRow(this.row);
            const roomId = rowData.roomId;
            const tree_id = rowData.tree_id;
            const nameList = optionMap[roomId]?.busbarconnector ?? [];

            const tableData = vm.$refs.busbarconnectorTable.getTableData();
            const filterNameList = tableData.reduce((prev, cur) => {
              if (cur.roomId === roomId) {
                if (cur.busBarConnectName && cur.tree_id !== tree_id) {
                  prev.push(cur.busBarConnectName);
                }
              }
              return prev;
            }, []);

            const list = nameList.filter(
              name => !filterNameList.includes(name)
            );
            process(list);
          },
          validator: function (value, callback) {
            const { busBarSectionSecondName, tree_id } =
              this.instance.getSourceDataAtRow(this.row);

            //母线2填了就必须填母联
            if (!value) {
              if (busBarSectionSecondName) {
                callback(false);
                return false;
              }
              callback(true);
              return true;
            }

            //母联不允许重复
            const tableData = vm.$refs.busbarconnectorTable.getTableData();
            const list = tableData.filter(
              i => i.busBarConnectName === value && i.tree_id !== tree_id
            );
            if (list.length) {
              callback(false);
              return false;
            }

            const checkNamePattern = common.pattern_name.pattern;
            const checkNameLength = common.check_name.max;
            const isValid =
              checkNamePattern.test(value.trim()) &&
              value.trim().length < checkNameLength;

            callback(isValid);
            return isValid;
          }
        }
      ];
      this.busbarconnectorTable.columns = columns;
    },

    CetButton_delete_statusTrigger_out() {
      const data = this.$refs.busbarconnectorTable.getTableData();
      const deleteList = this._.cloneDeep(data.filter(i => i.deleteStatus));
      if (!this.editMode) {
        // 查看状态下直接进行删除
        this.$confirm($T("是否确认删除?"), $T("提示"), {
          confirmButtonText: $T("确认"),
          cancelButtonText: $T("取消"),
          distinguishCancelAndClose: true,
          type: "warning"
        })
          .then(() => {
            this.deleteData(deleteList);
          })
          .catch(() => {
            this.$message.info($T("已取消"));
          });
        return;
      } else {
        // 编辑状态下，先将删除的行存起来，最后保存的时候再统一删除
        this.deleteList.push(...deleteList);
        this.$refs.busbarconnectorTable.clearDeleteData();
      }
    },

    async CetButton_preview_statusTrigger_out() {
      if (!this.ifHandsontableFilter()) return;
      this.openPreview();
    },

    /**
     * 打开预览弹框
     * @param saveMode 是否以保存模式进行预览
     */
    async openPreview(saveMode = false) {
      if (saveMode) {
        const valid = await this.$refs.busbarconnectorTable.validateCells();
        if (!valid) return;
      }
      const tableData = this.$refs.busbarconnectorTable.getTableData();
      const tableDataValid = await this.checkTableData();
      if (!tableDataValid) {
        return;
      }

      const params = this.convertTableData(tableData);
      const res = await customApi.topologyManageBusbarPreview(params);
      if (res.code !== 0) return;

      const dataInfo = res?.data?.dataInfo ?? [];
      const dataLink = res?.data?.dataLink ?? [];
      this.topologyChartPreview.inputData_in = {
        nodes: dataInfo.map(item => {
          return {
            id: item.name,
            label: item.nodeName,
            nodeLabel: item.nodeLabel
          };
        }),
        edges: dataLink
      };
      this.topologyChartPreview.saveMode = saveMode;
      this.topologyChartPreview.openTrigger_in = Date.now();
    },

    CetButton_association_statusTrigger_out() {
      this.isFillConnectData = false;
      this.editMode = true;
      this.getTableData();
    },

    CetButton_auto_statusTrigger_out() {
      this.isFillConnectData = true;
      this.editMode = true;
      this.getTableData();
    },

    async CetButton_cancel_statusTrigger_out() {
      const canLeave = await this.abandonEditing();
      if (!canLeave) return;
      this.editMode = false;
      this.getTableData();
    },

    async CetButton_confirm_statusTrigger_out() {
      if (!this.ifHandsontableFilter()) return;
      this.openPreview(true);
    },

    async topologyChartPreview_confirm_out() {
      const res = await this.saveData();
      if (!res) return;
      this.editMode = false;
      this.getTableData();
      this.topologyChartPreview.closeTrigger_in = Date.now();
    },

    async saveData() {
      const valid = await this.$refs.busbarconnectorTable.validateCells();
      if (!valid) return false;
      const params = {
        deleteData: this.convertTableData(this.deleteList),
        projectId: this.projectId,
        busBarConfigVOList: []
      };
      const tableData = this.$refs.busbarconnectorTable.getTableData();
      params.busBarConfigVOList = this.convertTableData(tableData);
      const res = await this.saveFn(params);
      return res;
    },

    async deleteData(deleteList) {
      const params = {
        deleteData: this.convertTableData(deleteList),
        projectId: this.projectId,
        busBarConfigVOList: []
      };
      const res = await this.saveFn(params);
      if (!res) return;
      this.getTableData();
    },

    async saveFn(data) {
      const res = await customApi.topologyManageBusbarConfig(data);
      if (res.code !== 0) return;
      this.$message.success($T("操作成功"));
      return true;
    },

    CetButton_reset_statusTrigger_out() {
      this.busbarconnectorTable.clearFilterHandle = Date.now();
    },

    /**
     * 将表格数据转换为接口入参格式
     * @param list 表格数据
     */
    convertTableData(list) {
      return list?.map(item => {
        return {
          busBarConnectName: item.busBarConnectName,
          busBarSectionFirstName: item.busBarSectionFirstName,
          busBarSectionSecondName: item.busBarSectionSecondName,
          roomName: item.roomName,
          roomId: item.roomId
        };
      });
    },

    /**
     * 判断表格是否存在筛选
     * @param warningText 存在过滤的提示文本
     */
    ifHandsontableFilter(warningText) {
      const handsontableFilter =
        this.$refs.busbarconnectorTable.handsontableFilter;
      if (handsontableFilter) {
        this.$message.warning(warningText || $T("请点击重置全部过滤"));
        return false;
      }
      return true;
    },

    /**
     * 回退上一次节点勾选与状态
     */
    treeCheckedGoBack(treeStatus, checkedNodes) {
      this.$refs.busbarconnectorTree.setCheckedAndTreeType(
        checkedNodes,
        treeStatus
      );
    },

    /**
     * 放弃编辑操作前的确认逻辑
     * 如果数据已被修改且处于编辑模式，弹出确认框询问用户是否保存
     * 根据用户的选择（确定、取消或关闭弹窗）返回相应的结果
     *
     * @returns {Promise<boolean>} 返回一个 Promise，解析为布尔值：
     * - `true`：表示用户可以继续执行后续操作（如离开页面或关闭弹窗）。
     * - `false`：表示用户取消了操作，阻止后续逻辑（如留在当前页面）。
     *
     * @example
     * const canLeave = await abandonEditing();
     * if (canLeave) {
     *   // 用户确认保存或取消操作，继续执行后续逻辑
     * } else {
     *   // 用户关闭了弹窗，阻止后续操作
     * }
     */
    async abandonEditing() {
      if (!this.modified || !this.editMode) {
        return true;
      }
      try {
        await this.$confirm($T("数据已修改，是否进行保存？"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          distinguishCancelAndClose: true,
          type: "warning"
        });
        this.CetButton_confirm_statusTrigger_out();
        return false;
      } catch (action) {
        if (action === "cancel") {
          // 用户点击了取消
          return true;
        }
        // 用户关闭了弹窗
        this.$message.info($T("已取消"));
        return false;
      }
    },

    /**
     * 母线1+母线2组合不允许重复
     * 母联不允许重复
     * 母线2填了就必须填母联
     */
    async checkTableData() {
      const valid = await this.$refs.busbarconnectorTable.validateCells();
      if (!valid) return false;
      const tableData = this.$refs.busbarconnectorTable.getTableData();
      const indexes = [];
      tableData.forEach(
        (
          {
            busBarSectionFirstName,
            busBarSectionSecondName,
            busBarConnectName,
            roomId
          },
          index
        ) => {
          const rule1 =
            tableData.filter(item => {
              return (
                item.busBarSectionFirstName === busBarSectionFirstName &&
                item.busBarSectionSecondName === busBarSectionSecondName &&
                item.roomId === roomId
              );
            }).length > 1;
          const rule2 =
            tableData.filter(item => {
              return (
                item.busBarConnectName === busBarConnectName &&
                busBarConnectName &&
                item.roomId === roomId
              );
            }).length > 1;
          const rule3 = !!busBarSectionSecondName && !busBarConnectName;
          if (rule1 || rule2 || rule3) {
            indexes.push(index + 1);
          }
        }
      );
      if (indexes.length) {
        this.msgBox.inputData_in = indexes;
        this.msgBox.visibleTrigger_in = Date.now();
        return false;
      }
      return true;
    }
  },
  async created() {
    const res = await customApi.manageLimitImportConfig();
    this.limit = res.data ?? 3000;
  }
};
</script>

<style lang="scss" scoped>
.association-img {
  width: 1120px;
}
</style>
