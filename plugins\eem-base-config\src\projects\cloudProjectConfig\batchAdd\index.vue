<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
      <div>
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="nodeType flex flex-col">
              <div class="label">
                {{ $T("节点类型") }}
                <span class="required">*</span>
              </div>
              <ElSelect
                class="mt-J1"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                :placeholder="$T('请选择节点类型')"
                @change="nodeTypeChange"
              >
                <ElOption
                  v-for="item in ElOption_nodeType.options_in"
                  :key="item[ElOption_nodeType.key]"
                  :label="item[ElOption_nodeType.label]"
                  :value="item[ElOption_nodeType.value]"
                  :disabled="item[ElOption_nodeType.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
        </el-row>
        <div class="nodeTable flex flex-col mt-J3">
          <div class="tableHeader flex flex-row mb-J3">
            <div class="flex-auto title">{{ $T("创建节点") }}</div>
            <CetButton
              v-bind="CetButton_delete"
              v-on="CetButton_delete.event"
              :disable_in="!tableSelectData?.length"
            ></CetButton>
          </div>
          <div class="tableBody">
            <CetForm
              ref="cetForm"
              class="cetForm"
              :data.sync="CetTable_1.data"
              v-bind="CetForm_1"
              v-on="CetForm_1.event"
            >
              <CetTable
                ref="cetTable"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
                @selection-change="handleSelectionChange"
              >
                <ElTableColumn
                  type="selection"
                  width="50"
                  headerAlign="center"
                  align="center"
                  fixed="left"
                  :selectable="
                    (row, index) => index !== CetTable_1.data.length - 1
                  "
                ></ElTableColumn>
                <ElTableColumn
                  :label="$T('序号')"
                  width="70"
                  type="index"
                  headerAlign="center"
                  align="center"
                  fixed="left"
                ></ElTableColumn>
                <template v-for="item in showFieldList">
                  <ElTableColumn
                    v-if="
                      !['pic', 'document', 'latitude', 'longitude'].includes(
                        item.type
                      )
                    "
                    :key="item.propertyLabel"
                    headerAlign="left"
                    align="left"
                    showOverflowTooltip
                    minWidth="200"
                  >
                    <template slot="header">
                      <span>{{ item.name }}</span>
                      <span v-if="item.unit">({{ item.unit }})</span>
                      <span class="required ml-J mr-J" v-if="isRequired(item)">
                        *
                      </span>
                      <el-tooltip
                        :content="nameTooltip(item)"
                        effect="light"
                        v-if="item.nameTooltip || item.noEdit"
                      >
                        <i
                          class="el-icon-question ml-J"
                          v-if="item.nameTooltip || item.noEdit"
                        ></i>
                      </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="`[${scope.$index}].${item.propertyLabel}`"
                        :rules="getRules(item, scope.$index)"
                      >
                        <!-- 枚举 存在show的字段 -->
                        <template v-if="item.showFn">
                          <template
                            v-for="(showFnItem, showFnIndex) in item.showFields"
                          >
                            <ElSelect
                              v-if="
                                showFnItem.type === 'energytype' &&
                                (!showFnItem.show ||
                                  showFnItem.show(
                                    CetTable_1.data[scope.$index]
                                  ))
                              "
                              :key="`${showFnItem.propertyLabel}_${showFnIndex}`"
                              v-model="
                                CetTable_1.data[scope.$index][
                                  showFnItem.propertyLabel
                                ]
                              "
                              v-bind="ElSelect_1"
                              :clearable="clearSelect(showFnItem)"
                              @change="itemDataChange(showFnItem, scope)"
                            >
                              <ElOption
                                v-for="optionItem in showFnItem.filterElOption
                                  ? showFnItem.filterElOption(
                                      ElOption_energytype.options_in
                                    )
                                  : ElOption_energytype.options_in"
                                :key="optionItem[ElOption_energytype.key]"
                                :label="optionItem[ElOption_energytype.label]"
                                :value="optionItem[ElOption_energytype.value]"
                                :disabled="
                                  optionItem[ElOption_energytype.disabled]
                                "
                              ></ElOption>
                            </ElSelect>
                            <ElSelect
                              v-else-if="
                                showFnItem.type === 'product' &&
                                (!showFnItem.show ||
                                  showFnItem.show(
                                    CetTable_1.data[scope.$index]
                                  ))
                              "
                              :key="`${showFnItem.propertyLabel}_${showFnIndex}`"
                              v-model="
                                CetTable_1.data[scope.$index][
                                  showFnItem.propertyLabel
                                ]
                              "
                              v-bind="ElSelect_1"
                              :clearable="clearSelect(showFnItem)"
                              @change="itemDataChange(showFnItem, scope)"
                            >
                              <ElOption
                                v-for="optionItem in showFnItem.filterElOption
                                  ? showFnItem.filterElOption(
                                      ElOption_product.options_in
                                    )
                                  : ElOption_product.options_in"
                                :key="optionItem[ElOption_product.key]"
                                :label="optionItem[ElOption_product.label]"
                                :value="optionItem[ElOption_product.value]"
                                :disabled="
                                  optionItem[ElOption_product.disabled]
                                "
                              ></ElOption>
                            </ElSelect>
                            <ElSelect
                              v-else-if="
                                !showFnItem.show ||
                                showFnItem.show(CetTable_1.data[scope.$index])
                              "
                              :key="`${showFnItem.propertyLabel}_${showFnIndex}`"
                              v-model="
                                CetTable_1.data[scope.$index][
                                  showFnItem.propertyLabel
                                ]
                              "
                              v-bind="ElSelect_1"
                              :clearable="clearSelect(showFnItem)"
                              :placeholder="
                                showFnItem.placeholder || ElSelect_1.placeholder
                              "
                              @change="itemDataChange(showFnItem, scope)"
                            >
                              <ElOption
                                v-for="optionItem in showFnItem.filterElOption
                                  ? showFnItem.filterElOption(
                                      enumerations[showFnItem.enumLabel]
                                    )
                                  : enumerations[showFnItem.enumLabel]"
                                :key="optionItem[ElOption_enums.key]"
                                :label="optionItem[ElOption_enums.label]"
                                :value="optionItem[ElOption_enums.value]"
                                :disabled="optionItem[ElOption_enums.disabled]"
                              ></ElOption>
                            </ElSelect>
                          </template>
                        </template>
                        <!-- 能源类型 -->
                        <ElSelect
                          v-else-if="item.type === 'energytype'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="optionItem in item.filterElOption
                              ? item.filterElOption(
                                  ElOption_energytype.options_in
                                )
                              : ElOption_energytype.options_in"
                            :key="optionItem[ElOption_energytype.key]"
                            :label="optionItem[ElOption_energytype.label]"
                            :value="optionItem[ElOption_energytype.value]"
                            :disabled="optionItem[ElOption_energytype.disabled]"
                          ></ElOption>
                        </ElSelect>
                        <!-- 设备归类 -->
                        <ElSelect
                          v-else-if="item.type === 'deviceclassification'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="item in ElOption_deviceclassification.options_in"
                            :key="item[ElOption_deviceclassification.key]"
                            :label="item[ElOption_deviceclassification.label]"
                            :value="item[ElOption_deviceclassification.value]"
                            :disabled="
                              item[ElOption_deviceclassification.disabled]
                            "
                          ></ElOption>
                        </ElSelect>
                        <!-- 母线 -->
                        <ElSelect
                          v-else-if="item.type === 'busbarseg'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="item in ElOption_busbarseg.options_in"
                            :key="item[ElOption_busbarseg.key]"
                            :label="item[ElOption_busbarseg.label]"
                            :value="item[ElOption_busbarseg.value]"
                            :disabled="item[ElOption_busbarseg.disabled]"
                          ></ElOption>
                        </ElSelect>
                        <!-- 产品类型 -->
                        <ElSelect
                          v-else-if="item.type === 'product'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="item in ElOption_product.options_in"
                            :key="item[ElOption_product.key]"
                            :label="item[ElOption_product.label]"
                            :value="item[ElOption_product.value]"
                            :disabled="item[ElOption_product.disabled]"
                          ></ElOption>
                        </ElSelect>
                        <!-- 范围选择 -->
                        <div v-else-if="item.type === 'rangeSelection'">
                          <div class="flex flex-row">
                            <div class="flex-auto">
                              <ElInputNumber
                                v-model="
                                  CetTable_1.data[scope.$index][
                                    item.propertyLabel
                                  ]
                                "
                                v-bind="ElInputNumber_Float"
                                v-on="ElInputNumber_Float.event"
                              ></ElInputNumber>
                            </div>
                            <template v-for="i in item.relatedLabel">
                              <span class="ml-J mr-J" :key="`${i}span`">~</span>
                              <div class="flex-auto" :key="`${i}div`">
                                <ElInputNumber
                                  v-model="CetTable_1.data[scope.$index][i]"
                                  v-bind="ElInputNumber_Float"
                                  v-on="ElInputNumber_Float.event"
                                ></ElInputNumber>
                              </div>
                            </template>
                          </div>
                        </div>
                        <!-- 枚举 -->
                        <ElSelect
                          v-if="item.type === 'enums'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          :placeholder="
                            item.placeholder || ElSelect_1.placeholder
                          "
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="item in item.filterElOption
                              ? item.filterElOption(
                                  enumerations[item.enumLabel]
                                )
                              : enumerations[item.enumLabel]"
                            :key="item[ElOption_enums.key]"
                            :label="item[ElOption_enums.label]"
                            :value="item[ElOption_enums.value]"
                            :disabled="item[ElOption_enums.disabled]"
                          ></ElOption>
                        </ElSelect>
                        <!--  自定义枚举 -->
                        <ElSelect
                          v-if="item.type === 'customEnums'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          :placeholder="
                            item.placeholder || ElSelect_1.placeholder
                          "
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="item in item.customEnums_ElOption"
                            :key="item[ElOption_enums.key]"
                            :label="item[ElOption_enums.label]"
                            :value="item[ElOption_enums.value]"
                            :disabled="item[ElOption_enums.disabled]"
                          ></ElOption>
                        </ElSelect>
                        <!-- 文本 -->
                        <!-- 名称加上失去焦点后添加一行逻辑 -->
                        <ElInput
                          v-else-if="
                            item.type === 'string' &&
                            item.propertyLabel === 'name'
                          "
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElInput_1"
                          v-on="ElInput_1.event"
                          :placeholder="
                            item.placeholder || ElInput_1.placeholder
                          "
                          @blur="
                            addItem(`[${scope.$index}].${item.propertyLabel}`)
                          "
                          @keyup.enter.native="
                            addItem(`[${scope.$index}].${item.propertyLabel}`)
                          "
                        ></ElInput>
                        <ElInput
                          v-else-if="item.type === 'string'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElInput_1"
                          v-on="ElInput_1.event"
                          :placeholder="
                            item.placeholder || ElInput_1.placeholder
                          "
                          @change="itemDataChange(item, scope)"
                        ></ElInput>
                        <ElInput
                          v-else-if="item.type === 'textarea'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElInput_1"
                          v-on="ElInput_1.event"
                          type="textarea"
                          :rows="1"
                          :maxlength="item.maxlength || 200"
                          show-word-limit
                          :placeholder="
                            item.placeholder || ElInput_1.placeholder
                          "
                          @change="itemDataChange(item, scope)"
                        ></ElInput>
                        <!-- 布尔值 -->
                        <ElSelect
                          v-else-if="item.type === 'boolean'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElSelect_1"
                          :clearable="clearSelect(item)"
                          @change="itemDataChange(item, scope)"
                        >
                          <ElOption
                            v-for="item in ElOption_boolean.options_in"
                            :key="item[ElOption_boolean.key]"
                            :label="item[ElOption_boolean.label]"
                            :value="item[ElOption_boolean.value]"
                            :disabled="item[ElOption_boolean.disabled]"
                          ></ElOption>
                        </ElSelect>
                        <!-- 数字 -->
                        <ElInputNumber
                          v-else-if="item.type === 'numberFloat'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElInputNumber_Float"
                          v-on="ElInputNumber_Float.event"
                          :placeholder="
                            item.placeholder || ElInput_1.placeholder
                          "
                          @change="itemDataChange(item, scope)"
                        ></ElInputNumber>
                        <ElInputNumber
                          v-else-if="item.type === 'numberInt'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          v-bind="ElInputNumber_Int"
                          v-on="ElInputNumber_Int.event"
                          :placeholder="
                            item.placeholder || ElInput_1.placeholder
                          "
                          @change="itemDataChange(item, scope)"
                        ></ElInputNumber>

                        <!-- 时间 -->
                        <el-date-picker
                          v-else-if="item.type === 'datePicker'"
                          v-model="
                            CetTable_1.data[scope.$index][item.propertyLabel]
                          "
                          value-format="timestamp"
                          type="date"
                          :editable="false"
                          :pickerOptions="item.pickerOptions || {}"
                          :placeholder="$T('选择日期')"
                          @change="itemDataChange(item, scope)"
                        ></el-date-picker>
                      </el-form-item>
                    </template>
                  </ElTableColumn>
                  <ElTableColumn
                    v-else-if="item.type === 'document'"
                    :key="item.propertyLabel"
                    headerAlign="left"
                    align="left"
                    showOverflowTooltip
                    minWidth="300"
                  >
                    <template slot="header">
                      <span>{{ item.name }}</span>
                      <el-tooltip
                        :content="
                          $T(
                            '只能上传xls/xlsx/docx/pdf格式文件,且不超过{0}M',
                            systemCfg.uploadDocSize || '10'
                          )
                        "
                        effect="light"
                      >
                        <i class="el-icon-question ml-J"></i>
                      </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                      <UploadFile
                        :filePath.sync="
                          CetTable_1.data[scope.$index][item.propertyLabel]
                        "
                        :fileSize="systemCfg.uploadDocSize"
                        :fileTypes_in="['xls', 'xlsx', 'docx', 'pdf']"
                      />
                    </template>
                  </ElTableColumn>
                  <ElTableColumn
                    v-else-if="item.type === 'pic'"
                    :key="item.propertyLabel"
                    headerAlign="left"
                    align="left"
                    showOverflowTooltip
                    minWidth="300"
                  >
                    <template slot="header">
                      <span>{{ item.name }}</span>
                      <el-tooltip
                        :content="
                          $T(
                            '只能上传jpg/png图片，且不超过{0}M，推荐80px*80px大小。',
                            systemCfg.uploadPicSize
                          )
                        "
                        effect="light"
                      >
                        <i class="el-icon-question ml-J"></i>
                      </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                      <UploadFile
                        :filePath.sync="
                          CetTable_1.data[scope.$index][item.propertyLabel]
                        "
                        :fileSize="systemCfg.uploadPicSize"
                        :fileTypes_in="['jpg', 'png', 'JPG', 'PNG']"
                      />
                    </template>
                  </ElTableColumn>
                  <!-- 经纬度 -->
                  <ElTableColumn
                    v-else-if="item.type === 'latitude'"
                    :key="item.propertyLabel"
                    headerAlign="left"
                    align="left"
                    showOverflowTooltip
                    :minWidth="en ? 660 : 500"
                  >
                    <template slot="header">
                      <span>{{ $T("经纬度") }}</span>
                    </template>
                    <template slot-scope="scope">
                      <div class="flex flex-row">
                        <div class="geography flex flex-row">
                          <span class="label">{{ $T("经度") }}</span>
                          <ElInputNumber
                            class="inputNumber"
                            :disabled="true"
                            v-model="CetTable_1.data[scope.$index].latitude"
                            v-bind="ElInputNumber_Float2"
                            v-on="ElInputNumber_Float2.event"
                          ></ElInputNumber>
                        </div>
                        <div class="geography flex flex-row ml-J1">
                          <span class="label">{{ $T("纬度") }}</span>
                          <ElInputNumber
                            class="inputNumber"
                            :disabled="true"
                            v-model="CetTable_1.data[scope.$index].longitude"
                            v-bind="ElInputNumber_Float2"
                            v-on="ElInputNumber_Float2.event"
                          ></ElInputNumber>
                        </div>
                        <div class="geographyHandle">
                          <span
                            @click.stop="
                              OpenBaiduMap(CetTable_1.data[scope.$index])
                            "
                            class="text-ZS cursor-pointer ml-J1"
                          >
                            {{ $T("选择地点") }}
                          </span>
                          <span
                            @click.stop="
                              delteleMap(CetTable_1.data[scope.$index])
                            "
                            class="text-Sta3 cursor-pointer ml-J1"
                          >
                            {{ $T("重置") }}
                          </span>
                        </div>
                      </div>
                    </template>
                  </ElTableColumn>
                </template>

                <!-- 设备运行区间录入，只有冷水主机需要录入 -->
                <!-- 
                  批量暂不实现【设备运行区间、运行效率曲线】,后续汽车集成的时候再进行适配
                  v-if="ElSelect_1.value === 'coldwatermainengine'"
                -->
                <ElTableColumn
                  v-if="false"
                  headerAlign="left"
                  align="left"
                  showOverflowTooltip
                  minWidth="200"
                >
                  <template slot="header">
                    <span>{{ $T("设备运行区间录入") }}</span>
                    <el-tooltip
                      :content="$T('用于制冷系统AI优化分析')"
                      effect="light"
                    >
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <CetButton
                      v-bind="CetButton_range"
                      @statusTrigger_out="
                        CetButton_range_statusTrigger_out(
                          CetTable_1.data[scope.$index]
                        )
                      "
                    ></CetButton>
                  </template>
                </ElTableColumn>
                <ElTableColumn
                  v-if="false"
                  headerAlign="left"
                  align="left"
                  showOverflowTooltip
                  minWidth="200"
                >
                  <template slot="header">
                    <span>{{ $T("理论运行效率曲线") }}</span>
                    <el-tooltip
                      :content="
                        $T(
                          '只能上传xls/xlsx格式文件,且不超过{0}M',
                          systemCfg.uploadDocSize || 10
                        )
                      "
                      effect="light"
                    >
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <div class="flex flex-row">
                      <el-upload
                        class="elupload11"
                        ref="curveElupload"
                        action=""
                        :headers="{ Authorization: token }"
                        :on-change="
                          (file, fileList) =>
                            handleBeforeUpload3(
                              file,
                              fileList,
                              CetTable_1.data[scope.$index]
                            )
                        "
                        :on-remove="handleRemove(CetTable_1.data[scope.$index])"
                        :multiple="false"
                        :on-exceed="handleExceed"
                        :auto-upload="false"
                        accept=".xls,.xlsx"
                        :file-list="CetTable_1.data[scope.$index].fileList"
                      >
                        <el-button
                          class="text-left"
                          size="small"
                          type="primary"
                        >
                          {{ $T("上传") }}
                        </el-button>
                      </el-upload>
                      <!-- 下载运行效率曲线模板 -->
                      <CetButton
                        class="ml-J3"
                        v-bind="CetButton_download"
                        v-on="CetButton_download.event"
                        @statusTrigger_out="
                          CetButton_download_statusTrigger_out(
                            CetTable_1.data[scope.$index]
                          )
                        "
                      ></CetButton>
                    </div>
                  </template>
                </ElTableColumn>
                <ElTableColumn
                  :label="$T('操作')"
                  width="100"
                  header-align="left"
                  align="left"
                  fixed="right"
                >
                  <template slot-scope="scope">
                    <span
                      v-if="
                        scope.$index !== CetTable_1.data.length - 1 ||
                        scope.$index === 19
                      "
                      @click.stop="deletehHandle(scope)"
                      class="text-Sta3 cursor-pointer"
                    >
                      {{ $T("删除") }}
                    </span>
                  </template>
                </ElTableColumn>
              </CetTable>
            </CetForm>
          </div>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          class="mr-J1"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <!-- 设备运行区间录入 -->
    <AddWorkRange
      v-bind="addWorkRange"
      v-on="addWorkRange.event"
    ></AddWorkRange>
    <MapLatOrLng
      :visibleTrigger_in="MapLatOrLng.visibleTrigger_in"
      :closeTrigger_in="MapLatOrLng.closeTrigger_in"
      :queryId_in="MapLatOrLng.queryId_in"
      :inputData_in="MapLatOrLng.inputData_in"
      :mapInfo="MapLatOrLng.mapInfo"
      @finishData_out="MapLatOrLng_finishData_out"
    />
  </div>
</template>

<script>
import { getFields } from "@/utils/projectTreeField.js";
import common from "eem-base/utils/common";
import AddWorkRange from "../addProjectConfig/addWorkRange.vue";
import MapLatOrLng from "../addProjectConfig/MapLatOrLng.vue";
import customApi from "@/api/custom";
import UploadFile from "../components/UploadFile";
import omegaI18n from "@omega/i18n";
export default {
  name: "batchAdd",
  props: {
    visibleTrigger_in: Number,
    currentNode_in: Object,
    netWork: Boolean,
    projectId_in: Number
  },
  components: {
    AddWorkRange,
    MapLatOrLng,
    UploadFile
  },
  computed: {
    fieldList() {
      if (!this.ElSelect_1.value) return [];
      const arr = this.ElSelect_1.value.split("_");
      const fields = getFields();
      const item = fields.find(item => {
        if (arr[0] === "room") {
          const nodeSubType = Number(arr[1]);
          return item.modelLabel === arr[0] && item.roomType === nodeSubType;
        } else {
          return item.modelLabel === arr[0];
        }
      });
      if (!item) return [];
      const nodeFields = item.node_fields.filter(i => i.type !== "nodeType");

      return nodeFields;
    },
    enumerations() {
      return this.$store.state.enumerations;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    token() {
      return this.$store.state.token;
    },
    en() {
      return omegaI18n.locale === "en";
    }
  },
  data() {
    return {
      showFieldList: [],
      tableSelectData: [],
      CetDialog_1: {
        title: $T("批量添加子级"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        doLayoutTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_nodeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      Columns_1: [],
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        labelWidth: "120px",
        labelPosition: "top",
        rules: {},
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElOption_deviceclassification: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_busbarseg: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_product: {
        options_in: [],
        key: "producttype",
        value: "producttype",
        label: "name",
        disabled: "disabled"
      },
      ElOption_boolean: {
        options_in: [
          {
            id: true,
            text: $T("是")
          },
          {
            id: false,
            text: $T("否")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_enums: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        type: "text",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInputNumber_Int: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_Float: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_Float2: {
        ...common.check_numberFloat11,
        value: "",
        placeholder: $T("请选择"),
        controls: false,
        event: {}
      },
      MapLatOrLng: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        rowData: null,
        mapInfo: {
          areaJson: null,
          point: null
        },
        inputData_in: null
      },
      addWorkRange: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        wokeRangeData: [],
        isEdit: false,
        event: {
          saveData_out: this.saveWorkRange
        }
      },
      wokeRangeData: [], // 运行区间数据
      currentDevice: {}, // 新建之后的获取设备id
      fileList: [],
      CetButton_range: {
        visible_in: true,
        disable_in: false,
        title: $T("录入"),
        type: "primary",
        plain: false,
        event: {}
      },
      CetButton_download: {
        visible_in: true,
        disable_in: false,
        title: $T("下载模板"),
        plain: true,
        event: {}
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.init();
      this.CetDialog_1.openTrigger_in = val;
    }
  },
  methods: {
    async init() {
      await this.setNodeTypes();
      this.nodeTypeChange();
      await this.$nextTick();
      if (!this.$refs.cetTable) return;
      const dom = this.$refs.cetTable.$el.querySelector(
        ".el-table__body-wrapper"
      );
      dom?.scrollTo(0, 0);
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = Date.now();
    },
    CetButton_delete_statusTrigger_out() {
      const rowIndexs = this.tableSelectData.map(i => i.rowIndex);
      let newTableData = this.CetTable_1.data.filter((item, index) => {
        return !rowIndexs.includes(index);
      });
      this.CetTable_1.data = newTableData;
    },
    async CetForm_1_saveData_out() {
      const params = this.getParams();
      if (!params) return;
      const res = await customApi.nodeBatchAdd(params);
      if (res.code !== 0) return;
      this.$message.success($T("操作成功"));
      this.$emit("save_out");
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    getParams() {
      // 检查名称是否重复
      const dataList = this._.cloneDeep(this.CetTable_1.data);
      if (dataList?.length < 2) {
        this.$message.warning($T("最少填写一条"));
        return;
      }
      if (this._.uniqBy(dataList, "name").length !== dataList.length) {
        this.$message.warning($T("节点名称重复！"));
        return;
      }
      const flag = this.checkInitData(dataList[dataList.length - 1]);
      if (flag) {
        dataList.splice(dataList.length - 1, 1);
      }
      let validateFlag = true;
      dataList.forEach(item => {
        if (!validateFlag) return;
        // 检查时间选择关系
        let cooperativedeadline = item.cooperativedeadline
            ? new Date(item.cooperativedeadline).getTime()
            : null,
          commissiondate = item.commissiondate
            ? new Date(item.commissiondate).getTime()
            : null,
          manufacturedate = item.manufacturedate
            ? new Date(item.manufacturedate).getTime()
            : null;
        const lastoverhauldate = item.lastoverhauldate;
        const nextoverhauldate = item.nextoverhauldate;
        if (lastoverhauldate && nextoverhauldate) {
          if (lastoverhauldate >= nextoverhauldate) {
            this.$message.warning($T("下次检修时间不能小于等于上次检修日期！"));
            validateFlag = false;
            return;
          }
        }
        if (cooperativedeadline && commissiondate) {
          if (commissiondate >= cooperativedeadline) {
            this.$message.warning($T("合作截止时间不能小于等于投运时间！"));
            validateFlag = false;
            return;
          }
        }
        if (manufacturedate && commissiondate) {
          if (manufacturedate >= commissiondate) {
            this.$message.warning($T("投运时间不能小于等于出厂时间！"));
            validateFlag = false;
            return;
          }
        }

        if (this.hasProp(item, "cooperativedeadline")) {
          item.cooperativedeadline = cooperativedeadline;
        }
        if (this.hasProp(item, "commissiondate")) {
          item.commissiondate = commissiondate;
        }
        if (this.hasProp(item, "manufacturedate")) {
          item.manufacturedate = manufacturedate;
        }

        // 取字段默认value
        this.fieldList.forEach(item => {
          if (
            this.hasProp(item, "saveDefaultValue") &&
            !item[item.propertyLabel]
          ) {
            item[item.propertyLabel] = item.saveDefaultValue;
          }
        });

        // 补上父节点
        item.children = [
          {
            id: this.currentNode_in.id,
            modelLabel: this.currentNode_in.modelLabel
          }
        ];
        // 房间节点补上房间类型
        const arr = this.ElSelect_1.value.split("_");
        item.modelLabel = arr[0];
        if (item.modelLabel === "room") {
          let roomType = this._.get(arr, "[1]", null);
          if (roomType && roomType === "null") {
            roomType = null;
          } else if (roomType) {
            roomType = Number(roomType);
          }
          item.roomtype = roomType;
        }
        // 经纬度如果是undefined 则转成null
        if (item.longitude === undefined) {
          item.longitude = null;
        }
        if (item.latitude === undefined) {
          item.latitude = null;
        }
        // 将undefined转为null
        Object.keys(item).forEach(i => {
          if (item[i] === undefined) {
            item[i] = null;
          }
        });
      });
      if (!validateFlag) return;
      return dataList;
    },
    deletehHandle({ $index }) {
      this.CetTable_1.data.splice($index, 1);
    },
    // 失去焦点时需要新增在最后新增一条空数据
    addItem(prop) {
      if (this.$refs?.cetForm?.$refs?.cetForm) {
        this.$refs.cetForm.$refs.cetForm.validateField(prop);
      }
      if (this.CetTable_1.data?.length >= 20) return;
      const addData = {};
      this.fieldList.forEach(item => {
        if (this.hasProp(item, "defaultValue")) {
          addData[item.propertyLabel] = item.defaultValue;
        }
      });
      const lastData = this.CetTable_1.data[this.CetTable_1.data.length - 1];
      if (!lastData) {
        this.CetTable_1.data.push(addData);
        return;
      }
      const flag = this.checkInitData(lastData);
      if (!flag) {
        this.CetTable_1.data.push(addData);
      }
    },
    // 检查是否是初始化的数据
    checkInitData(data) {
      let flag = true;
      this.fieldList.forEach(item => {
        if (this.hasProp(item, "defaultValue")) {
          if (data[item.propertyLabel] !== item.defaultValue) {
            flag = false;
          }
        } else if (data[item.propertyLabel]) {
          flag = false;
        }
      });
      return flag;
    },
    hasProp(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key);
    },
    // 找可新建/编辑的节点类型
    async setNodeTypes() {
      const queryData = {
        nodeTreeGroupId: this.netWork ? 1 : 2,
        parentLabel: this.currentNode_in?.modelLabel,
        parentSubType: this.currentNode_in?.roomtype ?? null
      };
      const res = await customApi.findTreeConfigNode(queryData);
      const nodeRelationList = res.data?.[0].nodeRelationList ?? [];

      let options = [];
      nodeRelationList.forEach(item => {
        const optionsItem = {
          id:
            item.nodeType === "room"
              ? `${item.nodeType}_${item.nodeSubType || null}`
              : item.nodeType,
          text: item.nodeSubTypeName || item.nodeTypeName
        };
        options.push(optionsItem);
      });

      this.ElOption_nodeType.options_in = options;
      this.ElSelect_1.value = options?.[0]?.id;
    },
    // 节点类型切换将表头切换，并清除表格数据
    async nodeTypeChange() {
      this.CetTable_1.data = [];
      this.CetForm_1.resetTrigger_in = new Date().getTime();
      this.addItem();
      this.initElOption();
      this.showFieldList = [];
      await this.$nextTick();
      this.fieldsHandle();
      this.CetTable_1.doLayoutTrigger_in = Date.now();
    },
    // 其他自定义类型需要请求接口获取选项
    async initElOption() {
      if (this.fieldList.find(i => i.type === "energytype")) {
        this.ElOption_energytype.options_in = await this.projectEnergy();
      }
      if (this.fieldList.find(i => i.type === "deviceclassification")) {
        this.ElOption_deviceclassification.options_in =
          await this.getDeviceclassification();
      }
      if (this.fieldList.find(i => i.type === "busbarseg")) {
        this.ElOption_busbarseg.options_in = await this.getBusbarseg();
      }
      if (this.fieldList.find(i => i.type === "product")) {
        this.ElOption_product.options_in = await this.getProduct();
      }
      const customEnumsFieldList = this.fieldList.filter(item => {
        return item.type === "customEnums";
      });
      const promiseAll = customEnumsFieldList.map(item => {
        return new Promise(res => {
          if (this._.isArray(item.customEnums)) {
            res(item.customEnums);
          } else {
            item.customEnums().then(data => {
              res(data);
            });
          }
        });
      });
      const customEnumsAll = await Promise.all(promiseAll);
      customEnumsFieldList.forEach((item, index) => {
        item.customEnums_ElOption = customEnumsAll[index];
      });
    },
    fieldsHandle() {
      const nodeFields = this._.cloneDeep(this.fieldList);

      // 处理存在show的字段
      let filterFieldsObj = {};
      nodeFields.forEach(item => {
        if (!item.show) return;
        if (!filterFieldsObj[item.propertyLabel]) {
          filterFieldsObj[item.propertyLabel] = [item];
        } else {
          filterFieldsObj[item.propertyLabel].push(item);
        }
      });
      const copyNodeFields = [];
      nodeFields.forEach(item => {
        if (!filterFieldsObj[item.propertyLabel]) {
          copyNodeFields.push(item);
          return;
        }
        item.showFn = true;
        if (
          !copyNodeFields.find(
            field => field.propertyLabel === item.propertyLabel
          )
        ) {
          item.showFields = filterFieldsObj[item.propertyLabel];
          copyNodeFields.push(item);
        }
      });

      // 将必填项排到前面去
      copyNodeFields.sort((a, b) => {
        const aRequired = this.isRequired(a);
        const bRequired = this.isRequired(b);
        // 将 Required 为 true 的排在前面
        if (aRequired && !bRequired) {
          return -1;
        }
        // 将 Required 为 false 的排在后面
        if (!aRequired && bRequired) {
          return 1;
        }
        // 如果两者相同，则保持原顺序
        return 0;
      });
      this.showFieldList = copyNodeFields;
    },
    async projectEnergy() {
      const res = await customApi.queryProjectEnergyList();
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getDeviceclassification() {
      const res = await customApi.deviceClassification();
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getBusbarseg() {
      const params = {
        parentNode: {
          modelLabel: "project",
          id: this.projectId_in
        },
        subLabelList: ["busbarsection"]
      };

      const res = await customApi.queryChildNodeByLabel(params);
      if (res.code !== 0) return [];
      return this._.get(res.data, "[0].children", []);
    },
    async getProduct() {
      const res = await customApi.queryProductList();
      if (res.code !== 0) return [];
      return this._.get(res, "data", []);
    },

    CetButton_range_statusTrigger_out(data) {
      this.addWorkRange.inputData_in = data;
      this.addWorkRange.wokeRangeData = this._.cloneDeep(
        data.wokeRangeData || []
      );
      this.addWorkRange.openTrigger_in = new Date().getTime();
    },
    saveWorkRange(val) {
      this.addWorkRange.inputData_in.wokeRangeData = this._.cloneDeep(val);
      this.addWorkRange.closeTrigger_in = new Date().getTime();
    },
    // 运行效率曲线模板下载
    CetButton_download_statusTrigger_out(row) {
      let url = "eem-base/v1/parameterConfig/exportOperatingEfficiencyCurve";
      let data = {
        ratedrefrigeration: row?.ratedrefrigeration
      };
      common.downExcel(url, data, this.token);
    },
    isRequired(field) {
      if (!field.validator && !field.rules) return false;
      const rules = [];
      if (field.rules) {
        rules.push(...field.rules);
      }
      if (field.validator) {
        const validatorRules = field.validator();
        rules.push(...validatorRules);
      }
      return !!rules.find(i => i.required);
    },
    // 判断下拉框是否添加清空功能
    clearSelect(val) {
      let arr = val.validator ? val.validator() : val.rules || [];
      return !arr.find(item => item.required === true);
    },
    nameTooltip(field) {
      if (!field.nameTooltip && !field.noEdit) return;
      let str = field.nameTooltip ?? "";
      if (str && field.noEdit) str += `(${$T("创建后不可修改")})`;
      if (!str) str = $T("创建后不可修改");
      return str;
    },
    // 运行效率曲线上传
    handleBeforeUpload3(file, fileList, row) {
      if (
        file.name.indexOf(".xls") === -1 &&
        file.name.indexOf(".xlsx") === -1
      ) {
        row.fileList = [];
        this.$message({
          type: "warning",
          message: $T("只能上传xls/xlsx格式文件")
        });
        return;
      }

      const uploadDocSize = this.systemCfg.uploadDocSize || 10;
      const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
      if (!isLimit100M) {
        row.fileList = [];
        this.$message.error($T("上传文件超过规定的最大上传大小"));
      } else {
        row.fileList = [file];
      }
      return isLimit100M;
    },
    // 文档相关
    handleExceed() {
      this.$message.warning($T("当前限制选择 1 个文件"));
    },
    handleRemove(row) {
      row.fileList = [];
    },
    OpenBaiduMap(row) {
      this.MapLatOrLng.mapInfo.areaJson = row.locationrange;
      this.MapLatOrLng.mapInfo.point = {
        latitude: row.latitude,
        longitude: row.longitude
      };
      this.MapLatOrLng.rowData = row;
      this.MapLatOrLng.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    delteleMap(row) {
      row.longitude = undefined;
      row.latitude = undefined;
      row.locationrange = "";
    },
    // 地图相关
    MapLatOrLng_finishData_out(val) {
      if (!val) {
        return;
      }
      this.$set(this.MapLatOrLng.rowData, "longitude", val.lng);
      this.$set(this.MapLatOrLng.rowData, "latitude", val.lat);
      this.MapLatOrLng.rowData.locationrange = val.areaJson;
    },
    geographyFormat(val) {
      if (val == null) return;
      return val.toFixed(6);
    },
    handleSelectionChange(val) {
      this.tableSelectData = val;
    },
    getRules(field, index) {
      const dataList = this.CetTable_1.data;
      const flag = this.checkInitData(dataList[dataList.length - 1]);
      if (flag && index === this.CetTable_1.data.length - 1) return [];
      if (field.validator) return field.validator(this.CetTable_1.data[index]);
      const rules = this._.cloneDeep(field.rules) || [];
      if (field.propertyLabel === "name") {
        // 加上不能重名校验
        const validateName = (rule, value, callback) => {
          const props = [];
          this.CetTable_1.data.forEach((item, index) => {
            if (item.name === value) {
              props.push(`[${index}].${item.propertyLabel}`);
            }
          });
          if (props?.length > 1) {
            callback(new Error($T("名称重复")));
          } else {
            callback();
          }
        };
        rules.push({ validator: validateName, trigger: ["blur", "change"] });
      }
      return rules;
    },
    async itemDataChange(field, scope) {
      if (this.$refs?.cetForm?.$refs?.cetForm) {
        const prop = `[${scope.$index}].${field.propertyLabel}`;
        this.$refs.cetForm.$refs.cetForm.validateField(prop);
      }
      field.change && field.change(this.CetTable_1.data[scope.$index]);
    }
  }
};
</script>

<style lang="scss" scoped>
.required {
  @include font_color(Sta3);
}
.tableHeader {
  .title {
    font-weight: bold;
    @include font_size(H3);
    font-size: var(H3);
    line-height: 1.5;
  }
}
.cetForm {
  height: 441px;
}

.uploadImg {
  width: 70px;
  height: 50px;
}
.geography {
  flex: 1;
  display: inline-flex;
  line-height: 30px;
  position: relative;
  .label {
    padding-right: var(--J1);
    padding-left: var(--J1);
    border-radius: 4px 0 0 4px;
    border: 1px solid;
    border-color: var(--B1);
    display: inline-block;
  }
  .inputNumber {
    flex: 1;
  }
}
.geographyHandle {
  flex: 1;
  line-height: 32px;
}
.cetForm :deep() {
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
  .el-table .cell.el-tooltip {
    overflow: initial;
  }
  .el-textarea .el-input__count {
    bottom: 1px;
    height: 30px;
  }
  .el-form-item__error {
    z-index: 1;
  }
  .el-table__body tr > td {
    padding: 8px 0 23px 0;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
}
</style>
