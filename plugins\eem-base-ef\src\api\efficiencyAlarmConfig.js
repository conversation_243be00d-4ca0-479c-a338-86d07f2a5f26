import fetch from "eem-base/utils/fetch";

// 获取能效指标数据
export function alarmIndexData(params) {
  return fetch({
    url: `/eem-service/v1/alarm/indexData`,
    method: "GET",
    params
  });
}

// 获取能效报警方案
export function alarmSchemeData(data) {
  return fetch({
    url: `/eem-service/v1/alarm/alarmScheme`,
    method: "POST",
    data
  });
}

// 保存能效报警方案
export function saveAlarmSchemeData(data) {
  return fetch({
    url: `/eem-service/v1/alarm/saveAlarmSchemes`,
    method: "POST",
    data
  });
}
