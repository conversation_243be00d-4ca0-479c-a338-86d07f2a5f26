<template>
  <div class="h-full flex flex-row">
    <div
      class="w-[315px] bg-BG1 rounded-Ra p-J4 box-border mr-J3 flex flex-col"
    >
      <div class="text-H1 mb-J3 font-bold">{{ $T("选择对象") }}</div>
      <customElSelect
        v-model="ElSelect_treeType.value"
        v-bind="ElSelect_treeType"
        v-on="ElSelect_treeType.event"
        class="mb-J3"
        :prefix_in="$T('节点树类型')"
        v-if="multidimensional"
      >
        <ElOption
          v-for="item in ElOption_treeType.options_in"
          :key="item[ElOption_treeType.key]"
          :label="item[ElOption_treeType.label]"
          :value="item[ElOption_treeType.value]"
          :disabled="item[ElOption_treeType.disabled]"
        ></ElOption>
      </customElSelect>
      <CetTree
        class="flex-auto"
        :key="treeKey"
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
      ></CetTree>
    </div>
    <div
      class="w-[200px] bg-BG1 rounded-Ra p-J4 box-border mr-J3 flex flex-col"
    >
      <div class="text-H1 mb-J3 font-bold">{{ $T("选择指标") }}</div>
      <CetTree
        class="flex-auto"
        :key="indexTreeKey"
        :selectNode.sync="CetTree_2.selectNode"
        :checkedNodes.sync="CetTree_2.checkedNodes"
        v-bind="CetTree_2"
        v-on="CetTree_2.event"
      ></CetTree>
    </div>
    <div class="flex-auto bg-BG1 rounded-Ra p-J4 box-border flex flex-col">
      <div class="text-H1 mb-J3 font-bold">{{ $T("配置报警阈值") }}</div>
      <div class="flex flex-row justify-between items-center mb-J3">
        <div class="flex flex-row items-center">
          <el-radio-group
            v-model="saveData.thresholdgeneratemethod"
            @change="thresholdgeneratemethodChange"
            class="mr-J3"
          >
            <el-radio-button :label="1">
              {{ $T("Al智能报警设置") }}
            </el-radio-button>
            <el-radio-button :label="2">
              {{ $T("自定义报警设置") }}
            </el-radio-button>
          </el-radio-group>
          <div
            v-if="saveData.thresholdgeneratemethod === 1"
            class="text-Aa bg-Sta2 p-J0 rounded-Ra text-T5"
          >
            {{
              $T(
                "默认由软件自动设置动态阈值AI智能模式；月阈值提前一个月给出，日阈值提前一天给出"
              )
            }}
          </div>
          <div
            v-if="saveData.thresholdgeneratemethod === 2"
            class="text-Aa bg-Sta2 p-J0 rounded-Ra text-T5"
          >
            {{ $T("选择自定义模式，系统判定停用AI智能模式阈值计划！") }}
          </div>
        </div>

        <CetButton
          v-bind="CetButton_grade"
          v-on="CetButton_grade.event"
        ></CetButton>
      </div>
      <div class="flex-auto">
        <AiAlarmConfig
          v-show="saveData.thresholdgeneratemethod === 1"
          ref="aiAlarmConfig"
          v-bind="aiAlarmConfig"
        />
        <CustomAlarmConfig
          v-show="saveData.thresholdgeneratemethod === 2"
          ref="customAlarmConfig"
          v-bind="customAlarmConfig"
        />
      </div>
      <div class="mt-J3 flex flex-row justify-between items-center">
        <div class="flex flex-row items-center">
          <template v-if="saveData.thresholdgeneratemethod === 1">
            <customElSelect
              :prefix_in="$T('收敛')"
              v-model="saveData.convergence"
              :placeholder="$T('请选择')"
              class="w-[120px] mr-J3"
            >
              <el-option :label="$T('是')" :value="true"></el-option>
              <el-option :label="$T('否')" :value="false"></el-option>
            </customElSelect>
            <customElSelect
              v-model="saveData.limittype"
              :placeholder="$T('请选择')"
              :prefix_in="$T('越限类型')"
              class="w-[200px] mr-J3"
            >
              <el-option :label="$T('越上限')" :value="1"></el-option>
              <el-option :label="$T('越下限')" :value="2"></el-option>
            </customElSelect>
          </template>

          <el-checkbox v-model="saveData.isalarm">
            {{
              saveData.thresholdgeneratemethod === 2
                ? $T("启用报警指标（不勾选默认不产生事件推送）")
                : $T("启用报警")
            }}
          </el-checkbox>
        </div>
        <el-button class="fr mlJ1" type="primary" @click="saveAlarmConfig">
          {{ $T("保存") }}
        </el-button>
      </div>
    </div>
    <EventWarningGradeConfig
      v-bind="eventWarningGradeConfig"
      v-on="eventWarningGradeConfig.event"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import AiAlarmConfig from "./aiAlarmConfig.vue";
import CustomAlarmConfig from "./customAlarmConfig.vue";
import EventWarningGradeConfig from "./EventWarningGradeConfig.vue";
export default {
  name: "efficiencyAlarmConfig",
  components: {
    AiAlarmConfig,
    CustomAlarmConfig,
    EventWarningGradeConfig
  },
  computed: {
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      treeKey: 1,
      indexTreeKey: 1,
      currentNode: null,
      actionIndex: null,
      saveData: {
        thresholdgeneratemethod: 1,
        convergence: true,
        limittype: 1,
        isalarm: true,
        id: 0
      },
      ElSelect_treeType: {
        value: -1,
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300)
        }
      },
      CetTree_2: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this._.debounce(this.CetTree_2_currentNode_out, 300)
        }
      },
      CetButton_grade: {
        visible_in: true,
        disable_in: false,
        title: $T("报警等级设置"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_grade_statusTrigger_out
        }
      },
      customAlarmConfig: {
        data_in: null
      },
      aiAlarmConfig: {
        data_in: null
      },
      eventWarningGradeConfig: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          alarmlevelconfig_model_out: this.alarmlevelconfig_model_out
        }
      }
    };
  },
  methods: {
    async init() {
      await this.queryTreeType();
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    /**
     * 查询树类型
     */
    async queryTreeType() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      if (res.code !== 0) {
        return;
      }
      const data = res?.data || [
        {
          id: -1,
          name: $T("固定管理层级")
        }
      ];
      this.ElOption_treeType.options_in = data;
      const flag = data.some(i => i.id === -1);
      this.ElSelect_treeType.value = flag ? -1 : data?.[0]?.id ?? null;
    },
    /**
     * 树类型切换
     */
    ElSelect_treeType_change_out(val) {
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    /**
     * 获取固定管理层级树
     */
    async getTreeData1() {
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype",
                  tagid: 2
                }
              ]
            },
            modelLabel: "room"
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "airconditioner" },
          { modelLabel: "virtualbuildingnode" }
        ],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTreeSimple(data);
      if (res.code !== 0) {
        return;
      }
      const treeData = res.data || [];
      this.CetTree_1.inputData_in = treeData;
      this.CetTree_1.selectNode = treeData[0];
      this.treeKey++;
    },

    /**
     * 获取多维度树
     */
    async getTreeData2() {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value
      };
      const res = await customApi.getEfNodeTreeAnalysis(queryData);
      if (res.code !== 0) {
        return;
      }
      const treeData = res.data || [];
      this.CetTree_1.inputData_in = treeData;
      this.CetTree_1.selectNode = treeData[0];
      this.treeKey++;
    },
    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.currentNode = val;
      this.getIndexData();
    },
    /**
     * 获取能效指标
     */
    async getIndexData() {
      const queryData = {
        modelId: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };
      const response = await customApi.alarmIndexData(queryData);
      if (response.code !== 0) {
        return;
      }

      const data = response.data || [];
      this.CetTree_2.inputData_in = data;
      this.CetTree_2.selectNode = data[0];
      this.indexTreeKey++;
    },
    /**
     * 切换指标
     */
    CetTree_2_currentNode_out(val) {
      this.actionIndex = val;
      this.getAlarmScheme();
    },
    /**
     * 获取方案详情
     */
    async getAlarmScheme() {
      if (!this.actionIndex || !this.currentNode) {
        this.initSchemeConfig(1);
        return;
      }

      const queryData = {
        alarmType: 2,
        energyType: this.actionIndex.energytype,
        indexId: this.actionIndex.id,
        nodeId: this.currentNode.id,
        nodeLabel: this.currentNode.modelLabel,
        projectId: this.projectId
      };
      const res = await customApi.alarmSchemeData(queryData);
      if (res.code !== 0) {
        return;
      }
      this.handleScheme(res.data[0]);
    },
    handleScheme(data) {
      if (!data) {
        this.initSchemeConfig(1);
        return;
      }
      const {
        thresholdgeneratemethod,
        convergence,
        id,
        isalarm,
        alarmlevelconfig_model
      } = data;

      this.saveData = {
        thresholdgeneratemethod,
        convergence,
        isalarm,
        alarmlevelconfig_model,
        id
      };

      const limittype = data.benchmarkset_model?.[0]?.limittype ?? 1;
      const customData = {
        name: data.name,
        limittype,
        limitvalue: data.benchmarkset_model?.[0]?.limitvalue,
        id: data.benchmarkset_model?.[0]?.id
      };
      this.customAlarmConfig.data_in = customData;
      this.aiAlarmConfig.data_in = {
        name: data.name,
        benchmarkset_model: data.benchmarkset_model
      };
    },
    /**
     * 重置预警配置
     * @param {Number} thresholdgeneratemethod 方案类型
     */
    initSchemeConfig(thresholdgeneratemethod) {
      this.saveData = {
        thresholdgeneratemethod: thresholdgeneratemethod,
        convergence: true,
        limittype: 1,
        isalarm: true,
        alarmlevelconfig_model: [
          {
            alarmcolorset_id: 1,
            alarmlevel: $T("报警等级一"),
            isactive: false,
            name: $T("红色报警"),
            rate: 100,
            id: 0
          },
          {
            alarmcolorset_id: 2,
            alarmlevel: $T("报警等级二"),
            isactive: false,
            name: $T("橙色报警"),
            rate: 85,
            id: 0
          },
          {
            alarmcolorset_id: 3,
            alarmlevel: $T("报警等级三"),
            isactive: false,
            name: $T("黄色报警"),
            rate: 80,
            id: 0
          }
        ],
        id: 0
      };
      this.customAlarmConfig.data_in = null;
      this.aiAlarmConfig.data_in = null;
    },
    /**
     * 方案类型切换
     */
    thresholdgeneratemethodChange(val) {
      const text =
        val == 1
          ? $T(
              "您切换到AI智能模式，自定义方案将不再生效，只能选择一种方案配置能效报警计划！"
            )
          : $T(
              "您切换到自定义模式，AI智能报警方案将不再生效，只能选择一种方案配置能效报警计划！"
            );
      const backValue = val == 1 ? 2 : 1;

      this.$confirm(text, $T("尊敬的用户："), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false
      })
        .then(() => {
          const oldId = this.saveData.id;
          // 清理已配置数据
          this.initSchemeConfig(val);
          this.saveData.id = oldId;
        })
        .catch(() => {
          this.saveData.thresholdgeneratemethod = backValue;
        });
    },
    /**
     * 打开报警等级配置
     */
    CetButton_grade_statusTrigger_out() {
      this.eventWarningGradeConfig.inputData_in = this._.cloneDeep(
        this.saveData.alarmlevelconfig_model
      );
      this.eventWarningGradeConfig.visibleTrigger_in = Date.now();
    },
    /**
     * 报警等级配置输出
     */
    alarmlevelconfig_model_out(val) {
      this.saveData.alarmlevelconfig_model = val;
    },
    /**
     * 保存方案
     */
    async saveAlarmConfig() {
      let alarmlevelconfig = this.saveData.alarmlevelconfig_model;
      const flag = alarmlevelconfig.some(item => item.isactive);
      if (!flag) {
        this.$confirm($T("是否已勾选报警等级?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            this.saveHandle();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          });
        return;
      }
      this.saveHandle();
    },
    saveHandle() {
      const saveData = {
        alarmtype: 2,
        relatedNodes: [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          }
        ],
        thresholdgeneratemethod: this.saveData.thresholdgeneratemethod,
        id: this.saveData.id,
        project_id: this.projectId,
        efId: this.actionIndex.id,
        energytype: this.actionIndex.energytype,
        benchmarkset_model: [],
        alarmlevelconfig_model: this.saveData.alarmlevelconfig_model,
        convergence: this.saveData.convergence || true,
        notificationmethod: "",
        isalarm: this.saveData.isalarm
      };
      if (this.saveData.thresholdgeneratemethod == 2) {
        this.saveCustomAlarm(saveData);
        return;
      }

      this.saveAiAlarm(saveData);
    },
    /**
     * 保存自定义报警方案
     */
    async saveCustomAlarm(data) {
      const validateData = await this.$refs.customAlarmConfig.validate();
      if (!validateData) return;
      const { id, name, limittype, limitvalue } = validateData;

      const saveData = { ...data, name };
      saveData.benchmarkset_model = [
        {
          id: id,
          name: name,
          limitvalue: limitvalue,
          validtime: new Date().getTime(),
          aggregationcycle: this.actionIndex.aggregationcycle || 17,
          limittype: limittype
        }
      ];
      const res = await customApi.saveAlarmSchemeData(saveData);
      if (res.code !== 0) {
        return;
      }

      this.$message.success($T("保存成功"));
      this.getAlarmScheme();
    },
    /**
     * 保存Ai方案
     */
    async saveAiAlarm(data) {
      const validateData = await this.$refs.aiAlarmConfig.validate();
      if (!validateData) return;
      const { name } = validateData;
      const saveData = { ...data, name };
      const benchmarkset = this.aiAlarmConfig.data_in.benchmarkset_model;
      // if(benchmarkset?.length){
      //   // 后续看是否保留前端传入默认阈值的逻辑
      // }
      saveData.benchmarkset_model = benchmarkset;
      const res = await customApi.saveAlarmSchemeData(saveData);
      if (res.code !== 0) {
        return;
      }

      this.$message.success($T("保存成功"));
      this.getAlarmScheme();
    }
  },
  mounted() {
    this.init();
  }
};
</script>
