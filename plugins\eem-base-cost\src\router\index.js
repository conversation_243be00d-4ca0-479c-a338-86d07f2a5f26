/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "/rateadministration",
        component: () => import("@/projects/rateadministration/index.vue")
      },
      {
        path: "/accountingscheme",
        component: () => import("@/projects/accountingscheme/index.vue")
      },
      {
        path: "/newCostAnalysis",
        component: () => import("@/projects/newCostAnalysis/index.vue")
      },
      {
        path: "/electricityCostAnalysis",
        component: () => import("@/projects/electricityCostAnalysis/index.vue")
      },
      {
        path: "/unitCostAnalysis",
        component: () => import("@/projects/unitCostAnalysis/index.vue")
      },
      {
        path: "/comprehensiveCostAnalysis",
        component: () =>
          import("@/projects/comprehensiveCostAnalysis/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
