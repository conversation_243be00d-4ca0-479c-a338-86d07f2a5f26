import fetch, { hideNoticeFetch } from "eem-base/utils/fetch";
const version = "v1";
const service = "eem-service";
// 拓扑查询的节点树
export function topologyManageTree(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/tree`,
    method: "POST",
    data
  });
}

// 根据节点查询前后拓扑信息
export function topologyManageInfo(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/info`,
    method: "POST",
    data
  });
}

// 拓扑配置的节点树
export function topologyManageTreeLinenode(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/tree/linenode`,
    method: "POST",
    data
  });
}

// 查询母线，变压器，进线馈线的名称列表
export function topologyManageNode(params) {
  return hideNoticeFetch({
    url: `/${service}/${version}/topology/manage/node`,
    method: "POST",
    params
  });
}

// 返回右侧表格限制条数
export function manageLimitImportConfig() {
  return fetch({
    url: `/${service}/${version}/topology/manage/limit/importConfig`,
    method: "GET"
  });
}

// 根据选择节点查询拓扑配置的内容
export function topologyManageConfigInfo(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/config/info`,
    method: "POST",
    data
  });
}

// 编辑拓扑配置
export function topologyManageConfig(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/config`,
    method: "POST",
    data
  });
}

// 预览数据--拓扑配置
export function topologyManagePreview(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/preview`,
    method: "POST",
    data
  });
}

// 拓扑配置的节点树
export function topologyManageBusbarTree(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/busbar/tree`,
    method: "POST",
    data
  });
}

// 根据选择节点查询母联母线配置的内容
export function topologyManageBusbarConfigInfo(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/busbar/config/info`,
    method: "POST",
    data
  });
}

// 编辑母联母线配置
export function topologyManageBusbarConfig(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/busbar/config`,
    method: "POST",
    data
  });
}

// 预览数据--母联配置
export function topologyManageBusbarPreview(data) {
  return fetch({
    url: `/${service}/${version}/topology/manage/busbar/preview`,
    method: "POST",
    data
  });
}

// 查询配电房列表
export function topologyManageRoom(params) {
  return hideNoticeFetch({
    url: `/${service}/${version}/topology/manage/room`,
    method: "POST",
    params
  });
}
