<template>
  <div class="GuideDialog">
    <components
      :is="'CetDialog'"
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      :key="openTrigger_in"
    >
      <el-container></el-container>
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
    </components>
  </div>
</template>
<script>
export default {
  name: "GuideDialog",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    }
  },
  computed: {},
  watch: {},
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新手引导"),
        width: "900px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      CetDrawer: {
        isVisible: false,
        direction: "rtl",
        size: "60%"
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      selectNode: {},
      CetTree: {
        inputData_in: [],
        selectNode: {},
        checkedNodes: [],
        filterNodes_in: null,
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          currentNode_out: this.CetTree_currentNode_out
        }
      }
    };
  },
  methods: {
    onGuideClick(node) {
      this.$emit("useEventHandler", "onGuideClick", node);
    },
    CetButton_cancel_statusTrigger_out() {
      this.$emit("useEventHandler", "onClose");
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetTree_currentNode_out(node) {
      this.selectNode = node;
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="scss" scoped></style>
