<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full">
        <customElSelect
          v-model="ElSelect_treeType.value"
          v-bind="ElSelect_treeType"
          v-on="ElSelect_treeType.event"
          :prefix_in="$T('节点树类型')"
          v-if="multidimensional"
          class="mb-J3"
        >
          <ElOption
            v-for="item in ElOption_treeType.options_in"
            :key="item[ElOption_treeType.key]"
            :label="item[ElOption_treeType.label]"
            :value="item[ElOption_treeType.value]"
            :disabled="item[ElOption_treeType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto cetTree"
          :selectNode.sync="CetTree_leftTree.selectNode"
          :checkedNodes.sync="CetTree_leftTree.checkedNodes"
          :searchText_in.sync="CetTree_leftTree.searchText_in"
          v-bind="CetTree_leftTree"
          v-on="CetTree_leftTree.event"
        >
          <span
            class="custom-tree-node el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
            <div class="icon" v-if="node?.data?.changeStatus">
              <el-tooltip effect="light" :content="effTimeFormat(node.data)">
                <omega-icon symbolId="collect-lin" />
              </el-tooltip>
            </div>
          </span>
        </CetTree>
      </div>
    </template>
    <template #container>
      <div class="h-full flex flex-col">
        <div class="mb-J3 flex flex-row items-center">
          <customElSelect
            class="mr-J1"
            v-model="ElSelect_productType.value"
            v-bind="ElSelect_productType"
            v-on="ElSelect_productType.event"
            :prefix_in="$T('产品类型')"
          >
            <ElOption
              v-for="item in ElOption_productType.options_in"
              :key="item[ElOption_productType.key]"
              :label="item[ElOption_productType.label]"
              :value="item[ElOption_productType.value]"
              :disabled="item[ElOption_productType.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="mr-J1"
            v-model="ElSelect_energyType.value"
            v-bind="ElSelect_energyType"
            v-on="ElSelect_energyType.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_energyType.options_in"
              :key="item[ElOption_energyType.key]"
              :label="item[ElOption_energyType.label]"
              :value="item[ElOption_energyType.value]"
              :disabled="item[ElOption_energyType.disabled]"
            ></ElOption>
          </customElSelect>
          <time-tool
            :typeID="14"
            :val.sync="startTime"
            @change="changeQueryTime"
            :timeType_in="timeType"
            :shortcuts="pickerOptions.shortcuts"
          ></time-tool>
        </div>
        <div class="flex-auto flex flex-col">
          <div class="flex-col flex flex-[5] bg-BG1 rounded-Ra p-J4">
            <div class="mb-J3 text-H3 font-bold">
              {{ $T("单位成本趋势") }}
            </div>
            <CetChart
              class="flex-auto"
              v-bind="CetChart_electrictyCost"
            ></CetChart>
          </div>
          <div class="proportion mt-J3 flex-[4] min-[230px] flex-row flex">
            <div class="flex-auto flex-col flex bg-BG1 rounded-Ra p-J4">
              <div class="mb-J3 text-H3 font-bold">
                {{ $T("单位成本概览") }}
              </div>
              <div class="flex-auto proportion-item avg-cost flex-col flex">
                <div class="cost-item mb-J1">
                  <span>{{ $T("时间") }}:</span>
                  &emsp;
                  <span>{{ selectTime }}</span>
                </div>
                <div class="cost-item mb-J1">
                  <span>{{ $T("单位成本") }}:</span>
                  &emsp;
                  <span>
                    {{ formatNumberWithPrecision(unitCostView, 2) }}
                  </span>
                  <span>{{ tbLabel.unit }}</span>
                </div>
                <div class="flex-auto compare flex flex-col relative">
                  <div class="flex flex-row">
                    <span class="preText flex-auto p-J0 text-T5 bg-BG2">
                      {{ $T("同比") }}
                    </span>
                    <span
                      class="lastText flex-auto p-J0 text-T5 bg-BG2 ml-J3"
                      v-if="queryTime.aggregationCycle === 14"
                    >
                      {{ $T("环比") }}
                    </span>
                  </div>
                  <div class="flex-auto flex-row flex">
                    <div class="relative flex-auto">
                      <CetChart
                        v-bind="CetChart_avgcost1"
                        style="position: absolute; top: 0; left: 0"
                      ></CetChart>
                      <div class="label">
                        <el-tooltip
                          :content="
                            _.isEmpty(tbLabel)
                              ? ''
                              : tbLabel.price + tbLabel.unit
                          "
                          effect="light"
                        >
                          <div class="price text-ellipsis">
                            <span>
                              {{ tbLabel.price || "--" }}
                            </span>
                          </div>
                        </el-tooltip>
                        <div class="percent">
                          <el-tooltip :content="tbLabel.percent" effect="light">
                            <span class="text-ellipsis">
                              {{ tbLabel.percent || "--" }}
                            </span>
                          </el-tooltip>
                          <img :src="tbLabel.src" v-if="tbLabel.src" alt="" />
                        </div>
                      </div>
                    </div>
                    <div
                      class="relative ml-J3 flex-auto"
                      v-if="queryTime.aggregationCycle === 14"
                    >
                      <CetChart
                        v-bind="CetChart_avgcost2"
                        style="position: absolute; top: 0; left: 0"
                      ></CetChart>
                      <div class="label">
                        <el-tooltip
                          :content="
                            _.isEmpty(hbLabel)
                              ? ''
                              : hbLabel.price + hbLabel.unit
                          "
                          effect="light"
                        >
                          <div class="price text-ellipsis">
                            <span>
                              {{ hbLabel.price || "--" }}
                            </span>
                          </div>
                        </el-tooltip>
                        <div class="percent">
                          <el-tooltip :content="hbLabel.percent" effect="light">
                            <span class="text-ellipsis">
                              {{ hbLabel.percent || "--" }}
                            </span>
                          </el-tooltip>
                          <img :src="hbLabel.src" v-if="hbLabel.src" alt="" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex-auto flex-col flex ml-J3 mr-J3 bg-BG1 rounded-Ra p-J4"
            >
              <div class="mb-J3 text-H3 font-bold">
                {{ $T("单位成本占比") }}
              </div>
              <div class="flex-auto proportion-item flex flex-col">
                <div class="cost-item mb-J1">
                  <span>{{ $T("时间") }}:</span>
                  &emsp;
                  <span>{{ selectTime }}</span>
                </div>
                <CetChart
                  v-if="CetChart_energyCost.options.series[0].data.length"
                  v-bind="CetChart_energyCost"
                  class="flex-auto"
                ></CetChart>
                <div class="itemBox-empty flex-auto" v-else>
                  {{ $T("暂无数据") }}
                </div>
              </div>
            </div>
            <div class="flex-auto flex-col flex bg-BG1 rounded-Ra p-J4">
              <div class="mb-J3 text-H3 font-bold">
                {{ $T("单位成本") }} TOP5
              </div>
              <div class="flex-auto proportion-item flex flex-col">
                <div class="cost-item mb-J1">
                  <span>{{ $T("时间") }}:</span>
                  &emsp;
                  <span>{{ selectTime }}</span>
                </div>
                <template v-if="costList.length">
                  <div class="top-unit mb-J1">
                    <span class="fr">
                      <span>{{ $T("单位") }}:</span>
                      &emsp;
                      <span>{{ costList[0].unit }}</span>
                    </span>
                  </div>
                  <div class="top flex-auto">
                    <div
                      class="top-item"
                      v-for="(item, index) in costList"
                      :key="index"
                    >
                      <div class="label fl mr-J1 text-ellipsis fullheight">
                        {{ item.objectName }}
                      </div>
                      <el-tooltip
                        :content="
                          item.objectName +
                          ':' +
                          formatNumberWithPrecision(item.value, 2) +
                          item.unit
                        "
                        effect="light"
                      >
                        <div class="costVal fl fullheight">
                          <div
                            class="item fullheight"
                            :style="{
                              width: (item.value / costMax) * 100 + '%',
                              background: colors[index]
                            }"
                          ></div>
                          <span
                            class="text-ellipsis ml-J0"
                            :style="{
                              left: (item.value / costMax) * 100 + '%'
                            }"
                          >
                            {{ formatNumberWithPrecision(item.value, 2) }}
                          </span>
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
                <div class="itemBox-empty flex-auto" v-else>
                  {{ $T("暂无数据") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CetAside>
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import TimeTool from "@/components/TimeTool.vue";
import omegaTheme from "@omega/theme";
import omegaI18n from "@omega/i18n";
const isLightTheme = omegaTheme.theme === "light";
let colors = ["#0d86ff", "#19e9e9", "#26ec86", "#2485c0", "#8680f6"];
if (isLightTheme) {
  colors = ["#29b061", "#9D6630", "#FFC24C", "#2B71C3", "#9B2222"];
}

export default {
  name: "electricityCostAnalysis",
  components: {
    TimeTool
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    costMax() {
      return this._.max(this.costList.map(item => item.value)) * 1.2;
    },
    language() {
      return omegaI18n.locale === "en";
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    }
  },
  data() {
    return {
      colors,
      ElSelect_treeType: {
        value: -1,
        style: {},
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_productType: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        event: {
          change: this.ElSelect_productType_change_out
        }
      },
      ElOption_productType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_energyType: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTree_leftTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_leftTree_currentNode_out
        }
      },
      currentNode: null, // 当前点击树节点
      startTime: new Date().getTime(),
      queryTime: {},
      timeType: [
        {
          type: "month",
          text: $T("月"),
          typeID: 14,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          typeID: 17,
          number: 1,
          unit: "y"
        }
      ],
      CetChart_electrictyCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      CetChart_energyCost: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            type: "scroll",
            left: 10,
            bottom: -5
          },
          tooltip: {
            trigger: "item",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: "65%",
              center: ["50%", "48%"],
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      },
      unitCostView: null, // 单位成本
      tbLabel: {}, // 同比数据
      hbLabel: {}, // 环比数据
      CetChart_avgcost1: {
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      CetChart_avgcost2: {
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      selectTime: "",
      pickerOptions: {
        // 添加回到今天的快捷键
        shortcuts: [
          {
            text: this.$T("当月"),
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          }
        ]
      },
      costList: [] // 单位成本top5列表
    };
  },
  methods: {
    ElSelect_treeType_change_out(val) {
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    filNodeColor(node) {
      const state = this._.get(node, "data.childSelectState", null);
      return state !== 1 ? "#989898" : undefined;
    },
    ElSelect_productType_change_out() {
      this.getAllData();
    },
    ElSelect_energyType_change_out() {
      this.getAllData();
    },

    CetTree_leftTree_currentNode_out: _.debounce(function (val) {
      // 按照张壮要求，在通用3.5迭代中，重新添加成点击弹窗处理
      if (val.childSelectState == 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.currentNode = this._.cloneDeep(val);
      this.getAllData();
    }, 300),
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.queryTime = {
        aggregationCycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const yearStr = this.language ? "YYYY" : "YYYY年";
      this.selectTime =
        timeOption.typeID === 14
          ? date.format(mothStr)
          : timeOption.typeID === 17
          ? date.format(yearStr)
          : "--";
      this.pickerOptions.shortcuts[0].text =
        timeOption.typeID === 14 ? this.$T("当月") : this.$T("今年");
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true);
      } else {
        this.getAllData();
      }
    },
    // 获取节点树类型下拉框
    async queryTreeType() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (
        res?.data || [
          {
            id: -1,
            name: $T("固定管理层级")
          }
        ]
      ).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    getTreeData1() {
      this.CetTree_leftTree.inputData_in = [];
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype",
                  tagid: 1
                }
              ]
            },
            modelLabel: "room"
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "airconditioner" },
          { modelLabel: "virtualbuildingnode" }
        ],
        treeReturnEnable: true
      };
      customApi.getNodeTreeSimple(data).then(res => {
        if (res.code === 0) {
          this.CetTree_leftTree.inputData_in = res.data;
          if (res.data && res.data.length) {
            // 选中第一个有数据 childSelectState = 1 的节点并展开节点
            const obj = this._.find(this.dataTransform(res.data), [
              "childSelectState",
              1
            ]);
            this.CetTree_leftTree.selectNode = obj;
          }
        }
      });
    },
    // 获取维度节点树数据
    async getTreeData2(keepSelectNode) {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        aggregationCycle: this.queryTime.aggregationCycle
      };
      const res = await customApi.queryCostcheckplanMultitree(queryData);
      this.CetTree_leftTree.inputData_in = res?.data || [];
      let obj;
      if (keepSelectNode) {
        obj = this._.find(this.dataTransform(res.data), [
          "tree_id",
          this.currentNode.tree_id
        ]);
      }
      if (!obj) {
        // 选中第一个有数据 childSelectState = 1 的节点并展开节点
        obj = this._.find(this.dataTransform(res.data), [
          "childSelectState",
          1
        ]);
      }
      this.CetTree_leftTree.selectNode = obj;
    },
    // 获取第一个有权限的节点
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    formatNumberWithPrecision: common.formatNumberWithPrecision,
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    // 获取项目产品类型
    async getProductType() {
      this.ElOption_productType.options_in = [];
      const res = await customApi.queryProductList({
        projectId: this.projectId
      });
      if (res.code !== 0) {
        return;
      }
      const selectData = (res.data || []).map(item => {
        return {
          id: item.producttype,
          text: item.name
        };
      });
      this.ElOption_productType.options_in = selectData;
      this.ElSelect_productType.value = selectData[0].id;
    },
    // 获取项目能源类型
    async getProjectEnergy() {
      this.ElOption_energyType.options_in = [];
      const res = await customApi.getProjectEnergy(this.projectId);
      if (res.code !== 0) {
        return;
      }
      const filterEnergyType = await this.getFilterEnergyType();
      let selectData = [];

      res.data.forEach(item => {
        if (!filterEnergyType.includes(item.energytype)) {
          selectData.push({
            id: item.energytype,
            text: item.name
          });
        }
      });
      this.ElOption_energyType.options_in = selectData;
      this.ElSelect_energyType.value = selectData[0].id;
    },
    /**
     * 获取需要过滤能源类型
     */
    async getFilterEnergyType() {
      const res = customApi.organizationConfigFilterEnergyType();
      return res.data || [];
    },

    getAllData() {
      this.CetChart_electrictyCost.options = {};
      this.CetChart_energyCost.options.series[0].data = [];
      this.unitCostView = null;
      this.tbLabel = {};
      this.hbLabel = {};
      this.costList = [];
      if (
        !this.currentNode ||
        !this.ElSelect_productType.value ||
        !this.ElSelect_energyType.value
      )
        return;
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        productType: this.ElSelect_productType.value,
        energyType: this.ElSelect_energyType.value,
        ...this.queryTime
      };
      this.getUnitObjectCosttrend(params);
      this.getUnitObjectCostView(params);
      this.getUnitObjectCostPercent(params);
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    // 单位成本图例综合成本、同比、环比增加悬浮提示
    formatLegend(name, startTime, endTime) {
      const cycle = this.queryTime.aggregationCycle;
      let str = name + ":";
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const dayStr = this.language ? "YYYY-MM-DD" : "YYYY年MM月DD日";
      const formatStr = cycle === 17 ? mothStr : dayStr;
      const unit = cycle === 17 ? "M" : "d";
      if (name === $T("单位成本")) {
        str +=
          this.formatterDate(startTime, formatStr) +
          "-" +
          this.formatterDate(this.$moment(endTime).add(1, unit), formatStr);
      }
      if (name === $T("同比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "Y"),
            formatStr
          ) +
          "-" +
          this.formatterDate(
            this.$moment(endTime).add(1, unit).subtract(1, "Y"),
            formatStr
          );
      }
      if (name === $T("环比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "M"),
            formatStr
          ) +
          "-" +
          this.formatterDate(startTime, formatStr);
      }
      return str;
    },
    // 查单位成本趋势图
    getUnitObjectCosttrend(params) {
      const _this = this;
      customApi.queryUnitObjectCostTrend(params).then(res => {
        if (res.code === 0 && res.data.data && res.data.data.length) {
          // 处理x轴
          const xAxisData = [];
          // 自然周期和非自然周期返回的开始时间和结束时间
          const startTime = res.data.data[0].time;
          const endTime = res.data.data[res.data.data.length - 1].time;
          res.data.data.forEach(item => {
            xAxisData.push(
              this.getAxixs(item.time, this.queryTime.aggregationCycle)
            );
          });
          if (this.queryTime.aggregationCycle === 14) {
            this.CetChart_electrictyCost.options = {
              toolbox: {
                top: 30,
                right: 30,
                feature: {
                  saveAsImage: {}
                }
              },
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unit);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: res.data.unit,
                right: $T("天数"),
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("天数"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unit,
                nameTextStyle: {
                  align: "left"
                }
              },
              series: [
                {
                  name: $T("单位成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.data
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.tbData,
                  smooth: true
                },
                {
                  name: $T("环比"),
                  type: "line",
                  data: res.data.hbData,
                  smooth: true
                }
              ]
            };
          } else {
            this.CetChart_electrictyCost.options = {
              toolbox: {
                top: 30,
                right: 30,
                feature: {
                  saveAsImage: {}
                }
              },
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unit);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: res.data.unit,
                right: $T("月份"),
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("月份"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unit,
                nameTextStyle: {
                  align: "left"
                }
              },
              series: [
                {
                  name: $T("单位成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.data
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.tbData,
                  smooth: true
                }
              ]
            };
          }
        }
      });
    },
    // 单位成本趋势tooltip格式化
    formatTooltip(params, unit) {
      if (!params[0].data.time) return;
      const cycle = this.queryTime.aggregationCycle;
      const formatStr =
        cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
      let str = this.$moment(params[0].data.time).format(formatStr) + "<br />";
      params.forEach(item => {
        str +=
          item.marker +
          item.seriesName +
          ": " +
          (item.data.value || item.data.value === 0
            ? Number(item.data.value).toFixed(2)
            : "--") +
          "(" +
          unit +
          ")" +
          "<br />";
      });
      return str;
    },
    // 单位成本概览
    getUnitObjectCostView(params) {
      customApi.queryUnitObjectCostView(params).then(res => {
        if (res.code === 0) {
          this.unitCostView = res.data.unitCostValue;
          // 同比
          const tbRate = Math.abs(res.data.tb);
          if (tbRate > 1) {
            this.CetChart_avgcost1.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost1.options.series[0].data = [
              tbRate,
              1 - tbRate
            ];
          }
          this.tbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueTb, 2) ||
              "--",
            percent:
              res.data.tb || res.data.tb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.tb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.tb > 0
                ? require("./assets/arrow_up.png")
                : res.data.tb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res?.data?.unit ?? "--"
          };
          // 环比
          const hbRate = Math.abs(res.data.hb);
          if (hbRate > 1) {
            this.CetChart_avgcost2.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost2.options.series[0].data = [
              hbRate,
              1 - hbRate
            ];
          }
          this.hbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueHb, 2) ||
              "--",
            percent:
              res.data.hb || res.data.hb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.hb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.hb > 0
                ? require("./assets/arrow_up.png")
                : res.data.hb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };
        }
      });
    },
    // 单位成本占比
    getUnitObjectCostPercent(params) {
      let nodes = [];
      if (this.currentNode.children && this.currentNode.children.length) {
        nodes = this.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      }
      customApi.queryUnitObjectCostPercent({ ...params, nodes }).then(res => {
        if (res.code === 0) {
          this.CetChart_energyCost.options.series[0].data = [];
          res.data.forEach(item => {
            this.CetChart_energyCost.options.series[0].data.push({
              value: item.value,
              name: item.objectName,
              unit: item.unit
            });
          });
          this.costList = res.data.slice(0, 5);
        }
      });
    },
    effTimeFormat(data) {
      const { effTimeList } = data;
      return effTimeList
        .map(item => {
          const { startTime, endTime } = item;
          return `${this.$moment(startTime).format(
            "YYYY-MM-DD"
          )}~${this.$moment(endTime).format("YYYY-MM-DD")}`;
        })
        .join(",");
    },
    // 初始化
    async init() {
      await this.getProductType();
      await this.getProjectEnergy();
      this.queryTreeType();
    }
  },
  mounted() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  padding: 0;
}
:deep(.el-tree-node__content) {
  position: relative;
}

.proportion {
  .proportion-item {
    .cost-item {
      text-align: center;
      height: 28px;
      line-height: 28px;
      @include font_color(T5);
      @include background(BG2);
    }
    .top {
      overflow-y: auto;
      .top-item {
        height: 20px;
        line-height: 20px;
        margin-bottom: 20px;
        .label {
          width: 80px;
          text-align: right;
        }
        .costVal {
          width: calc(100% - 90px);
          display: flex;
          align-items: center;
          span {
            flex: 1;
          }
        }
      }
    }
    .itemBox-empty {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .avg-cost {
    padding-bottom: 0;
    margin-right: 20px;
    .compare {
      .tip {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 99;
        padding: 2px 20px 2px 2px;
      }
      .preText {
        @include font_color(T5);
        @include background(BG2);
      }
      .lastText {
        @include font_color(T5);
        @include background(BG2);
      }
      .label {
        width: 60px;
        height: 50px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        .percent {
          span {
            display: inline-block;
            max-width: calc(100% - 20px);
          }
          img {
            width: 14px;
          }
        }
      }
    }
  }
}
.cetTree {
  .custom-tree-node {
    .icon {
      visibility: hidden;
      position: absolute;
      right: 0;
      top: 4px;
      @include background_color(BG1);
      .edit {
        @include font_color(ZS);
      }
      .delete {
        @include font_color(Sta3);
      }
    }
  }
  :deep(.el-tree-node__content) {
    position: relative;
    &:hover {
      .custom-tree-node .icon {
        visibility: visible;
      }
    }
  }
}
</style>
