{
  "cet-gianttree-template": {
    "prefix": "cet-gianttree-template",
    "body": [
      "<!-- ${1:设置组件唯一识别字段}组件 -->  ",
      "<CetGiantTree ",
      "v-bind=\"CetGiantTree_$1\" ",
      "v-on=\"CetGiantTree_${1:请输入组件唯一识别字符串}.event\"",
      "></CetGiantTree>"
    ],
    "description": ""
  },
  "cet-gianttree-data": {
    "prefix": "cet-gianttree-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件",
      "CetGiantTree_$1: {",
      "inputData_in: [],",
      "checkedNodes: [], //设置勾选多个节点{ tree_id: \"linesegmentwithswitch_2\" }",
      "selectNode: {},//设置选中某一行",
      "setting: {",
      "check: {",
      "enable: true //多选，不配置则默认单选",
      "},",
      "data: {",
      "simpleData: {",
      "enable: true,",
      "idKey: \"tree_id\"",
      "}",
      "}",
      "},",
      "event: {",
      "Created_out: this.CetGiantTree_$1_created_out, //创建完成后输出",
      "currentNode_out: this.",
      "CetGiantTree_$1_currentNode_out,//选中单行输出",
      "checkedNodes_out: this.",
      "CetGiantTree_$1_checkedNodes_out //勾选节点输出",
      "}",
      "},",
    ],
    "description": ""
  },
  "cet-gianttree-method": {
    "prefix": "cet-gianttree-method",
    "body": [
      "// ${1:设置组件唯一识别字段}组件",
      "CetGiantTree_$1_created_out(val, obj) {},",
      "CetGiantTree_$1_currentNode_out(val) {},",
      "CetGiantTree_$1_checkedNodes_out(val) {},",
    ],
    "description": ""
  },
}