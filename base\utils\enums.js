/**
 * @file 常用枚举使用解决方案
 *
 * 固定式的两层结构
 * 以报警事件状态枚举来说
 *
 * const enums = new Enums({
 *    ALARM_EVENT_STATUS: {
 *      ALARM_EVENT: [0, "报警事件"],
 *      WEIGHTY_WARNING: [1, "严重警告"]
 *    }
 * })
 *
 * enums.val('EVENT_STATUS.ALARM_EVENT')      -> 0
 * enums.desc('EVENT_STATUS.ALARM_EVENT')     -> "报警事件"
 * enums.match('EVENT_STATUS', 0)             -> "报警事件"
 * enums.list('EVENT_STATUS')                 -> [{value: 0, desc: "报警事件"}, {value:1, desc: "严重告警"}]
 */
import _ from "lodash";

class Base {
  constructor(option) {
    this._option = option;
  }

  _get(path) {
    const [mod, key] = path.split(".");
    return this._option[mod][key];
  }

  /**
   * 获取枚举值的 value 值
   * @param {String} path - 两级查找 enums.val('EVENT_STATUS.ALARM_EVENT')      -> 0
   */
  val(path) {
    const [value] = this._get(path);
    return value;
  }

  /**
   * 获取枚举值的描述值
   * @param {String} path - enums.desc('EVENT_STATUS.ALARM_EVENT')     -> "报警事件"
   */
  desc(path) {
    const [, desc] = this._get(path);
    return desc;
  }

  /**
   * 根据枚举值的 value 来匹配枚举值，返回结果见@returns
   * @param {String} mod [必须] - 枚举值限制集名称
   * @param {Number} value [必须] - 枚举值的 value
   * @param {Function|Object} hanlder [非必须]
   *
   * @returns
   *  无handler 参数，默认返回枚举值的描述信息
   *  有handler
   *    -> 函数  返回函数的回调结果
   *    -> 对象  返回对象的 key 匹配值
   */
  match(mod, value, hanlder) {
    const keyMap = this._option[mod];

    for (const key of Object.keys(keyMap)) {
      const [_value, _desc] = keyMap[key];
      if (_value === value) {
        if (_.isPlainObject(hanlder)) {
          return hanlder[key];
        }
        if (_.isFunction(hanlder)) {
          return hanlder(key, _desc);
        }
        return _desc;
      }
    }
    return null;
  }

  /**
   * 解析获取枚举值的列表
   * @param {String} mod [必须] - 枚举值限制集名称
   *
   * @returns
   *  [
   *    {
   *      value: 0,
   *      desc: "报警事件"
   *     }
   *  ]
   */
  list(mod) {
    const keyMap = this._option[mod];
    return Object.keys(keyMap).map((key) => {
      const [_value, _desc] = keyMap[key];
      return {
        value: _value,
        desc: _desc,
      };
    });
  }

  /**
   * 遍历当前枚举值
   *
   * @param {String} mod [必须] - 枚举值限制集名称
   * @param {Function} cb [必须] - 回调函数,参数为 枚举值的 key
   * @returns 由callback返回值组成的集合
   *  特殊说明：如果返回值为 false, 不会推入最终的返回值集合，起到一个过滤作用
   *
   */
  each(mod, cb) {
    const keyMap = this._option[mod];
    const list = [];
    for (const key of Object.keys(keyMap)) {
      const ret = cb(key, keyMap[key]);
      if (ret === false) {
        continue;
      }
      list.push(ret);
    }
    return list;
  }

  /**
   * match 函数的柯里化，将match做了分步处理，方便业务侧包装
   * @param {String} mod [必须]- 见 match
   * @param {Function} cb [必须] - 见 match
   */
  warp(mod, cb) {
    const _this = this;
    return function (value) {
      return _this.match(mod, value, cb);
    };
  }
}

export default class Enums extends Base {}
