<template>
  <el-tabs
    v-model="activeTab"
    :before-leave="tabsBeforeChange"
    class="eltabs bg-BG1 h-full w-full rounded-Ra flex flex-col"
  >
    <el-tab-pane :label="$T('维度标签管理')" name="1">
      <LabelManagement v-if="activeTab === '1'"></LabelManagement>
    </el-tab-pane>
    <el-tab-pane :label="$T('维度标签赋值')" name="2">
      <DimensionConfig
        v-if="activeTab === '2'"
        ref="dimensionConfig"
      ></DimensionConfig>
    </el-tab-pane>
    <el-tab-pane :label="$T('维度节点树配置')" name="3">
      <allocateNodeConfiguration
        v-if="activeTab === '3'"
      ></allocateNodeConfiguration>
    </el-tab-pane>
    <el-tab-pane :label="$T('分项节点树配置')" name="4">
      <itemize v-if="activeTab === '4'"></itemize>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import DimensionConfig from "./dimensionConfig";
import LabelManagement from "./labelManagement";
import allocateNodeConfiguration from "./allocateNodeConfiguration";
import itemize from "./itemize";

export default {
  name: "dimensionConfiguration",
  beforeRouteLeave(to, from, next) {
    if (this.activeTab !== "2") return next();
    this.$refs.dimensionConfig.saveDraftTips(next);
  },
  components: {
    DimensionConfig,
    LabelManagement,
    allocateNodeConfiguration,
    itemize
  },
  data() {
    return {
      activeTab: "1"
    };
  },
  methods: {
    tabsBeforeChange(activeName, oldActiveName) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        if (
          activeName !== null &&
          activeName !== "2" &&
          oldActiveName === "2"
        ) {
          let flag = true;
          await this.$refs.dimensionConfig.saveDraftTips(
            () => {},
            () => {
              flag = false;
            }
          );
          if (!flag) {
            reject();
          } else {
            resolve();
          }
        } else {
          resolve();
        }
      });
    }
  },
  async activated() {
    this.activeTab = null;
    await this.$nextTick();
    this.activeTab = "1";
  }
};
</script>

<style lang="scss" scoped>
.eltabs {
  :deep() {
    .el-tabs__header {
      margin-bottom: 0;
      line-height: 46px;
    }
    .el-tabs__nav-scroll {
      padding-left: var(--J4);
    }
    .el-tabs__content {
      flex: 1;
      min-height: 0;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style>
