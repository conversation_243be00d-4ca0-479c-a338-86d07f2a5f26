<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
          <CetButton
            class="fl"
            v-bind="CetButton_delete"
            v-on="CetButton_delete.event"
          ></CetButton>
          <CetButton
            class="ml-J1"
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <div>
        <el-table
          border
          stripe
          :data="tableData"
          ref="multipleTable"
          height="250"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
          ></el-table-column>
          <el-table-column :label="$T('区间类型')" align="center">
            <template slot-scope="{ row }">
              <ElSelect
                class="mt-J1 mb-J1"
                v-model="row.sectiontype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in sectiontypeList"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </template>
          </el-table-column>
          <el-table-column
            prop="sectiondatatype"
            :label="$T('区间数据类型')"
            align="center"
          >
            <template slot-scope="{ row }">
              <ElSelect
                v-model="row.sectiondatatype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in sectiondatatypeList"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </template>
          </el-table-column>
          <el-table-column prop="min" :label="$T('区间下限')" align="center">
            <template slot-scope="{ row }">
              <ElInputNumber
                v-model="row.min"
                v-bind="ElInputNumber_1"
                v-on="ElInputNumber_1.event"
                :placeholder="$T('请输入区间下限')"
                @change="validateRight([row])"
              ></ElInputNumber>
            </template>
          </el-table-column>
          <el-table-column prop="max" :label="$T('区间上限')" align="center">
            <template slot-scope="{ row }">
              <ElInputNumber
                v-model="row.max"
                v-bind="ElInputNumber_1"
                v-on="ElInputNumber_1.event"
                :placeholder="$T('请输入区间上限')"
                @change="validateRight([row])"
              ></ElInputNumber>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-base/utils/common";

export default {
  name: "addWorkRange",
  components: {},
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    currentNode: {
      type: Object
    },
    wokeRangeData: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("设备运行区间录入"),
        width: "960px",
        showClose: true,
        "append-to-body": true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_delete: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        }
      },
      // 推送周期
      ElSelect_1: {
        value: "",
        style: {
          // width: "90px"
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      tableData: [],
      sectiontypeList: [], // 区间类型枚举
      sectiondatatypeList: [], // 区间数据类型枚举
      multipleSelection: [] // 多选
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.sectiontypeList = this.$store.state.enumerations.sectiontype || [];
      this.sectiondatatypeList =
        this.$store.state.enumerations.sectiondatatype || [];
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      });
      this.init();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {},
    multipleSelection(val) {
      this.CetButton_delete.disable_in = !val.length;
    }
  },
  methods: {
    init() {
      // 查询工作区间配置，有显示，没有新增一条空白
      if (this.wokeRangeData.length) {
        this.tableData = this._.cloneDeep(this.wokeRangeData);
      } else {
        this.tableData = [{ tagId: new Date().getTime() }];
      }
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    // 数据必填
    validateEmpty(editData) {
      let flag = false;
      // 必填校验
      editData.forEach(item => {
        if (
          !item.sectiontype ||
          !item.sectiondatatype ||
          (!item.max && item.max !== 0) ||
          (!item.min && item.min !== 0)
        ) {
          flag = true;
        }
      });
      if (flag) {
        this.$message.warning($T("数据不能为空!"));
      }
      return flag;
    },
    // 重复校验
    validateRepeat(data) {
      let repeatFlag = false;
      let rules = []; // 重复的规则提示
      // 判重校验 区间类型和区间数据类型相同即为重复
      data.forEach(item => {
        if (
          data.filter(
            i =>
              i.sectiontype === item.sectiontype &&
              i.sectiondatatype === item.sectiondatatype
          ).length >= 2
        ) {
          repeatFlag = true;
          rules.push(item);
        }
      });
      if (repeatFlag) {
        let sectiontype = rules[0].sectiontype;
        let sectiondatatype = rules[0].sectiondatatype;
        let str = `${$T("区间类型")}${
          this._.find(this.sectiontypeList, ["id", sectiontype]).text
        }，${$T("区间数据类型")}${
          this._.find(this.sectiondatatypeList, ["id", sectiondatatype]).text
        }${$T("有重复")}！`;
        this.$message.warning(str);
      }
      return repeatFlag;
    },
    // 数据正确性校验
    validateRight(editData) {
      let flag = false;
      editData.forEach(item => {
        if (
          (item.max || item.max === 0) &&
          (item.min || item.min === 0) &&
          item.max <= item.min
        ) {
          flag = true;
        }
      });
      if (flag) {
        this.$message.warning($T("上限要大于下限！"));
      }
      return flag;
    },
    CetButton_preserve_statusTrigger_out(val) {
      if (this.validateEmpty(this.tableData)) return;
      if (this.validateRepeat(this.tableData)) return;
      if (this.validateRight(this.tableData)) return;
      this.$emit("saveData_out", this.tableData);
    },
    CetButton_add_statusTrigger_out(val) {
      this.tableData.push({ tagId: new Date().getTime() });
    },
    // 多选删除
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    CetButton_delete_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        this.multipleSelection.forEach(item => {
          let index;
          if (item.id) {
            index = this._.findIndex(this.tableData, ["id", item.id]);
          }
          if (item.tagId) {
            index = this._.findIndex(this.tableData, ["tagId", item.tagId]);
          }
          this.tableData.splice(index, 1);
        });
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
