<template>
  <div class="w-full h-full relative table-border">
    <el-checkbox
      class="checkedAll"
      :indeterminate="isIndeterminate"
      v-model="checkedAll"
      @change="handleCheckAllChange"
    ></el-checkbox>
    <div
      :class="{
        fullfilled: true,
        light: lightTheme,
        boxShadow: tableScrollLeft
      }"
      id="busbarsectionTable"
      ref="busbarsectionTable"
    ></div>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
export default {
  name: "busbarsectionTable",
  props: {
    data: Array,
    columns: Array,
    editMode: Boolean,
    clearFilterHandle: Number,
    deleteDisable: Boolean
  },
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    hotLanguage() {
      if (window.localStorage.getItem("omega_language") === "en") {
        return "en-US";
      }
      return "zh-CN";
    }
  },
  data() {
    return {
      tableScrollLeft: false,
      checkedAll: false,
      isIndeterminate: false,
      tableData: [],
      handsontableFilter: false
    };
  },
  watch: {
    data: {
      handler: function (val) {
        this.tableData = this._.cloneDeep(val);
        this.initTable();
      },
      deep: true,
      immediate: true
    },
    tableData: {
      handler: function () {
        this.isIndeterminate = !!this.tableData.find(
          item => !item.deleteStatus
        );
        this.checkedAll = !!this.tableData.length;
        this.$emit("update:deleteDisable", !this.checkedAll);
      },
      deep: true,
      immediate: true
    },
    clearFilterHandle() {
      this.clearFilter();
    }
  },
  methods: {
    async initTable() {
      const hotElement = this.$refs.busbarsectionTable;
      if (!hotElement) {
        return;
      }

      const tableColumns = [
        {
          data: "deleteStatus",
          type: "checkbox",
          readOnly: false
        },
        ...this.columns,
        {
          className: "htLeft",
          data: "tree_id",
          type: "text",
          columnSorting: true,
          readOnly: true
        }
      ];
      const tableHeaders = ["", ...this.columns.map(i => i.label), ""];
      const colWidths = [
        50,
        ...this.columns.map(i => {
          return i.minWidth ?? 200;
        }),
        0.001
      ];

      const hotSettings = {
        fixedColumnsLeft: 1,
        colWidths: colWidths,
        wordWrap: true, // 单元格文字是否换行展示
        data: this.tableData,
        columns: tableColumns,
        readOnly: false,
        stretchH: "all",
        autoWrapRow: true,
        height: "100%",
        maxRows: this.tableData.length,
        rowHeaders: true,
        colHeaders: tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: "vertical", //设置自定填充垂直方向
        manualRowMove: false, //当值为true时，行可拖拽至指定行
        language: this.hotLanguage,
        columnSorting: true,
        afterChange: (val, source) => {
          if (source !== "edit" && source !== "CopyPaste.paste") return;
          const isCheck = val.find(i => i[1] === "deleteStatus");
          if (isCheck) {
            this.changeDataDeleteStatus();
          }
          this.$emit("dataChange");
          this.validateCells();
        },
        beforeColumnSort: () => {
          const filtersPlugin = this.hot.getPlugin("filters");
          if (filtersPlugin.isEnabled() && this.handsontableFilter) {
            this.$message.warning($T("请点击重置全部过滤"));
            return false;
          }
          return true;
        },
        beforeFilter: filter => {
          this.handsontableFilter = !!filter?.length;
          // 将此次排序好的数据先保存起来
          return true;
        },
        afterValidate: (isValid, value, row, col, source) => {
          if (isValid) {
            return;
          }
          if (!["edit", "CopyPaste.paste"].includes(source)) {
            return;
          }

          if (
            ["busBarSectionFirstName", "busBarSectionSecondName"].includes(col)
          ) {
            /**
             * 改为母线只允许出现一次，
             * 后续可能会改回母线1+母线2不能重复，将注释部分打开即可
             */
            // this.$message.warning(
            //   $T("只能选择已有项，并且母线1和母线2组合之后不能重复")
            // );
            this.$message.warning($T("只能选择已有项"));
            return;
          }

          if (col !== "busBarConnectName") {
            return;
          }

          const checkNameLength = common.check_name.max;

          if (!value) {
            this.$message.warning($T("请填写母联", checkNameLength));
          }

          this.$message.warning(
            $T(
              "母联名称不能包含特殊字符，不能超过{0}个字符，并且不允许重复",
              checkNameLength
            )
          );
        }
      };

      if (this.hot) {
        this.hot.updateSettings(hotSettings);
      } else {
        // eslint-disable-next-line no-undef
        this.hot = new Handsontable(hotElement, hotSettings);
      }
      await this.$nextTick();
      this.addScrollEvent();
    },

    /**
     * 修改表格单行勾选状态
     */
    changeDataDeleteStatus() {
      const tableData = this._.cloneDeep(this.tableData);
      const data = this.hot.getData();
      data.forEach(item => {
        const tree_id = item[item.length - 1];
        const obj = tableData.find(i => i.tree_id === tree_id);
        obj.deleteStatus = item[0];
      });
      this.tableData = tableData;
    },

    async handleCheckAllChange() {
      const tableData = this.getTableData();
      const data = this.hot.getData();
      const showIds = data.map(item => item[item.length - 1]);
      await this.$nextTick();
      tableData.forEach(item => {
        // 存在过滤条件只会将显示出来的勾上或者取消，其他的置空
        if (this.handsontableFilter) {
          const tree_id = item.tree_id;
          if (showIds.includes(tree_id)) {
            item.deleteStatus = this.checkedAll;
          } else {
            item.deleteStatus = false;
          }
          return tableData;
        } else {
          item.deleteStatus = this.checkedAll;
        }
      });
      this.tableData = tableData;
      this.hot.loadData(this.tableData);
      setTimeout(() => {
        const filtersPlugin = this.hot.getPlugin("filters");
        filtersPlugin.filter();
      }, 300);

      this.$emit("update:deleteDisable", !this.checkedAll);
    },

    addScrollEvent() {
      if (this.scrollDom) {
        this.scrollDom.removeEventListener("scroll", this.scrollEvent);
        this.scrollDom = null;
      }
      const scrollDom = this.$refs?.busbarsectionTable
        ?.getElementsByClassName("ht_master")[0]
        .getElementsByClassName("wtHolder")[0];
      this.scrollDom = scrollDom;
      scrollDom?.addEventListener("scroll", this.scrollEvent);
    },
    scrollEvent() {
      this.tableScrollLeft = !!this.scrollDom.scrollLeft;
    },

    /**
     * 获取表格完整数据
     */
    getTableData() {
      return this._.cloneDeep(this.tableData);
    },

    /**
     * 清除过滤
     */
    async clearFilter() {
      if (!this.hot) return;
      this.hot.updateSettings({ filters: false });
      await this.$nextTick();
      this.handsontableFilter = false;
      this.hot.updateSettings({ filters: true });
    },

    /**
     * 清除已删除数据
     */
    async clearDeleteData() {
      this.tableData = this.tableData.filter(i => !i.deleteStatus);
      this.hot.loadData(this.tableData);
      await this.$nextTick();
      setTimeout(() => {
        const filtersPlugin = this.hot.getPlugin("filters");
        filtersPlugin.filter();
      }, 300);
    },

    /**
     * 校验表格字段是否通过
     */
    async validateCells() {
      return new Promise(resolve => {
        this.hot.validateCells(valid => {
          resolve(valid);
        });
      });
    }
  },
  mounted() {
    this.initTable();
    this.resizeObserver = new ResizeObserver(
      this._.debounce(() => {
        this.initTable();
      }, 300)
    );
    this.resizeObserver.observe(this.$refs.busbarsectionTable);
  },
  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(this.$refs.busbarsectionTable);
    }
    this.scrollDom.removeEventListener("scroll", this.scrollEvent);
  }
};
</script>

<style lang="scss" scoped>
#busbarsectionTable {
  @import "../handsontable.scss";

  :deep() {
    .handsontable span.colHeader.columnSorting:before {
      top: 8px;
      width: 14px;
      height: 14px;
      padding-left: 0;
      transform: translate(6px, 0px);
      background-image: url("../assets/initial.png");
      background-size: 100% 100%;
    }
    .handsontable span.colHeader.columnSorting.descending:before {
      background-image: url("../assets/descending.png");
      background-size: 100% 100%;
    }
    .handsontable span.colHeader.columnSorting.ascending:before {
      background-image: url("../assets/ascending.png");
      background-size: 100% 100%;
    }
  }

  .light :deep() {
    .handsontable span.colHeader.columnSorting:before {
      background-image: url("../assets/initial_light.png");
      background-size: 100% 100%;
    }
    .handsontable span.colHeader.columnSorting.descending:before {
      background-image: url("../assets/descending_light.png");
      background-size: 100% 100%;
    }
    .handsontable span.colHeader.columnSorting.ascending:before {
      background-image: url("../assets/ascending_light.png");
      background-size: 100% 100%;
    }
  }
}

.table-border {
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}
.checkedAll {
  position: absolute;
  z-index: 111;
  left: 55px;
  top: 8px;
}
</style>

<style lang="scss">
#hot-display-license-info {
  display: none;
}
.handsontable .ht_master table td.htCustomMenuRenderer {
  @include background_color(BG);
}
.htFiltersConditionsMenu table tbody tr td {
  @include background_color(BG1);
}
.htDropdownMenu table tbody tr td {
  @include background_color(BG1);
}
.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {
  @include background_color(BG2);
}
.handsontable .htUISelectCaption {
  @include background_color(BG4);
}
.handsontable .htUIInput.htUIButtonOK input {
  @include background_color(ZS);
}

.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody {
  & > tr:nth-child(1),
  & > tr:nth-child(2),
  & > tr:nth-child(3),
  & > tr:nth-child(4),
  & > tr:nth-child(5),
  & > tr:nth-child(6),
  & > tr:nth-child(7),
  & > tr:nth-child(8),
  & > tr:nth-child(9),
  & > tr:nth-child(10) {
    display: none;
  }
}
ul.zTreeDragUL {
  position: fixed;
  list-style: none;
  padding: 4px;
  @include background_color(BG4);
}
</style>
