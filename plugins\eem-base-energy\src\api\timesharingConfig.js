import fetch from "eem-base/utils/fetch";

/**
 * 获取分时方案列表
 */
export function timeShareSchemeList(data) {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/timeShareScheme`,
    method: "POST",
    data
  });
}

/**
 * 批量删除分时方案
 */
export function timeShareSchemeDelete(data) {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/timeShareScheme/delete`,
    method: "POST",
    data
  });
}
/**
 * 获取时段方案
 */
export function timeShareSchemeById(id, params) {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/timeShareScheme/${id}`,
    method: "GET",
    params
  });
}

/**
 * 获取关联节点
 */
export function nodeByTSScheme(params) {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/nodeByTSScheme`,
    method: "GET",
    params
  });
}

/**
 * 保存关联节点
 */
export function timeShareRelationship(data, params) {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/timeShareRelationship`,
    method: "PUT",
    data,
    params
  });
}

/**
 * 保存分时方案
 */
export function saveTimeShareScheme(data, params) {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/saveTimeShareScheme`,
    method: "PUT",
    data,
    params
  });
}

/**
 * 获取时段名称
 */
export function timePeriodDefault() {
  return fetch({
    url: `/eem-base/energy/v1/timeshare-config/timePeriod/default`,
    method: "GET"
  });
}
