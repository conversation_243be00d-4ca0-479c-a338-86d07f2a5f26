<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="fullheight">
      <div class="basic-box-label">
        {{ $T("原能源类型") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElSelect
        class="mb-J3 fullwidth"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
        :disabled="!!inputData_in?.id"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </ElSelect>
      <div class="basic-box-label">
        {{ $T("目标能源类型") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElSelect
        class="mb-J3 fullwidth"
        v-model="ElSelect_2.value"
        v-bind="ElSelect_2"
        v-on="ElSelect_2.event"
        :disabled="!!inputData_in?.id"
      >
        <ElOption
          v-for="item in ElOption_2.options_in"
          :key="item[ElOption_2.key]"
          :label="item[ElOption_2.label]"
          :value="item[ElOption_2.value]"
          :disabled="item[ElOption_2.disabled]"
        ></ElOption>
      </ElSelect>
      <div class="basic-box-label">
        {{ $T("转换系数") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElInputNumber
        class="fullwidth"
        v-model="ElInputNumber_1.value"
        v-bind="ElInputNumber_1"
        v-on="ElInputNumber_1.event"
      ></ElInputNumber>
    </div>
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import custom from "@/api/custom";
export default {
  name: "AddConvertedstandardcoalcoef",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array,
      default: () => []
    },
    projectEnergy_in: {
      type: Array,
      default: () => []
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "320px",
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInputNumber_1: {
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        event: {}
      },
      ElSelect_1: {
        value: "",
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      this.projectEnergy = this._.cloneDeep(this.projectEnergy_in);
      this.ElOption_1.options_in = this.projectEnergy;
      this.ElOption_2.options_in = this.projectEnergy;
      if (this.inputData_in.id) {
        const data = this.inputData_in;
        this.CetDialog_1.title = $T("修改");
        this.ElSelect_1.value = data.sourceenergytype;
        this.ElSelect_2.value = data.targetenergytype;
        this.ElInputNumber_1.value = data.coef == null ? undefined : data.coef;
      } else {
        this.CetDialog_1.title = $T("新建");
        this.ElSelect_1.value = null;
        this.ElSelect_2.value = null;
        this.ElInputNumber_1.value = undefined;
      }
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    async CetButton_confirm_statusTrigger_out() {
      if (!this.ElSelect_1.value) {
        this.$message({
          type: "warning",
          message: $T("请选择源能源类型")
        });
        return;
      }
      if (!this.ElSelect_2.value) {
        this.$message({
          type: "warning",
          message: $T("目标能源类型")
        });
        return;
      }
      if (this.ElInputNumber_1.value === undefined) {
        this.$message({
          type: "warning",
          message: $T("请填写转换系数")
        });
        return;
      }
      const saveData = [
        {
          id: this.inputData_in.id,
          coef: this.ElInputNumber_1.value,
          sourceenergytype: this.ElSelect_1.value,
          targetenergytype: this.ElSelect_2.value,
          modelLabel: "convertedstandardcoalcoef"
        }
      ];
      const params = {
        projectId: this.projectId
      };

      const fn = this.inputData_in.id
        ? custom.editConvertedstandardcoalcoef
        : custom.addConvertedstandardcoalcoef;

      const res = await fn(saveData, params);
      if (res.code !== 0) {
        return;
      }

      this.$emit("addConvertedstandardcoalcoefFinished");
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.$message.success($T("保存成功"));
    },
    ElSelect_1_change_out(val) {
      const energytypeArr = [val];

      this.tableData.forEach(item => {
        if (item.sourceenergytype === val) {
          energytypeArr.push(item.targetenergytype);
        }
      });

      this.ElOption_2.options_in = this.projectEnergy.filter(item => {
        return !energytypeArr.includes(item.energytype);
      });

      if (
        !this.ElOption_2.options_in.some(
          item => item.energytype === this.ElSelect_2.value
        )
      ) {
        this.ElSelect_2.value = this.ElOption_2.options_in?.[0]?.energytype;
      }
    },
    ElSelect_2_change_out(val) {
      const energytypeArr = [val];

      this.tableData.forEach(item => {
        if (item.targetenergytype === val) {
          energytypeArr.push(item.sourceenergytype);
        }
      });

      this.ElOption_1.options_in = this.projectEnergy.filter(item => {
        return !energytypeArr.includes(item.energytype);
      });

      if (
        !this.ElOption_1.options_in.some(
          item => item.energytype === this.ElSelect_1.value
        )
      ) {
        this.ElSelect_1.value = this.ElOption_1.options_in?.[0]?.energytype;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.basic-box-label {
  margin-bottom: var(--J1);
}
</style>
