<template>
  <div class="fullfilled flex flex-col">
    <CustomSteps :active="stepActive" :steps="steps" />
    <div
      class="mt-J3 energyTypeContent flex-auto bg-BG1 rounded-Ra"
      v-if="stepActive === 0"
    >
      <div
        class="energyTypeBox flex flex-col"
        :class="{ light: lightTheme }"
        id="projectBatchConfig_energyType"
      >
        <div class="titleBox flex-row flex">
          <div class="icon mr-J3"></div>
          <div class="title">{{ $T("能源类型") }}</div>
        </div>
        <div class="energyType mt-J3 mb-J3">
          <ElSelect
            class="fullwidth"
            v-model="ElSelect_energyType.value"
            v-bind="ElSelect_energyType"
            v-on="ElSelect_energyType.event"
          >
            <ElOption
              v-for="item in ElOption_energyType.options_in"
              :key="item[ElOption_energyType.key]"
              :label="item[ElOption_energyType.label]"
              :value="item[ElOption_energyType.value]"
              :disabled="item[ElOption_energyType.disabled]"
            ></ElOption>
          </ElSelect>
        </div>
        <div class="next text-right">
          <CetButton
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton>
        </div>
      </div>
    </div>
    <div class="mt-J3 flex-auto" v-if="stepActive === 1">
      <div
        class="h-full w-full bg-BG1 rounded-Ra relative"
        v-if="refreshTreeIng"
      >
        <el-progress
          class="progress"
          type="circle"
          :percentage="percentage"
        ></el-progress>
        <span class="progress-text">{{ $T("请稍后，正在加载") }}</span>
      </div>
      <BatchConfig
        v-if="!refreshTreeIng"
        ref="batchConfig"
        :energyType_in="ElSelect_energyType.value"
        @back_out="back_out"
      />
    </div>
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import BatchConfig from "./batchConfig/index.vue";
import omegaTheme from "@omega/theme";
import CustomSteps from "eem-base/components/customSteps.vue";
import { api } from "@altair/knight";
import useCounter from "@/hooks/useDriver.js";
const [Driver] = useCounter();

export default {
  name: "projectBatchConfig",
  beforeRouteLeave(to, from, next) {
    if (this.stepActive !== 1 || this.refreshTreeIng) return next();
    this.$refs.batchConfig.saveDraftTips(next);
  },
  components: {
    BatchConfig,
    CustomSteps
  },
  data() {
    return {
      stepActive: 0,
      steps: [{ title: $T("选择能源类型") }, { title: $T("层级信息配置") }],
      ElSelect_energyType: {
        value: "",
        placeholder: $T("请选择能源类型"),
        event: {}
      },
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: $T("下一步"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      }
    };
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    connectionStatus() {
      return this.$store.state.importProgress.connectionStatus;
    },
    refreshTreeIng() {
      if (!this.connectionStatus) {
        return false;
      }
      return this.$store.getters["importProgress/importing"](14);
    },
    percentage() {
      const processList = this.$store.state.importProgress.processList || [];
      const data = processList.find(i => i.type === 14);
      const ratio = data?.processInfo?.ratio ?? 0;
      return Math.floor(ratio * 100);
    }
  },
  watch: {
    "$route.query": {
      handler() {
        this.initDriver();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async queryProjectEnergy() {
      const energyTypeRes =
        await commonApi.organizationConfigFilterEnergyType();
      const totalEnergyTypes = energyTypeRes?.data ?? [];
      const params = {
        projectId: this.projectId
      };
      const res = await commonApi.queryProjectEnergyList(params);
      if (res.code !== 0) return;
      const energytypeList = res.data || [];
      // 过滤折标能源类型
      this.ElOption_energyType.options_in = energytypeList.filter(
        item => !totalEnergyTypes.includes(item.energytype)
      );
    },
    init() {
      this.ElSelect_energyType.value = null;
      this.stepActive = 0;
    },
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    async initDriver() {
      const { step } = api.getRouterQuery();
      if (step !== "1") return;
      this.stepActive = 0;
      await this.$nextTick();
      Driver.setSteps([
        {
          element: "#projectBatchConfig_energyType",
          popover: {
            title: $T("选择能源类型"),
            description: $T("选择需要去创建的项目层级能源类型，点击下一步"),
            onNextClick: async () => {
              this.ElSelect_energyType.value =
                this.ElOption_energyType.options_in?.[0].energytype;
              this.stepActive = 1;
              await this.sleep(500);
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#projectBatchConfig_treeBox",
          popover: {
            title: $T("选择节点"),
            description: $T("在左侧将节点拖拽至右侧表格中新增节点"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: async () => {
              this.stepActive = 0;
              await this.sleep(500);
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#projectBatchConfig_table",
          popover: {
            title: $T("编辑表格"),
            description: $T(
              "支持类似excel表格操作进行编辑表格，支持单个、多个单元格复制，支持外部excel复制内容并粘贴进来，支持拖拽序号列进行顺序微调，单个单元格中支持键盘按delete进行删除数据，单元格支持选择已存在层级节点名称或者全新填写名称"
            ),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#projectBatchConfig_tableDelete",
          popover: {
            title: $T("删除数据"),
            description: $T("勾选后可支持删除"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#projectBatchConfig_draft",
          popover: {
            title: $T("保存草稿"),
            description: $T("在表格编辑过程中，可将当前编辑结构暂存成草稿"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#projectBatchConfig_save",
          popover: {
            title: $T("生成配置"),
            description: $T("点击可将表格中新增条目、草稿条目自动创建配置"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        }
      ]);

      Driver.drive();
    },
    CetButton_next_statusTrigger_out() {
      if (!this.ElSelect_energyType.value) {
        this.$message.warning($T("请选择能源类型"));
        return;
      }
      this.stepActive = 1;
    },
    back_out() {
      this.stepActive = 0;
    }
  },
  async mounted() {
    await this.queryProjectEnergy();
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.steps {
  width: 400px;
  margin: auto;
}
.energyTypeContent {
  position: relative;
  .energyTypeBox {
    width: 800px;
    position: absolute;
    left: 50%;
    top: 160px;
    transform: translate(-50%, 0);
    .titleBox {
      height: 40px;
      line-height: 40px;
      .icon {
        height: 40px;
        width: 40px;
        background: url("./assets/energyTypeIcon.png");
        background-size: 100% 100%;
      }
      .title {
        font-size: 32px;
        color: var(--ZS);
      }
    }
    .energyType {
      width: 100%;
      box-sizing: border-box;
      padding: 24px 40px;
      background: url("./assets/energyTypeBox.png");
      background-size: 100% 100%;
    }
    &.light {
      .titleBox .icon {
        background: url("./assets/energyTypeIcon_light.png");
        background-size: 100% 100%;
      }
      .energyType {
        background: url("./assets/energyTypeBox_light.png");
        background-size: 100% 100%;
      }
    }
  }
}
.progress {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.progress-text {
  position: absolute;
  left: 50%;
  top: calc(50% + 84px);
  transform: translate(-50%, -50%);
}
</style>
