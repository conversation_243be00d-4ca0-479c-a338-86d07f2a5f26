<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <CetGiantTree
      class="switch-tree"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
        class="mr-J1"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import commonApi from "@/api/custom.js";
import {
  getTreeParams,
  TREE_TYPE,
  findNode
} from "@/utils/analysisServiceConfig.js";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    //选中节点信息
    fatherNode_in: {
      type: Object
    },
    // 选中子节点列表
    selectedData_in: {
      type: Array,
      default() {
        return [];
      }
    },
    //管理层级和管网层级区别
    netWork: {
      type: Boolean
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },

    // 这几个特殊的模型可以挂在楼栋、楼层、普通房间里面
    isManuequipment() {
      const seleteList = this.selectedData_in || [];
      return !seleteList.some(item => {
        return ![
          "manuequipment",
          "meteorologicalmonitor",
          "airconditioner"
        ].includes(item.modelLabel);
      });
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("请选择节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checkedNode: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //勾选节点输出
        }
      },
      title: "",
      isManuequipmentOK: false,
      isBuildingOk: false,
      isLinesegmentOk: false,
      isVirtualBuildingNodeOk: false
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      if (!this.fatherNode_in) {
        return;
      }
      this.CetDialog_1.openTrigger_in = val;

      this.initDialogTitle();

      this.getTreeData();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    /**
     * 初始化弹窗标题
     */
    initDialogTitle() {
      const fatherNode = this.fatherNode_in;

      if (this.isManuequipment) {
        this.CetDialog_1.title = $T("请选择楼栋、楼层、普通房间节点");
        return;
      }

      const query = {
        modelLabel: fatherNode.modelLabel
      };
      if (fatherNode.modelLabel === "room") {
        query.roomType = fatherNode.roomtype || null;
      }
      const node = findNode(query);
      const fatherNodeName = node.name;
      this.CetDialog_1.title = $T("请选择{0}节点", fatherNodeName);
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    CetGiantTree_1_currentNode_out(val) {
      this.checkedNode = this._.cloneDeep(val);
    },
    async getTreeData() {
      let params;
      if (this.netWork) {
        params = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: getTreeParams(TREE_TYPE.NETWORK),
          treeReturnEnable: true
        };
      } else {
        params = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: getTreeParams(TREE_TYPE.MANAGE),
          treeReturnEnable: true
        };
      }
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNode = {};
      this.CetGiantTree_1.checkedNodes = [];

      const res = await commonApi.getNodeTreeSimple(params);
      if (res.code !== 0) {
        return;
      }
      this.CetGiantTree_1.inputData_in = res.data || [];
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    async CetButton_confirm_statusTrigger_out() {
      const { modelLabel, roomtype } = this.checkedNode;
      if (
        this.isManuequipment &&
        !["building", "floor"].includes(modelLabel) &&
        (modelLabel !== "room" || roomtype)
      ) {
        this.$message.warning(this.CetDialog_1.title);
        return;
      }

      if (!this.isManuequipment) {
        const fatherNode = this.fatherNode_in;
        const query = {
          modelLabel: fatherNode.modelLabel
        };
        if (fatherNode.modelLabel === "room") {
          query.roomType = fatherNode.roomtype || null;
        }
        const node = findNode(query);
        if (modelLabel !== node.modelLabel || roomtype !== node.roomtype) {
          this.$message.warning(this.CetDialog_1.title);
          return;
        }
      }

      const params = [
        {
          newParent: this.checkedNode,
          oldParent: this.fatherNode_in,
          toBeOutNodes: this.selectedData_in
        }
      ];
      const res = await commonApi.batchMoveNode(params);
      if (res.code !== 0) {
        return;
      }

      this.$message.success($T("保存成功"));
      this.$emit("updata_out");
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    }
  }
};
</script>
<style lang="scss" scoped>
.switch-tree {
  height: 500px;
}
</style>
