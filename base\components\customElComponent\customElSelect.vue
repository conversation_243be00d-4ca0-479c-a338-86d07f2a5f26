<template>
  <div class="custom-select bg-BG1" :class="{ disabled: isDisabled }">
    <div class="custom-select-label">
      {{ prefix_in }}
      <el-popover
        placement="bottom"
        width="400"
        trigger="hover"
        :content="popover_in"
      >
        <i
          class="el-icon-question question"
          slot="reference"
          v-if="popover_in"
        ></i>
      </el-popover>
    </div>
    <ElSelect
      v-bind="$attrs"
      v-on="$listeners"
      class="custom-select-content flex-auto"
    >
      <slot></slot>
    </ElSelect>
  </div>
</template>

<script>
export default {
  name: "customElSelect",
  props: {
    prefix_in: String,
    popover_in: String
  },
  computed: {
    isDisabled() {
      return (
        "disabled" in this.$attrs &&
        (this.$attrs.disabled || this.$attrs.disabled === "")
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.custom-select {
  display: inline-flex;
  align-items: center;
  border: 1px solid;
  border-radius: var(--Ra);
  border-color: var(--B1);
  &.disabled {
    border-color: var(--B2);
    background-color: var(--BG);
  }
}

.custom-select-label {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  color: var(--ZS);
  font-size: var(--Aa);
  padding-left: var(--J2);
}

.question {
  margin-left: var(--J0);
}

.custom-select-content {
  display: inline-flex;
  align-items: center;
  :deep() {
    .el-input--small .el-input__inner {
      border: none;
      height: 30px;
      padding-left: var(--J2);
    }
    .el-input-group__append {
      border: none;
    }
    .el-input__inner {
      padding-left: 0;
    }
    .el-select__tags span:first-child {
      margin-left: 0;
    }
  }
}
</style>
