<template>
  <div class="project-cfg">
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="cet-dialog">
      <el-tabs v-model="activeName" class="eltabs" @tab-click="handleClick">
        <el-tab-pane
          class="flex flex-col w-full"
          :label="$T('能耗')"
          name="2"
          v-if="false"
        >
          <div class="table-title clearfix" v-permission="'nodeconfig_update'">
            <div class="fl">
              {{ $T("已选") }}
              <span class="text-ZS">{{ delEnergyIds.length || 0 }}</span>
              {{ $T("条") }}
            </div>
            <CetButton
              id="cloudProjectConfig_projectCfg_energy"
              class="fr ml-J1"
              v-bind="CetButton_addEnergy"
              v-on="CetButton_addEnergy.event"
            ></CetButton>
            <CetButton
              class="fr"
              v-bind="CetButton_delEnergy"
              v-on="CetButton_delEnergy.event"
              :disable_in="!delEnergyIds?.length"
            ></CetButton>
          </div>
          <CetTable
            class="flex-auto"
            :data.sync="CetTable_energy.data"
            :dynamicInput.sync="CetTable_energy.dynamicInput"
            v-bind="CetTable_energy"
            v-on="CetTable_energy.event"
            @selection-change="selectionEnergyChanged"
          >
            <ElTableColumn
              v-bind="ElTableColumn_selection"
              v-if="$checkPermission('nodeconfig_update')"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_energy_type"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_unit_coefficient"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_unit_price"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_energy_type_id"
            ></ElTableColumn>
            <ElTableColumn
              :label="$T('操作')"
              :width="enLanguage ? 130 : 110"
              header-align="left"
              align="left"
              v-if="$checkPermission('nodeconfig_update')"
            >
              <template slot-scope="scope">
                <span
                  class="cursor-pointer text-ZS mr-J3"
                  @click.stop="editEnergy(scope.row)"
                >
                  {{ $T("编辑") }}
                </span>
                <span
                  class="cursor-pointer text-Sta3"
                  @click.stop="delEnergy([scope.row.id])"
                >
                  {{ $T("删除") }}
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </el-tab-pane>
        <el-tab-pane class="flex-col flex w-full" :label="$T('产品')" name="1">
          <div class="table-title clearfix" v-permission="'nodeconfig_update'">
            <div class="fl">
              {{ $T("已选") }}
              <span class="text-ZS">{{ delProductIds.length || 0 }}</span>
              {{ $T("条") }}
            </div>
            <CetButton
              class="fr ml-J1"
              v-bind="CetButton_addProduct"
              v-on="CetButton_addProduct.event"
            ></CetButton>
            <CetButton
              class="fr"
              v-bind="CetButton_delProduct"
              v-on="CetButton_delProduct.event"
              :disable_in="!delProductIds?.length"
            ></CetButton>
          </div>
          <div class="flex-auto">
            <CetTable
              height="100%"
              :data.sync="CetTable_product.data"
              :dynamicInput.sync="CetTable_product.dynamicInput"
              v-bind="CetTable_product"
              v-on="CetTable_product.event"
              @selection-change="selectionProductChanged"
            >
              <ElTableColumn
                v-bind="ElTableColumn_selection"
                v-if="$checkPermission('nodeconfig_update')"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_product_name"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_product_type"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_product_type_id"
              ></ElTableColumn>
              <ElTableColumn
                :label="$T('操作')"
                :width="enLanguage ? 130 : 110"
                header-align="left"
                align="left"
                v-if="$checkPermission('nodeconfig_update')"
              >
                <template slot-scope="scope">
                  <span
                    class="handle fl mr-J3"
                    @click.stop="editProduct(scope.row)"
                  >
                    {{ $T("编辑") }}
                  </span>
                  <span
                    class="handle delete fl"
                    @click.stop="delProduct([scope.row.id])"
                  >
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
        </el-tab-pane>
        <el-tab-pane
          class="flex flex-col w-full"
          :label="$T('折标系数')"
          name="3"
          v-if="false"
        >
          <div class="table-title clearfix" v-permission="'nodeconfig_update'">
            <div class="fl">
              {{ $T("已选") }}
              <span class="text-ZS">{{ dleCoefficientIds.length || 0 }}</span>
              {{ $T("条") }}
            </div>
            <CetButton
              class="fr ml-J1"
              v-bind="CetButton_addCoefficient"
              v-on="CetButton_addCoefficient.event"
            ></CetButton>
            <CetButton
              class="fr"
              v-bind="CetButton_delCoefficient"
              v-on="CetButton_delCoefficient.event"
              :disable_in="!dleCoefficientIds?.length"
            ></CetButton>
          </div>
          <div class="flex-auto">
            <CetTable
              height="100%"
              :data.sync="CetTable_coefficient.data"
              :dynamicInput.sync="CetTable_coefficient.dynamicInput"
              v-bind="CetTable_coefficient"
              v-on="CetTable_coefficient.event"
              @selection-change="selectionCoefficientChanged"
            >
              <ElTableColumn
                v-bind="ElTableColumn_selection"
                v-if="$checkPermission('nodeconfig_update')"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_sourceenergytype"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_targetenergytype"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_coefficient"></ElTableColumn>
              <ElTableColumn
                :label="$T('操作')"
                :width="enLanguage ? 130 : 110"
                header-align="left"
                align="left"
                v-if="$checkPermission('nodeconfig_update')"
              >
                <template slot-scope="scope">
                  <span
                    class="handle fl mr-J3"
                    @click.stop="editCoefficient(scope.row)"
                  >
                    {{ $T("编辑") }}
                  </span>
                  <span
                    class="handle delete fl"
                    @click.stop="delCoefficient([scope.row.id])"
                  >
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
        </el-tab-pane>
        <el-tab-pane
          :label="$T('单位转换配置')"
          name="4"
          style="width: 100%"
          v-if="false"
        >
          <UnitTransition
            :visibleTrigger_in="unitTransition.visibleTrigger_in"
          ></UnitTransition>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </CetDialog>
    <AddProduct
      :visibleTrigger_in="addProduct.visibleTrigger_in"
      :closeTrigger_in="addProduct.closeTrigger_in"
      :queryId_in="addProduct.queryId_in"
      :inputData_in="addProduct.inputData_in"
      :tableData="CetTable_product.data"
      @addProductFinished="addProductFinished"
    ></AddProduct>
    <AddEnergy
      :visibleTrigger_in="addEnergy.visibleTrigger_in"
      :closeTrigger_in="addEnergy.closeTrigger_in"
      :queryId_in="addEnergy.queryId_in"
      :inputData_in="addEnergy.inputData_in"
      :tableData="CetTable_energy.data"
      @addEnergyFinished="addEnergyFinished"
    ></AddEnergy>
    <AddConvertedstandardcoalcoef
      :visibleTrigger_in="addConvertedstandardcoalcoef.visibleTrigger_in"
      :closeTrigger_in="addConvertedstandardcoalcoef.closeTrigger_in"
      :queryId_in="addConvertedstandardcoalcoef.queryId_in"
      :inputData_in="addConvertedstandardcoalcoef.inputData_in"
      :tableData="CetTable_coefficient.data"
      @addConvertedstandardcoalcoefFinished="
        addConvertedstandardcoalcoefFinished
      "
      :projectEnergy_in="projectEnergy"
    ></AddConvertedstandardcoalcoef>
  </div>
</template>
<script>
import custom from "@/api/custom";
import AddProduct from "./AddProduct";
import AddEnergy from "./AddEnergy";
import AddConvertedstandardcoalcoef from "./AddConvertedstandardcoalcoef";
import UnitTransition from "./unitTransition";
import omegaI18n from "@omega/i18n";
import common from "eem-base/utils/common";
export default {
  name: "ProjectCfg",
  components: {
    AddProduct,
    AddEnergy,
    AddConvertedstandardcoalcoef,
    UnitTransition
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    enLanguage() {
      return omegaI18n.locale === "en";
    }
  },

  data(vm) {
    const language = omegaI18n.locale === "en";
    return {
      activeName: "1",
      projectEnergy: [],
      coefficientInfo: {},
      delProductIds: [],
      delEnergyIds: [],
      dleCoefficientIds: [],
      addProduct: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addEnergy: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addConvertedstandardcoalcoef: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      unitTransition: {
        visibleTrigger_in: new Date().getTime()
      },
      CetDialog_1: {
        title: $T("设置"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTable_energy: {
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {
          id_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        "highlight-current-row": false,
        event: {}
      },
      CetButton_addEnergy: {
        visible_in: true,
        disable_in: false,
        title: $T("新增能源"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addEnergy_statusTrigger_out
        }
      },
      CetButton_delEnergy: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        plain: true,
        type: "danger",
        event: {
          statusTrigger_out: this.CetButton_delEnergy_statusTrigger_out
        }
      },
      CetTable_product: {
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      CetButton_addProduct: {
        visible_in: true,
        disable_in: false,
        title: $T("新增产品"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addProduct_statusTrigger_out
        }
      },
      CetButton_delProduct: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        plain: true,
        type: "danger",
        event: {
          statusTrigger_out: this.CetButton_delProduct_statusTrigger_out
        }
      },
      CetTable_coefficient: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "project",
          dataIndex: [],
          modelList: [""],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      CetButton_addCoefficient: {
        visible_in: true,
        disable_in: false,
        title: $T("新增折标系数"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addCoefficient_statusTrigger_out
        }
      },
      CetButton_delCoefficient: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delCoefficient_statusTrigger_out
        }
      },
      ElTableColumn_selection: {
        type: "selection", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "50" //绝对宽度
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "70" //绝对宽度
      },
      ElTableColumn_product_name: {
        prop: "name", // 支持path a[0].b
        label: $T("产品名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "120px" : "100px" //该宽度会自适应
      },
      ElTableColumn_product_type: {
        prop: "producttype", // 支持path a[0].b
        label: $T("产品类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "120px" : "100px", //该宽度会自适应
        formatter(row, column, cellValue) {
          let obj = vm.$store.state.enumerations.producttype.find(
            item => item.id === cellValue
          );
          return obj ? obj.text : "--";
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_product_type_id: {
        prop: "producttype", // 支持path a[0].b
        label: $T("产品类型ID"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "140px" : "100px" //该宽度会自适应
      },
      ElTableColumn_energy_type: {
        prop: "energytype", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "120", //该宽度会自适应
        formatter(row, col, cellValue) {
          let obj = vm.$store.state.enumerations.energytype.find(
            item => item.id === cellValue
          );
          return obj ? obj.text : "--";
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energy_type_id: {
        prop: "energytype", // 支持path a[0].b
        label: $T("能源类型ID"), //列名
        headerAlign: "left",
        align: "left",
        minWidth: language ? "140px" : "100px",
        showOverflowTooltip: true
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "80px" //该宽度会自适应
      },
      ElTableColumn_unit_coefficient: {
        prop: "unitmultiplier", // 支持path a[0].b
        label: $T("单位系数"), //列名
        headerAlign: "left",
        align: "left",
        minWidth: language ? "180px" : "100px",
        showOverflowTooltip: true,
        formatter(row, column, cellValue) {
          let obj = vm.$store.state.enumerations.unitmultiplier.find(
            item => item.id === cellValue
          );
          return obj ? obj.text : "--";
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_unit_price: {
        prop: "unitprice", // 支持path a[0].b
        label: $T("单价（元）"), //列名
        headerAlign: "right",
        align: "right",
        minWidth: language ? "150px" : "100px",
        showOverflowTooltip: true,
        formatter: common.formatNumberCol(2)
      },
      ElTableColumn_efficiency_name: {
        prop: "name", // 支持path a[0].b
        label: $T("能效指标名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_efficiency_unit_type: {
        prop: "unittypeName", // 支持path a[0].b
        label: $T("能效单元类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_energytype: {
        prop: "energytypeName", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_sourceenergytype: {
        prop: "sourceenergytype", // 支持path a[0].b
        label: $T("原能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          var obj = vm.projectEnergy.find(
            item => item.energytype === cellValue
          );
          return (obj && obj.name) || "--";
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_targetenergytype: {
        prop: "targetenergytype", // 支持path a[0].b
        label: $T("目标能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          var obj = vm.projectEnergy.find(
            item => item.energytype === cellValue
          );
          return (obj && obj.name) || "--";
        }
      },
      ElTableColumn_aggregationcycle: {
        prop: "aggregationcycleName", // 支持path a[0].b
        label: $T("分析周期"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_producttype: {
        prop: "producttypeName", // 支持path a[0].b
        label: $T("产品"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_coef: {
        prop: "coef", // 支持path a[0].b
        label: $T("系数"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_coefficient: {
        prop: "coef", // 支持path a[0].b
        label: $T("转换系数"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,

        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      }
    };
  },
  watch: {
    async visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.activeName = "1";
      this.handleClick({
        name: "1"
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    /**
     * 能耗table勾选事件
     */
    selectionEnergyChanged(val) {
      this.delEnergyIds = (val || []).map(item => item.id);
    },

    /**
     * 获取项目能耗类型
     */
    async getProjectEnergytype() {
      const res = await custom.queryProjectEnergyList({
        projectId: this.projectId
      });
      if (res.code !== 0) {
        return;
      }

      let data = res.data || [];
      this.CetTable_energy.data = data;
      this.projectEnergy = this._.cloneDeep(data);
    },
    /**
     * 添加能耗
     */
    CetButton_addEnergy_statusTrigger_out() {
      this.addEnergy.inputData_in = null;
      this.addEnergy.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 批量删除能耗
     */
    CetButton_delEnergy_statusTrigger_out() {
      this.delEnergy(this.delEnergyIds);
    },
    /**
     * 添加或修改能耗成功后返回
     */
    addEnergyFinished() {
      this.getProjectEnergytype();
    },
    /**
     * 编辑单个能耗
     */
    editEnergy(val) {
      this.addEnergy.inputData_in = this._.cloneDeep(val);
      this.addEnergy.visibleTrigger_in = new Date().getTime();
    },

    /**
     * 删除能耗
     */
    delEnergy(delEnergyIds) {
      if (!delEnergyIds?.length) {
        return;
      }

      this.$confirm($T("确定要删除吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const params = {
            projectId: this.projectId,
            energyIds: this.delEnergyIds
          };

          const response = await custom.delEnergy(params);

          if (response.code !== 0) {
            return;
          }

          this.$message.success($T("删除成功"));
          this.getProjectEnergytype();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    /**
     * 获取项目的所有产品
     */
    async getProduct() {
      const res = await custom.queryProductList();
      if (res.code !== 0) {
        return;
      }

      this.CetTable_product.data = res.data;
    },
    /**
     * 产品table勾选事件
     */
    selectionProductChanged(val) {
      this.delProductIds = ([] || val).map(item => item.id);
    },
    /**
     * 添加产品
     */
    CetButton_addProduct_statusTrigger_out() {
      this.addProduct.inputData_in = null;
      this.addProduct.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 批量删除产品
     */
    CetButton_delProduct_statusTrigger_out() {
      this.delProduct(this.delProductIds);
    },
    /**
     * 编辑产品
     * @param val 产品
     */
    editProduct(val) {
      this.addProduct.inputData_in = this._.cloneDeep(val);
      this.addProduct.visibleTrigger_in = new Date().getTime();
    },

    /**
     * 删除产品
     * @param productIds 产品ids
     */
    delProduct(productIds) {
      if (!productIds?.length) {
        return;
      }
      this.$confirm($T("确定要删除吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const params = {
            projectId: this.projectId,
            productIds: productIds
          };
          const response = await custom.delProduct(params);

          if (response.code !== 0) {
            return;
          }

          this.$message.success($T("删除成功"));
          this.getProduct();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    /**
     * 编辑或修改产品完成后回调
     */
    addProductFinished() {
      this.getProduct();
    },
    /**
     * 获取折标系数
     */
    async getConvertedStandardCoalCoef() {
      const query = { projectId: this.projectId };
      const res = await custom.queryConvertedstandardcoalcoefTable(query);
      if (res.code !== 0) {
        return;
      }
      this.CetTable_coefficient.data = res.data;
    },
    /**
     * 新增或修改折标系数成功后回调
     */
    addConvertedstandardcoalcoefFinished() {
      this.getConvertedStandardCoalCoef();
    },
    /**
     * 折标系数table勾选事件
     */
    selectionCoefficientChanged(val) {
      this.dleCoefficientIds = (val || []).map(item => item.id);
    },
    /**
     * 新增折标系数
     */
    CetButton_addCoefficient_statusTrigger_out() {
      this.addConvertedstandardcoalcoef.inputData_in = {};
      this.addConvertedstandardcoalcoef.visibleTrigger_in =
        new Date().getTime();
    },
    /**
     * 批量删除折标系数
     * @param val
     */
    CetButton_delCoefficient_statusTrigger_out() {
      this.delCoefficient(this.dleCoefficientIds);
    },
    /**
     * 删除折标系数
     * @param energyIds 折标系数ids
     */
    delCoefficient(ids) {
      if (!ids?.length) {
        return;
      }
      this.$confirm($T("确定要删除吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const data = ids;
          const params = {
            projectId: this.projectId
          };
          const response = await custom.delConvertedstandardcoalcoef(
            data,
            params
          );
          if (response.code !== 0) {
            return;
          }

          this.$message.success($T("删除成功"));
          this.getConvertedStandardCoalCoef();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消")
          });
        });
    },
    editCoefficient(val) {
      this.addConvertedstandardcoalcoef.inputData_in = this._.cloneDeep(val);
      this.addConvertedstandardcoalcoef.visibleTrigger_in = Date.now();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    handleClick(tab) {
      if (tab.name === "1") {
        this.getProduct();
      } else if (tab.name === "2") {
        this.getProjectEnergytype();
      } else if (tab.name === "3") {
        this.getConvertedStandardCoalCoef();
      } else if (tab.name === "4") {
        this.unitTransition.visibleTrigger_in = Date.now();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.cet-dialog {
  :deep(.el-dialog__body) {
    padding: 0 !important;
    background: var(--BG);
  }
}
.eltabs {
  height: 500px;
  border-radius: 0 !important;
  padding: 0 !important;
  background-color: transparent !important;
  display: flex;
  flex-direction: column;
  :deep(.el-tabs__header) {
    background: var(--BG1);
    margin: 0;
    padding-left: var(--J3);
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
  :deep(.el-tabs__content) {
    background: var(--BG1);
    border-radius: var(--Ra1);
    margin: var(--J1);
    padding: var(--J4);
    flex: 1;
    min-height: 0;
    display: flex;
    .el-tab-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      .table-container {
        flex: 1;
        overflow: auto;
        .el-table__fixed-right {
          height: 100% !important;
        }
      }
    }
  }
}
.table-title {
  margin-bottom: var(--J3);
  line-height: 1.5;
}
.handle {
  cursor: pointer;
  color: var(--ZS);
}
.delete {
  color: var(--Sta3);
}
</style>
