import _ from "lodash";
import fetch from "eem-base/utils/fetch";
/*
  节点树类型
  nodeType: 1:管理层级，2：管网
*/
export const TREE_TYPE = {
  MANAGE: 1,
  NETWORK: 2
};
let config = [
  {
    name: "项目",
    modelLabel: "project",
    children: [
      {
        name: "园区",
        modelLabel: "sectionarea",
        children: [
          {
            name: "楼栋",
            modelLabel: "building",
            children: [
              {
                name: "楼层",
                modelLabel: "floor",
                children: [
                  {
                    name: "房间",
                    modelLabel: "room",
                    children: [
                      {
                        name: "用能设备",
                        modelLabel: "manuequipment",
                        children: null,
                        roomType: null,
                        rootLabel: false,
                        nodeTypes: [1]
                      }
                    ],
                    roomType: null,
                    rootLabel: false,
                    nodeTypes: [1]
                  },
                  {
                    name: "用能设备",
                    modelLabel: "manuequipment",
                    children: null,
                    roomType: null,
                    rootLabel: false,
                    nodeTypes: [1]
                  }
                ],
                roomType: null,
                rootLabel: false,
                nodeTypes: [1]
              },
              {
                name: "用能设备",
                modelLabel: "manuequipment",
                children: null,
                roomType: null,
                rootLabel: false,
                nodeTypes: [1]
              }
            ],
            roomType: null,
            rootLabel: false,
            nodeTypes: [1]
          }
        ],
        roomType: null,
        rootLabel: false,
        nodeTypes: [1]
      },
      {
        name: "楼栋",
        modelLabel: "building",
        children: [
          {
            name: "楼层",
            modelLabel: "floor",
            children: [
              {
                name: "房间",
                modelLabel: "room",
                children: [
                  {
                    name: "用能设备",
                    modelLabel: "manuequipment",
                    children: null,
                    roomType: null,
                    rootLabel: false,
                    nodeTypes: [1]
                  }
                ],
                roomType: null,
                rootLabel: false,
                nodeTypes: [1]
              },
              {
                name: "用能设备",
                modelLabel: "manuequipment",
                children: null,
                roomType: null,
                rootLabel: false,
                nodeTypes: [1]
              }
            ],
            roomType: null,
            rootLabel: false,
            nodeTypes: [1]
          },
          {
            name: "用能设备",
            modelLabel: "manuequipment",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [1]
          }
        ],
        roomType: null,
        rootLabel: false,
        nodeTypes: [1]
      },
      {
        name: "配电房",
        modelLabel: "room",
        children: [
          {
            name: "开关柜或一段线",
            modelLabel: "linesegmentwithswitch",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "变压器",
            modelLabel: "powertransformer",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "电容柜",
            modelLabel: "capacitor",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "母线",
            modelLabel: "busbarsection",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "母联",
            modelLabel: "busbarconnector",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "发电机",
            modelLabel: "generator",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "HVDC",
            modelLabel: "hvdc",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "UPS",
            modelLabel: "ups",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "蓄电池",
            modelLabel: "battery",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "PT柜",
            modelLabel: "ptcabinet",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "计量柜",
            modelLabel: "meteringcabinet",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "列头柜",
            modelLabel: "arraycabinet",
            children: [
              {
                name: "一段线",
                modelLabel: "linesegment",
                children: null,
                roomType: null,
                rootLabel: false,
                nodeTypes: [2]
              }
            ],
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "配电柜",
            modelLabel: "powerdiscabinet",
            children: [
              {
                name: "一段线",
                modelLabel: "linesegment",
                children: null,
                roomType: null,
                rootLabel: false,
                nodeTypes: [2]
              }
            ],
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "开关柜",
            modelLabel: "switchcabinet",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "AVC",
            modelLabel: "avc",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "ATS",
            modelLabel: "ats",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          }
        ],
        roomType: 1,
        rootLabel: false,
        nodeTypes: [2]
      },
      {
        name: "管道房",
        modelLabel: "room",
        children: [
          {
            name: "管道",
            modelLabel: "pipeline",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          },
          {
            name: "泵",
            modelLabel: "pump",
            children: null,
            roomType: null,
            rootLabel: false,
            nodeTypes: [2]
          }
        ],
        roomType: 6,
        rootLabel: false,
        nodeTypes: [2]
      },
      {
        name: "市政总管",
        modelLabel: "civicpipe",
        children: null,
        roomType: null,
        rootLabel: false,
        nodeTypes: [1]
      }
    ],
    roomType: null,
    rootLabel: true,
    nodeTypes: [1]
  }
];
export async function init() {
  const res = await fetch({
    url: `/eem-base/v1/node/nodeTreeLabels`,
    method: "GET"
  });
  config = _.get(res, "data", []) || [];
}

export function hasProp(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}

/*
根据查询条件获取某个配置节点，如:
condition = { 
  modelLabel: "room",
  roomType: 4
}
*/
export const findNode = (condition = {}) => {
  if (_.isEmpty(condition) || _.isEmpty(config)) return;

  function isEqual(item) {
    const keys = Object.keys(condition);
    let result = true;
    keys.forEach(key => {
      if (!hasProp(item, key) || item[key] !== condition[key]) {
        result = false;
      }
    });
    return result;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item.children) {
        const ret = loop(item.children);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(config) || null;
};

/*
获取节点树入参
nodeType: 1:管理层级，2：管网

返回的是subLayerConditions
*/

export const getTreeParams = nodeType => {
  if (!_.isNumber(nodeType) || _.isEmpty(config)) return [];
  const nodeAll = filterNodeType(nodeType);
  let subLayerConditions = [];

  function addSubLayerConditions(conditions, roomtype) {
    const oilConditions = subLayerConditions.find(item => {
      return item.modelLabel === conditions.modelLabel;
    });
    if (!oilConditions) {
      if (conditions.modelLabel === "room" && !_.isEmpty(roomtype)) {
        subLayerConditions.push({
          ...conditions,
          filter: {
            composemethod: true,
            expressions: [
              {
                limit: roomtype,
                operator: "IN",
                prop: "roomtype",
                tagid: 1
              }
            ]
          }
        });
      } else {
        subLayerConditions.push(conditions);
      }
    } else if (conditions.modelLabel === "room" && !_.isEmpty(roomtype)) {
      const limit = _.get(oilConditions, "filter.expressions[0].limit");
      let newLimit;
      if (!_.isEmpty(limit)) {
        newLimit = _.uniq([...roomtype, ...limit]);
      } else {
        newLimit = roomtype;
      }
      oilConditions.filter = {
        composemethod: true,
        expressions: [
          {
            limit: newLimit,
            operator: "IN",
            prop: "roomtype",
            tagid: 1
          }
        ]
      };
    }
  }

  nodeAll.forEach(item => {
    const conditions = {
      modelLabel: item.modelLabel
    };
    const roomtype =
      item.modelLabel === "room" ? [item.roomType || null] : null;
    addSubLayerConditions(conditions, roomtype);
  });

  return subLayerConditions;
};

const filterNodeType = nodeType => {
  const data = _.get(config, "[0].children", []);
  let dataAll = [];
  function loop(items) {
    for (const item of items) {
      if (!_.isEmpty(item.nodeTypes) && item.nodeTypes.includes(nodeType)) {
        dataAll.push(item);
      }
      if (item.children) {
        loop(item.children);
      }
    }
  }
  loop(data);
  return dataAll;
};
