<template>
  <div class="custom-date-picker bg-BG1" :class="{ disabled: isDisabled }">
    <div class="custom-date-picker-label">
      {{ prefix_in }}
    </div>
    <el-date-picker
      v-bind="$attrs"
      v-on="$listeners"
      class="date-picker"
    ></el-date-picker>
  </div>
</template>

<script>
export default {
  name: "CustomElDatePicker",
  props: {
    prefix_in: String
  },
  computed: {
    isDisabled() {
      return (
        "disabled" in this.$attrs &&
        (this.$attrs.disabled || this.$attrs.disabled === "")
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.custom-date-picker {
  display: inline-flex;
  align-items: center;
  border: 1px solid;
  border-radius: var(--Ra);
  border-color: var(--B1);
  &.disabled {
    border-color: var(--B2);
    background-color: var(--BG);
  }

  :deep(.el-input__inner) {
    font-size: var(--Aa);
    padding-left: var(--J2);
    border: none;
    height: 30px;
  }
  :deep(.el-input__prefix) {
    left: auto;
    right: 5px;
  }
  :deep(.el-input__icon.el-range__icon.el-icon-date) {
    position: absolute;
    right: 5px;
  }
  :deep(.el-input__suffix) {
    right: 25px;
  }
}

.custom-date-picker-label {
  display: inline-flex;
  align-items: center;
  color: var(--ZS);
  font-size: var(--Aa);
  margin-left: var(--J2);
  white-space: nowrap;
}
</style>
