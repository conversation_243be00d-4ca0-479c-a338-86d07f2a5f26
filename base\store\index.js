import customApi from "@/api/custom";
import { api } from "@altair/knight";

const modules = {};

const state = {
  // 选择的项目id
  projectId: 1,
  // 存储枚举类
  enumerations: {},
  token: null,
  userInfo: null
};

const mutations = {
  setProjectId(state, val) {
    state.projectId = val;
  },
  setEnumerations(state, val) {
    state.enumerations = val;
  },
  setUserInfo(state, val) {
    state.userInfo = val;
  }
};

const actions = {
  async setEnumerations({ commit }) {
    const res = await customApi.getEnumerations();
    const enumerations = res.data || [];

    // 所有枚举类型
    let allEnumerations = {};
    enumerations.forEach(item => {
      allEnumerations[item.modelLabel] = item.enums;
    });
    commit("setEnumerations", allEnumerations);
  },
  setUserInfo({ commit }) {
    const user = api.getUser()?._user;
    commit("setUserInfo", user);
  }
};

export { modules, state, mutations, actions };
