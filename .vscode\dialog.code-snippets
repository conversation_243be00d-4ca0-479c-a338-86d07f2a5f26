{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-dialog的代码片段
  "cet-dialog-template": {
    "prefix": "cet-dialog-template",
    "body": [
      " <CetDialog",
      "   v-bind=\"CetDialog_$1\"                                              ",
      "   v-on=\"CetDialog_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></CetDialog>                                                        "
    ],
    "description": ""
  },
  "cet-dialog-data": {
    "prefix": "cet-dialog-data",
    "body": [
      "// ${1:设置组件唯一识别字段}弹窗组件                                          ",
      " CetDialog_$1: {                                                       ",
      "   title: \"\",                                                          ",
      "   openTrigger_in: Date.now(),                                   ",
      "   closeTrigger_in: Date.now(),                                   ",
      "   event: {                                                              ",
      "      openTrigger_out:this.CetDialog_$1_openTrigger_out,                                                ",
      "      closeTrigger_out: this.CetDialog_$1_closeTrigger_out                                        ",
      "   }                                                                           ",
      " },                                                                             "
    ],
    "description": ""
  },
  "cet-dialog-method": {
    "prefix": "cet-dialog-method",
    "body": [
      "    CetDialog_$1_openTrigger_out(val) {                                          ",
      "    },                                                                 ",
      "    CetDialog_$1_closeTrigger_out(val) {                                        ",
      "    },                                                                   "
    ],
    "description": ""
  }
}
