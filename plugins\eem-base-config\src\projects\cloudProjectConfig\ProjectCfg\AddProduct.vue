<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div>
      <div class="basic-box-label">
        {{ $T("产品名称") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElInput
        class="mb-J3"
        v-model="ElInput_1.value"
        v-bind="ElInput_1"
        v-on="ElInput_1.event"
      ></ElInput>
      <div class="basic-box-label">
        {{ $T("产品类型") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElSelect
        class="mb-J3"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </ElSelect>
    </div>
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common.js";
import custom from "@/api/custom";
export default {
  name: "AddProduct",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "320px",
        event: {},
        data: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {},
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: this.$store.state.enumerations.producttype,
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      if (this.inputData_in) {
        const data = this.inputData_in;
        this.CetDialog_1.title = $T("修改");
        this.ElInput_1.value = data.name;
        this.ElSelect_1.value = data.producttype;
      } else {
        this.CetDialog_1.title = $T("新建");
        this.ElInput_1.value = null;
        this.ElSelect_1.value = null;
      }
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    async CetButton_confirm_statusTrigger_out() {
      if (!this.ElInput_1.value) {
        this.$message({
          type: "warning",
          message: $T("请填写产品名称")
        });
        return;
      }
      if (!this.ElSelect_1.value) {
        this.$message({
          type: "warning",
          message: $T("请选择产品类型")
        });
        return;
      }
      if (
        this.ElInput_1.value !== this.inputData_in?.name &&
        this.tableData.some(item => item.name === this.ElInput_1.value)
      ) {
        this.$message({
          type: "warning",
          message: $T("产品名称已存在")
        });
        return;
      }
      if (
        this.ElSelect_1.value !== this.inputData_in?.producttype &&
        this.tableData.some(item => item.producttype === this.ElSelect_1.value)
      ) {
        this.$message({
          type: "warning",
          message: $T("产品类型已存在")
        });
        return;
      }

      const params = {
        id: this.inputData_in?.id,
        name: this.ElInput_1.value,
        producttype: this.ElSelect_1.value
      };

      const fn = params.id ? custom.editProduct : custom.addProduct;
      const res = await fn(params);
      if (res.code !== 0) {
        return;
      }

      this.$emit("addProductFinished");
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.$message.success($T("保存成功"));
    }
  }
};
</script>
<style lang="scss" scoped>
.basic-box-label {
  margin-bottom: var(--J1);
}
</style>
