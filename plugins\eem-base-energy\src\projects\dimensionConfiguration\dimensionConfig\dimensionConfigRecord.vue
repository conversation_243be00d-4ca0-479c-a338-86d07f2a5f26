<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="eem-cont-c1 flex-col flex">
      <div class="tableHeader flex-row flex mb-J3" v-if="false">
        <CustomElDatePicker
          class="datePicker"
          v-model="queryTime"
          :prefix_in="$T('查询时段')"
          :firstDayOfWeek="1"
          size="small"
          :clearable="false"
          value-format="timestamp"
          type="daterange"
          unlink-panels
          range-separator="-"
          :start-placeholder="$T('开始日期')"
          :end-placeholder="$T('结束日期')"
          @change="getTableData"
          :picker-options="pickerOptions"
        ></CustomElDatePicker>
      </div>
      <div class="tableBody">
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_status">
            <template slot-scope="scope">
              <el-tag
                :type="`status_${scope.row[ElTableColumn_status.prop]}`"
                effect="plain"
              >
                {{ statusFormat(scope.row[ElTableColumn_status.prop]) }}
              </el-tag>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_detail"></ElTableColumn>
        </CetTable>
      </div>
    </div>
    <template v-slot:footer>
      <span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>

<script>
import common from "eem-base/utils/common.js";
import customApi from "@/api/custom";
export default {
  name: "dimensionConfigRecord",
  props: {
    openTrigger_in: Number,
    closeTrigger_in: Number,
    node_in: Object
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      queryTime: [],
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        title: $T("调整记录"),
        showClose: true,
        width: "1200px"
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        defaultSort: { prop: "enableTime", order: "descending" },
        event: {}
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "50"
        },
        {
          prop: "enableTime", // 支持path a[0].b
          label: $T("生效时间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          sortable: true,
          width: "200",
          formatter: common.formatDateCol("YYYY-MM-DD")
        }
      ],
      ElTableColumn_status: {
        prop: "effectiveStatus", // 支持path a[0].b
        label: $T("生效状态"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "120"
      },
      ElTableColumn_detail: {
        prop: "tagInfo", // 支持path a[0].b
        label: $T("详情"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: (row, column, cellValue) => {
          if (!cellValue?.length) return "--";
          return cellValue
            .map(i => `${i.levelName} ：${i.tagName}`)
            .join(" ； ");
        }
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    init() {
      this.queryTime = [
        this.$moment().add(-6, "d").valueOf(),
        this.$moment().valueOf()
      ];
      this.getTableData();
    },
    async getTableData() {
      if (!this.node_in?.id) {
        this.CetTable_1.data = [];
        return;
      }
      const queryData = {
        configNode: {
          id: this.node_in.id,
          modelLabel: this.node_in.modelLabel
        }
        // endTime: this.$moment(this.queryTime[1]).endOf("d").valueOf(),
        // startTime: this.$moment(this.queryTime[0]).startOf("d").valueOf()
      };
      const res = await customApi.attributedimensionNodeConfigRecord(queryData);
      this.CetTable_1.data = res?.data ?? [];
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    statusFormat(val) {
      const configeffectivestatus =
        this.$store.state.enumerations.configeffectivestatus || [];
      return configeffectivestatus.find(i => i.id === val)?.text ?? "--";
    }
  }
};
</script>

<style lang="scss" scoped>
.tableHeader {
  justify-content: flex-end;
  .datePicker {
    width: 320px;
  }
}
.tableBody {
  height: 450px;
  :deep() {
    .el-tag--status_1 {
      @include font_color(Sta1);
      @include border_color(Sta1);
    }
    .el-tag--status_2 {
      @include font_color(Sta2);
      @include border_color(Sta2);
    }
    .el-tag--status_3 {
      @include font_color(T6);
      @include border_color(T6);
    }
  }
}
</style>
