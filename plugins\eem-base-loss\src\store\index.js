import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";

export default {
  modules: { ...modules },
  state: {
    ...state,
    systemCfg: {
      hideEnergyLossOverview: false,
      alarmAggregationType: null
    },
    standardEnergyType: []
  },
  mutations: {
    ...mutations,
    setSystemCfg(state, val) {
      state.systemCfg = val;
    },
    setStandardEnergyType(state, val) {
      state.standardEnergyType = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.configProperties();
      const config = res.data || {};
      const systemCfg = {
        hideEnergyLossOverview: !!config.hideEnergyLossOverview,
        alarmAggregationType: config.alarmAggregationType
      };
      const standardEnergyType = config.standardEnergyType || [];
      commit("setSystemCfg", systemCfg);
      commit("setStandardEnergyType", standardEnergyType);
    }
  }
};
