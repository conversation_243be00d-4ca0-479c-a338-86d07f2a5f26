<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div slot="title">
      <i class="el-icon-warning"></i>
      <span class="titleName ml-J1">
        {{ $T("存在已经配置过的设备，是否进行添加?") }}
      </span>
    </div>
    <div class="content bg1 mb-J3">
      <div class="text-T3 mb-J1">
        {{
          $T(
            "已生成配置规则：当表计关联的管网类型是管道、开关柜或一段线时，该管网层级需要关联管理层级后，表计才属于已经生成配置；当表计关联的管网类型是其他类型（例：变压器），该表计属于已经生成配置。"
          )
        }}
      </div>
      <TextKeyword
        :text_in="
          $T(
            '共有{0}个设备已经配置过，{1}个设备未配置',
            repeatNodeNames.length,
            addSectionNum
          )
        "
        :keys_in="[repeatNodeNames.length.toString(), addSectionNum.toString()]"
        :keywordClass="'text-ZS'"
        class="mt-J1"
      />
      <div class="p-J2 bg-BG rounded-Ra mt-J1 repeatNodeNames" v-if="!retract">
        {{ repeatNodeNames.join("、") }}
      </div>
      <div class="text-ZS cursor-pointer mt-J1" @click="retractHandle">
        {{ retract ? $T("显示已经配置表计名称") : $T("隐藏已经配置表计名称") }}
      </div>
    </div>
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="mr-J1"
        v-bind="CetButton_add"
        v-on="CetButton_add.event"
        v-if="addSectionNum > 0"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import TextKeyword from "eem-base/components/textKeyword.vue";
export default {
  props: {
    visibleTrigger_in: Number,
    confirmFn: Function,
    repeatNodeNames: Array,
    addSectionNum: Number
  },
  components: {
    TextKeyword
  },
  data() {
    return {
      retract: true,
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "480px",
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("仅添加未配置表计"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.retract = true;
      this.CetDialog_1.openTrigger_in = val;
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.confirmFn && this.confirmFn(true);
    },
    retractHandle() {
      this.retract = !this.retract;
    },
    CetButton_add_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.confirmFn && this.confirmFn();
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body) {
  padding: 0;
}
.el-icon-warning {
  color: var(--ZS);
  font-size: 22px;
}
.titleName {
  font-weight: bold;
  font-size: var(--H3);
}
.content {
  padding: 0 50px;
  .repeatNodeNames {
    max-height: 300px;
    overflow: auto;
  }
}
</style>
