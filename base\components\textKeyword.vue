<template>
  <div class="inline-block">
    <span
      v-for="(item, index) in list"
      :key="index"
      :class="{
        [keywordClass]: item.red
      }"
    >
      {{ item.text }}
    </span>
  </div>
</template>

<script>
export default {
  name: "textKeyword",
  props: {
    text_in: String,
    keys_in: Array,
    keywordClass: {
      type: String,
      default: "text-sta3"
    }
  },
  data() {
    return {
      list: []
    };
  },
  watch: {
    text_in: {
      handler() {
        this.highlightKeyword();
      },
      immediate: true
    },
    keys_in: {
      handler() {
        this.highlightKeyword();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    highlightKeyword() {
      let text = this.text_in;
      if (!text) {
        this.list = [];
        return;
      }
      if (!this.keys_in?.length) {
        this.list = [
          {
            text: text
          }
        ];
        return;
      }
      // 按传入的关键字进行拆解字符
      this.keys_in.forEach(key => {
        text = text.replace(key, `$key$${key}$key$`);
      });
      this.list = text.split("$key$").map(item => {
        return {
          text: item,
          red: !!this.keys_in.includes(item)
        };
      });
    }
  }
};
</script>
