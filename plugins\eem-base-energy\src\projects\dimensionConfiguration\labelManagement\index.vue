<template>
  <div class="page">
    <!-- 暂时无共有标签 -->
    <!-- <div class="public">
      <div style="height: 300px" v-show="retract">
        <span class="title">{{ $T("公有维度标签") }}</span>
        <CetTable
          class="mt-J3"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          style="height: calc(100% - 48px)"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn :label="$T('标签赋值')">
            <template slot-scope="scope">
              <el-tag
                class="ml-J"
                type="info"
                v-for="item in scope.row.tagVoList || []"
                :key="item.id"
              >
                {{ item.name }}
              </el-tag>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <div class="handleRetract" :class="{ 'mt-J3': retract }">
        <span @click="handleRetract" style="cursor: pointer">
          {{ handleText }}
          <i :class="retract ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        </span>
      </div>
    </div> -->

    <dimensionConfigurationPlatform
      :isPublic="false"
      :title_in="$T('私有维度标签')"
      style="height: 0; flex: 1"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import dimensionConfigurationPlatform from "../dimensionConfigurationPlatform";
export default {
  name: "labelManagement",
  components: { dimensionConfigurationPlatform },
  computed: {
    handleText() {
      return `${this.retract ? $T("收起") : $T("展开")}${$T("公有维度标签")}`;
    }
  },
  data() {
    return {
      retract: true,
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "46" //绝对宽度
          //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("标签名称"), //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: 260,
          formatter: common.formatTextCol() //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          prop: "tagType", // 支持path a[0].b
          label: $T("标签类型"), //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: 160,
          formatter: (row, column, cellValue) => {
            const list = [
              { value: 1, label: $T("文本") },
              { value: 2, label: $T("枚举") }
            ];
            return list.find(i => i.value === cellValue)?.label ?? "--";
          }
        }
      ]
    };
  },
  methods: {
    handleRetract() {
      this.retract = !this.retract;
    },
    async getTableData() {
      const queryData = {
        isPublic: true,
        tagType: 0 // 0:查全部
      };
      const res = await customApi.queryAttributedimensionConfig(queryData);
      this.CetTable_1.data = res?.data ?? [];
    }
  },

  mounted() {
    this.retract = false;
    // this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .public {
    border-radius: var(--Ra);
    padding-top: var(--J4);
    padding-left: var(--J4);
    padding-right: var(--J4);
  }
  .title {
    line-height: 32px;
    @include font_size(H3);
    font-weight: bold;
  }
  .handleRetract {
    @include background_color(BG1);
    border-radius: var(--Ra);
    text-align: center;
    @include font_color(ZS);
  }
}
</style>
