<template>
  <div>
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <CetForm
        class="CetForm"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div
          class="bg-BG1 rounded-Ra2 rowBox"
          :class="{ retract: showExpand && retractCard }"
        >
          <el-row :gutter="16">
            <template v-for="(item, index) in fieldList">
              <el-col
                v-show="showFormItem(index, fieldList)"
                :span="item.span || 8"
                :key="item.propertyLabel"
                v-if="!item.show || item.show(CetForm_1.data)"
              >
                <el-form-item
                  v-if="!['pic', 'document'].includes(item.type)"
                  :label="item.name"
                  :prop="item.propertyLabel"
                  :rules="
                    item.validator
                      ? item.validator(CetForm_1.data)
                      : item.rules || []
                  "
                >
                  <template slot="label">
                    <span>{{ item.name }}</span>
                    <el-tooltip
                      :content="item.nameTooltip"
                      effect="light"
                      v-if="item.nameTooltip"
                    >
                      <i class="el-icon-question" v-if="item.nameTooltip"></i>
                    </el-tooltip>
                  </template>
                  <!-- 节点类型 -->
                  <ElSelect
                    v-if="item.type === 'nodeType'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :disabled="!isAdd"
                    @change="nodeTypeChange"
                  >
                    <ElOption
                      v-for="item in ElOption_nodeType.options_in"
                      :key="item[ElOption_nodeType.key]"
                      :label="item[ElOption_nodeType.label]"
                      :value="item[ElOption_nodeType.value]"
                      :disabled="item[ElOption_nodeType.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 能源类型 -->
                  <ElSelect
                    v-else-if="item.type === 'energytype'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in item.filterElOption
                        ? item.filterElOption(ElOption_energytype.options_in)
                        : ElOption_energytype.options_in"
                      :key="item[ElOption_energytype.key]"
                      :label="item[ElOption_energytype.label]"
                      :value="item[ElOption_energytype.value]"
                      :disabled="item[ElOption_energytype.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 设备归类 -->
                  <ElSelect
                    v-else-if="item.type === 'deviceclassification'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in ElOption_deviceclassification.options_in"
                      :key="item[ElOption_deviceclassification.key]"
                      :label="item[ElOption_deviceclassification.label]"
                      :value="item[ElOption_deviceclassification.value]"
                      :disabled="item[ElOption_deviceclassification.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 母线 -->
                  <ElSelect
                    v-else-if="item.type === 'busbarseg'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in ElOption_busbarseg.options_in"
                      :key="item[ElOption_busbarseg.key]"
                      :label="item[ElOption_busbarseg.label]"
                      :value="item[ElOption_busbarseg.value]"
                      :disabled="item[ElOption_busbarseg.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 产品类型 -->
                  <ElSelect
                    v-else-if="item.type === 'product'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in ElOption_product.options_in"
                      :key="item[ElOption_product.key]"
                      :label="item[ElOption_product.label]"
                      :value="item[ElOption_product.value]"
                      :disabled="item[ElOption_product.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 范围选择 -->
                  <div v-else-if="item.type === 'rangeSelection'">
                    <div class="flex-row flex">
                      <div class="flex-auto">
                        <ElInputNumber
                          v-model="CetForm_1.data[item.propertyLabel]"
                          v-bind="ElInputNumber_Float"
                          v-on="ElInputNumber_Float.event"
                          :disabled="!isAdd && item.noEdit"
                        ></ElInputNumber>
                      </div>
                      <template v-for="i in item.relatedLabel">
                        <span class="ml-J0 mr-J0" :key="`${i}span`">~</span>
                        <div class="flex-auto" :key="`${i}div`">
                          <ElInputNumber
                            v-model="CetForm_1.data[i]"
                            v-bind="ElInputNumber_Float"
                            v-on="ElInputNumber_Float.event"
                            :disabled="!isAdd && item.noEdit"
                          ></ElInputNumber>
                        </div>
                      </template>
                    </div>
                  </div>
                  <!-- 枚举 -->
                  <ElSelect
                    v-if="item.type === 'enums'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :placeholder="item.placeholder || ElSelect_1.placeholder"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    :disabled="!isAdd && item.noEdit"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in item.filterElOption
                        ? item.filterElOption(enumerations[item.enumLabel])
                        : enumerations[item.enumLabel]"
                      :key="item[ElOption_enums.key]"
                      :label="item[ElOption_enums.label]"
                      :value="item[ElOption_enums.value]"
                      :disabled="item[ElOption_enums.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!--  自定义枚举 -->
                  <ElSelect
                    v-if="item.type === 'customEnums'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :placeholder="item.placeholder || ElSelect_1.placeholder"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    :disabled="!isAdd && item.noEdit"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in item.customEnums_ElOption"
                      :key="item[ElOption_enums.key]"
                      :label="item[ElOption_enums.label]"
                      :value="item[ElOption_enums.value]"
                      :disabled="item[ElOption_enums.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 文本 -->
                  <ElInput
                    v-else-if="item.type === 'string'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElInput_1"
                    v-on="ElInput_1.event"
                    :placeholder="item.placeholder || ElInput_1.placeholder"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                  ></ElInput>
                  <ElInput
                    v-else-if="item.type === 'textarea'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElInput_1"
                    v-on="ElInput_1.event"
                    type="textarea"
                    :rows="item.rows || 2"
                    :maxlength="item.maxlength || 200"
                    show-word-limit
                    :placeholder="item.placeholder || ElInput_1.placeholder"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                  ></ElInput>

                  <!-- 布尔值 -->
                  <ElSelect
                    v-else-if="item.type === 'boolean'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElSelect_1"
                    :clearable="clearSelect(item)"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                    @clear="
                      clearSelectHandle(CetForm_1.data, item.propertyLabel)
                    "
                  >
                    <ElOption
                      v-for="item in ElOption_boolean.options_in"
                      :key="item[ElOption_boolean.key]"
                      :label="item[ElOption_boolean.label]"
                      :value="item[ElOption_boolean.value]"
                      :disabled="item[ElOption_boolean.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <!-- 数字 -->
                  <ElInputNumber
                    v-else-if="item.type === 'numberFloat'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElInputNumber_Float"
                    v-on="ElInputNumber_Float.event"
                    :placeholder="item.placeholder || ElInput_1.placeholder"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                  ></ElInputNumber>
                  <ElInputNumber
                    v-else-if="item.type === 'numberInt'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElInputNumber_Int"
                    v-on="ElInputNumber_Int.event"
                    :placeholder="item.placeholder || ElInput_1.placeholder"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                  ></ElInputNumber>

                  <!-- 经纬度 -->
                  <div v-else-if="item.type === 'longitude'">
                    <ElInputNumber
                      class="coordinate"
                      :disabled="true"
                      v-model="CetForm_1.data[item.propertyLabel]"
                      v-bind="ElInputNumber_Float2"
                      v-on="ElInputNumber_Float2.event"
                    ></ElInputNumber>
                    <i
                      class="el-icon-close el-input__icon eem-map-icon"
                      style="right: 35px"
                      @click="delteleMap"
                    ></i>
                    <i
                      class="el-icon-edit el-input__icon eem-map-icon"
                      @click="OpenBaiduMap"
                    ></i>
                  </div>
                  <ElInputNumber
                    v-else-if="item.type === 'latitude'"
                    class="coordinate"
                    :disabled="true"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    v-bind="ElInputNumber_Float2"
                    v-on="ElInputNumber_Float2.event"
                  ></ElInputNumber>

                  <!-- 时间 -->
                  <el-date-picker
                    class="date-picker"
                    v-else-if="item.type === 'datePicker'"
                    v-model="CetForm_1.data[item.propertyLabel]"
                    value-format="timestamp"
                    type="date"
                    :editable="false"
                    :pickerOptions="item.pickerOptions || {}"
                    :placeholder="$T('选择日期')"
                    :disabled="!isAdd && item.noEdit"
                    @change="item.change ? item.change(CetForm_1.data) : ''"
                  ></el-date-picker>

                  <!-- 单位 -->
                  <span class="form-item-unit" v-if="item.unit">
                    {{ item.unit }}
                  </span>
                  <!-- 创建后不可修改提示 -->
                  <span
                    v-if="isAdd && item.noEdit"
                    class="el-form-item__error notModifiable"
                  >
                    {{ $T("创建后不可修改") }}
                  </span>
                </el-form-item>
              </el-col>
            </template>
            <el-col
              v-show="showExpand && !retractCard"
              :span="24"
              v-if="CetForm_1.data.modelLabel === 'coldwatermainengine'"
            >
              <el-row :gutter="16">
                <!-- 设备运行区间录入，只有冷水主机需要录入 -->
                <!-- 20250611融合版本暂不支持暂不支持 -->
                <el-col :span="8" v-if="false">
                  <el-form-item>
                    <div class="text-center">
                      <CetButton
                        v-bind="CetButton_range"
                        v-on="CetButton_range.event"
                        class="mb-J1"
                      ></CetButton>
                      <br />
                      <span>
                        {{ $T("用于制冷系统AI优化分析") }}
                      </span>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <div class="mb-J1 uploadDoc text-center">
                    <el-upload
                      ref="curveElupload"
                      action=""
                      :headers="{ Authorization: this.token }"
                      :on-change="handleBeforeUpload3"
                      :on-remove="handleRemove"
                      :multiple="false"
                      :on-exceed="handleExceed"
                      :auto-upload="false"
                      accept=".xls,.xlsx"
                      :file-list="fileList"
                      :http-request="importOperatingEfficiencyCurve"
                    >
                      <el-button size="small" type="primary">
                        {{ $T("上传理论运行效率曲线") }}
                      </el-button>
                      <div slot="tip" class="el-upload__tip">
                        {{
                          $T(
                            "只能上传xls/xlsx格式文件,且不超过{0}M",
                            systemCfg.uploadDocSize || "10"
                          )
                        }}
                      </div>
                    </el-upload>
                  </div>
                </el-col>
                <el-col :span="8">
                  <!-- 下载运行效率曲线模板 -->
                  <CetButton
                    v-bind="CetButton_download"
                    v-on="CetButton_download.event"
                  ></CetButton>
                </el-col>
              </el-row>
            </el-col>
            <el-col
              :span="24"
              v-show="showExpand && !retractCard"
              v-if="documentField"
            >
              <el-form-item
                :label="documentField.name"
                :prop="documentField.propertyLabel"
              >
                <div class="upload mt-J3 mb-J3 fl">
                  <div
                    class="eem-pload-label"
                    :title="documentName"
                    v-if="!isAdd"
                  >
                    {{ documentName || "--" }}
                    <el-button
                      class="ml-10 fr"
                      style="position: absolute; top: 0px; right: 0px"
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      @click="handleDeleteDocument_out"
                    ></el-button>
                  </div>
                  <el-upload
                    class="elupload11"
                    ref="DocElupload"
                    action=""
                    :before-upload="handleBeforeUpload"
                    :multiple="false"
                    :limit="1"
                    :before-remove="beforeRemove"
                    :on-exceed="handleExceed"
                    :http-request="importExceed"
                  >
                    <el-button size="small" type="primary">
                      {{ $T("选择上传文件") }}
                    </el-button>
                    <p>
                      {{
                        $T(
                          "只能上传xls/xlsx/docx/pdf格式文件,且不超过{0}M",
                          systemCfg.uploadDocSize || "10"
                        )
                      }}
                    </p>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <div
            v-if="showExpand"
            class="handle fullwidth text-center text-ZS mt-J3"
            @click="retractCard = !retractCard"
          >
            {{ retractCard ? $T("展开") : $T("收起") }}
            <i class="el-icon-d-arrow-left handleIcon"></i>
          </div>
        </div>
        <div
          class="bg-BG1 rounded-Ra2 rowBox mt-J1"
          v-for="item in picFields"
          :key="item.propertyLabel"
        >
          <el-row :gutter="16">
            <el-col :span="24">
              <el-form-item>
                <div class="label" slot="label">
                  {{ item.name }}
                  <div class="box_tip2">
                    {{
                      $T(
                        "只能上传jpg/png图片，且不超过{0}M，推荐80px*80px大小。",
                        systemCfg.uploadPicSize
                      )
                    }}
                  </div>
                </div>
                <div class="value">
                  <UploadImg
                    class="uploadImg"
                    :imgUrl.sync="CetForm_1.data[item.propertyLabel]"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </CetForm>
      <span slot="footer">
        <CetButton
          class="mr-J1"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <MapLatOrLng
      :visibleTrigger_in="MapLatOrLng.visibleTrigger_in"
      :closeTrigger_in="MapLatOrLng.closeTrigger_in"
      :queryId_in="MapLatOrLng.queryId_in"
      :inputData_in="MapLatOrLng.inputData_in"
      :mapInfo="mapInfo"
      @finishData_out="MapLatOrLng_finishData_out"
    />
    <!-- 设备运行区间录入 -->
    <addWorkRange
      v-bind="addWorkRange"
      v-on="addWorkRange.event"
      :wokeRangeData="wokeRangeData"
    ></addWorkRange>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import MapLatOrLng from "./MapLatOrLng.vue";
import UploadImg from "@/components/uploadImg.vue";
import { getFields } from "@/utils/projectTreeField.js";
import customApi from "@/api/custom";
import addWorkRange from "./addWorkRange.vue";
export default {
  components: {
    MapLatOrLng,
    UploadImg,
    addWorkRange
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    // 当前选中节点
    inputData_in: {
      type: Object
    },
    // 当前选中节点的父节点,或者是目标添加节点的父节点
    fatherNode_in: {
      type: Object
    },
    // 兄弟节点名称列表
    treeNameList_in: {
      type: Array
    },
    // 是否是管网
    netWork: {
      type: Boolean
    },
    // 项目id
    projectId_in: Number
  },

  computed: {
    Fields() {
      return getFields();
    },
    enumerations() {
      return this.$store.state.enumerations;
    },
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    fieldList() {
      if (!this.CetForm_1.data.modelLabel) return [];
      const arr = this.CetForm_1.data.modelLabel.split("_");
      const item = this.Fields.find(item => {
        if (arr[0] === "room") {
          const nodeSubType = Number(arr[1]);
          return item.modelLabel === arr[0] && item.roomType === nodeSubType;
        } else {
          return item.modelLabel === arr[0];
        }
      });
      return item ? item.node_fields : [];
    },
    picFields() {
      if (!this._.isArray(this.fieldList)) return [];
      return this.fieldList.filter(item => item.type === "pic");
    },
    documentField() {
      if (!this._.isArray(this.fieldList)) return [];
      return this.fieldList.find(item => item.type === "document");
    },
    isAdd() {
      return (
        this._.isEmpty(this.inputData_in) ||
        !this._.get(this.inputData_in, "id")
      );
    },
    // 根据字段长度是否展示展开按钮
    showExpand() {
      let num = 0;
      if (this.fieldList) {
        this.fieldList.forEach(item => {
          if (item.type === "document") {
            num += 3;
          } else if (item.type !== "pic") {
            if (item.span) {
              // 8 16 24
              if (item.span <= 8) {
                num++;
              } else if (item.span >= 24) {
                num += 3;
              } else {
                num += 2;
              }
            } else {
              num++;
            }
          }
        });
      }
      if (this.CetForm_1.data.modelLabel === "coldwatermainengine") {
        num += 3;
      }
      if (this.documentField) {
        num += 3;
      }
      return num > 15;
    }
  },

  data() {
    return {
      detailInfo: null,
      retractCard: true,
      mapInfo: {
        areaJson: null,
        point: null
      },
      documentName: "",
      CetDialog_1: {
        title: $T("添加节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        labelWidth: "120px",
        labelPosition: "top",
        rules: {},
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_nodeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElOption_deviceclassification: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_busbarseg: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_product: {
        options_in: [],
        key: "producttype",
        value: "producttype",
        label: "name",
        disabled: "disabled"
      },
      ElOption_boolean: {
        options_in: [
          {
            id: true,
            text: $T("是")
          },
          {
            id: false,
            text: $T("否")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_enums: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        type: "text",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInputNumber_Int: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_Float: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_Float2: {
        ...common.check_numberFloat11,
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请选择"),
        controls: false,
        event: {}
      },
      MapLatOrLng: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addWorkRange: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        isEdit: false,
        event: {
          saveData_out: this.saveWorkRange
        }
      },
      wokeRangeData: [], // 运行区间数据
      currentDevice: {}, // 新建之后的获取设备id
      fileList: [],
      CetButton_range: {
        visible_in: true,
        disable_in: false,
        title: $T("设备运行区间录入"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_range_statusTrigger_out
        }
      },
      CetButton_download: {
        visible_in: true,
        disable_in: false,
        title: $T("下载模板"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_download_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.fileList = [];
      this.wokeRangeData = [];
      this.$nextTick(() => {
        $(this.$refs.CetDialog.$el).scrollTop(0);
        if (vm.$refs.DocElupload) {
          vm.$refs.DocElupload.clearFiles();
        }
      });
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    async init() {
      this.retractCard = true;
      await this.setNodeTypes();
      this.CetDialog_1.title = this.isAdd ? $T("添加节点") : $T("编辑节点");
      this.documentName = "";
      let modelLabel;
      if (!this.isAdd) {
        if (this.inputData_in.modelLabel === "room") {
          modelLabel = `room_${this.inputData_in.roomtype}`;
        } else {
          modelLabel = this.inputData_in.modelLabel;
        }
        const queryData = [
          {
            modelLabel: this.inputData_in.modelLabel,
            id: this.inputData_in.id
          }
        ];
        const res = await customApi.getNodeDetail(queryData);
        this.detailInfo = res?.data?.[0] ?? {};
      }
      this.initForm(modelLabel);
    },
    // 其他自定义类型需要请求接口获取选项
    async initElOption() {
      if (this.fieldList.find(i => i.type === "energytype")) {
        this.ElOption_energytype.options_in = await this.projectEnergy();
      }
      if (this.fieldList.find(i => i.type === "deviceclassification")) {
        this.ElOption_deviceclassification.options_in =
          await this.getDeviceclassification();
      }
      if (this.fieldList.find(i => i.type === "busbarseg")) {
        this.ElOption_busbarseg.options_in = await this.getBusbarseg();
      }
      if (this.fieldList.find(i => i.type === "product")) {
        this.ElOption_product.options_in = await this.getProduct();
      }
      const customEnumsFieldList = this.fieldList.filter(item => {
        return item.type === "customEnums";
      });
      const promiseAll = customEnumsFieldList.map(item => {
        return new Promise(res => {
          if (this._.isArray(item.customEnums)) {
            res(item.customEnums);
          } else {
            item.customEnums().then(data => {
              res(data);
            });
          }
        });
      });
      const customEnumsAll = await Promise.all(promiseAll);
      customEnumsFieldList.forEach((item, index) => {
        item.customEnums_ElOption = customEnumsAll[index];
      });
    },
    async initForm(modelLabel) {
      let formData = {};
      this.CetForm_1.data = {
        modelLabel:
          modelLabel || this._.get(this.ElOption_nodeType, "options_in[0].id")
      };
      // 编辑重置表单
      if (!this.isAdd) {
        this.fieldList.forEach(({ propertyLabel, relatedLabel, type }) => {
          let oldValue = this.detailInfo[propertyLabel];
          if (
            ["numberInt", "numberFloat", "longitude", "latitude"].includes(
              type
            ) &&
            oldValue === null
          ) {
            oldValue = undefined;
          }
          formData[propertyLabel] = oldValue;

          if (relatedLabel) {
            relatedLabel.forEach(i => {
              formData[i] = this.detailInfo[i];
            });
          }
        });
        formData.id = this.detailInfo.id;
        if (this.documentField) {
          let str = this.detailInfo[this.documentField.propertyLabel] || "";
          if (str) {
            const fileName = str.split("\\")[3];
            let fileNameArr = fileName.split("_");
            fileNameArr.length = fileNameArr.length - 1;
            this.documentName = `${fileNameArr.join("_")}.${
              fileName.split(".")[1]
            }`;
          }
        }
        // 暂不支持工作区间写入
        // if (this.CetForm_1.data.modelLabel === "coldwatermainengine") {
        //   this.getWorkSection();
        // }
      } else {
        //新增重置表单，找到默认回填数据
        this.fieldList.forEach(item => {
          if (
            this.hasProp(item, "defaultValue") &&
            !formData[item.propertyLabel]
          ) {
            formData[item.propertyLabel] = item.defaultValue;
          }
        });
      }
      formData.modelLabel =
        modelLabel || this._.get(this.ElOption_nodeType, "options_in[0].id");

      await this.initElOption();
      this.CetForm_1.data = formData;
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    getWorkSection() {
      let data = {
        id: this.inputData_in.id,
        modelLabel: this.inputData_in.modelLabel
      };
      customApi.queryWorkSection(data).then(res => {
        if (res.code === 0) {
          this.wokeRangeData = res.data;
        }
      });
    },
    nodeTypeChange(modelLabel) {
      this.initForm(modelLabel);
    },
    // 找可新建/编辑的节点类型
    async setNodeTypes() {
      const queryData = {
        nodeTreeGroupId: this.netWork ? 1 : 2,
        parentLabel: this.fatherNode_in?.modelLabel,
        parentSubType: this.fatherNode_in?.roomtype ?? null
      };
      const res = await customApi.findTreeConfigNode(queryData);
      const nodeRelationList = res.data?.[0].nodeRelationList ?? [];

      let options = [];
      nodeRelationList.forEach(item => {
        const optionsItem = {
          id:
            item.nodeType === "room"
              ? `${item.nodeType}_${item.nodeSubType || null}`
              : item.nodeType,
          text: item.nodeSubTypeName || item.nodeTypeName
        };
        options.push(optionsItem);
      });
      this.ElOption_nodeType.options_in = options;
    },
    CetForm_1_saveData_out() {
      // 检查名称是否重复
      let NameList = this.treeNameList_in || [],
        nodeName = this.CetForm_1.data.name;
      for (let index = 0, le = NameList.length; index < le; index++) {
        if (NameList[index] === nodeName) {
          if (!this.isAdd && this.inputData_in.name === nodeName) {
            continue;
          }
          this.$message.warning($T("节点名称重复！"));
          return;
        }
      }
      this.addNode();
    },
    hasProp(obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key);
    },
    getParams() {
      let params = this._.cloneDeep(this.CetForm_1.data);

      // 检查时间选择关系
      let cooperativedeadline = params.cooperativedeadline
          ? new Date(params.cooperativedeadline).getTime()
          : null,
        commissiondate = params.commissiondate
          ? new Date(params.commissiondate).getTime()
          : null,
        manufacturedate = params.manufacturedate
          ? new Date(params.manufacturedate).getTime()
          : null;
      const lastoverhauldate = this.CetForm_1.data.lastoverhauldate;
      const nextoverhauldate = this.CetForm_1.data.nextoverhauldate;
      if (lastoverhauldate && nextoverhauldate) {
        if (lastoverhauldate >= nextoverhauldate) {
          this.$message.warning($T("下次检修时间不能小于等于上次检修日期！"));
          return;
        }
      }
      if (cooperativedeadline && commissiondate) {
        if (commissiondate >= cooperativedeadline) {
          this.$message.warning($T("合作截止时间不能小于等于投运时间！"));
          return;
        }
      }
      if (manufacturedate && commissiondate) {
        if (manufacturedate >= commissiondate) {
          this.$message.warning($T("投运时间不能小于等于出厂时间！"));
          return;
        }
      }
      if (this.hasProp(params, "cooperativedeadline")) {
        params.cooperativedeadline = cooperativedeadline;
      }
      if (this.hasProp(params, "commissiondate")) {
        params.commissiondate = commissiondate;
      }
      if (this.hasProp(params, "manufacturedate")) {
        params.manufacturedate = manufacturedate;
      }

      // 取字段默认value
      this.fieldList.forEach(item => {
        if (
          this.hasProp(item, "saveDefaultValue") &&
          !params[item.propertyLabel]
        ) {
          params[item.propertyLabel] = item.saveDefaultValue;
        }
      });

      // 补上父节点
      params.children = [
        {
          id: this.fatherNode_in.id,
          modelLabel: this.fatherNode_in.modelLabel
        }
      ];

      // 房间节点补上房间类型
      const arr = params.modelLabel.split("_");
      params.modelLabel = arr[0];
      if (params.modelLabel === "room") {
        params.roomtype = Number(arr[1]);
      }

      // 经纬度如果是undefined 则转成null
      if (params.longitude === undefined) {
        params.longitude = null;
      }
      if (params.latitude === undefined) {
        params.latitude = null;
      }
      // 将undefined转为null
      Object.keys(params).forEach(item => {
        if (params[item] === undefined) {
          params[item] = null;
        }
      });

      return params;
    },
    async addNode() {
      const params = this.getParams();
      if (!params) return;
      const fn = this.isAdd
        ? customApi.nodeBatchAdd
        : customApi.nodeBatchUpdate;
      const res = await fn([params]);

      if (res.code !== 0) return;
      this.currentDevice = this._.cloneDeep(res.data?.[0]);
      if (!this.currentDevice) {
        this.CetDialog_1.closeTrigger_in = new Date().getTime();
        this.$emit("saveData_out");
        return;
      }
      // 暂不支持工作区间写入
      // if (this.CetForm_1.data.modelLabel === "coldwatermainengine") {
      //   // 保存设备运行区间
      //   this.saveWorkSection(res.data[0]);
      // }
      if (
        this.CetForm_1.data.modelLabel === "coldwatermainengine" &&
        this.fileList.length
      ) {
        // 冷水主机导入运行效率曲线
        this.$refs.curveElupload.submit();
      } else {
        this.CetDialog_1.closeTrigger_in = new Date().getTime();
      }
      this.$emit("saveData_out");
    },
    CetButton_range_statusTrigger_out() {
      this.addWorkRange.inputData_in = this._.cloneDeep(this.inputData_in);
      this.addWorkRange.openTrigger_in = new Date().getTime();
    },
    saveWorkRange(val) {
      this.wokeRangeData = this._.cloneDeep(val);
      this.addWorkRange.closeTrigger_in = new Date().getTime();
    },
    // 运行效率曲线模板下载
    CetButton_download_statusTrigger_out(val) {
      let url = "eem-base/v1/parameterConfig/exportOperatingEfficiencyCurve";
      let data = {
        id: this.inputData_in?.id,
        modelLabel: this.inputData_in?.modelLabel,
        name: this.inputData_in?.name,
        ratedrefrigeration: this.CetForm_1.data?.ratedrefrigeration
      };
      common.downExcel(url, data, this.token);
    },
    // 写入工作区间配置
    saveWorkSection(val) {
      if (!val) return;
      let objectId = val.id;
      let objectLabel = val.modelLabel;
      let params = {
        objectId,
        objectLabel
      };
      let data = this.wokeRangeData.map(item => {
        return {
          id: item.id,
          sectiontype: item.sectiontype,
          sectiondatatype: item.sectiondatatype,
          min: item.min,
          max: item.max
        };
      });
      if (!data.length) return;
      customApi.writeWorkSection(params, data).then(() => {});
    },
    // 地图相关
    MapLatOrLng_finishData_out(val) {
      if (!val) {
        return;
      }
      this.$set(this.CetForm_1.data, "longitude", val.lng);
      this.$set(this.CetForm_1.data, "latitude", val.lat);
      this.CetForm_1.data.locationrange = val.areaJson;
    },
    OpenBaiduMap() {
      this.mapInfo.areaJson = this.CetForm_1.data.locationrange;
      this.mapInfo.point = {
        latitude: this.CetForm_1.data.latitude,
        longitude: this.CetForm_1.data.longitude
      };
      this.MapLatOrLng.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    delteleMap() {
      this.CetForm_1.data.longitude = undefined;
      this.CetForm_1.data.latitude = undefined;
      this.CetForm_1.data.locationrange = "";
    },
    // 文档相关
    handleExceed() {
      this.$message.warning($T("当前限制选择 1 个文件"));
    },
    beforeRemove(file) {
      this.CetForm_1.data[this.documentField.propertyLabel] = "";
    },
    //    上传前
    handleBeforeUpload: function (file) {
      if (this.documentName) {
        this.$message.warning($T("当前限制选择 1 个文件"));
        return false;
      }
      if (
        file.name.indexOf(".xls") != -1 ||
        file.name.indexOf(".xlsx") != -1 ||
        file.name.indexOf(".docx") != -1 ||
        file.name.indexOf(".pdf") != -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 10;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error($T("上传文件超过规定的最大上传大小"));
        }
        return isLimit100M;
      } else {
        this.$message({
          type: "warning",
          message: $T("只能上传xls/xlsx/docx/pdf格式文件")
        });
        return false;
      }
    },
    async importExceed({ file }) {
      const data = new FormData();
      data.append("file", file);
      const response = await customApi.commonUploadFile(data);
      if (response.code !== 0) {
        var tips = "";
        if (response.data) {
          for (var i = 0; i < response.data.length; i++) {
            tips = tips + response.data[i] + "<br/>";
          }
        } else {
          tips = response.msg;
        }
        this.$message({
          type: "error",
          message: tips
        });
        return;
      }

      this.CetForm_1.data[this.documentField.propertyLabel] = response.data;
      this.$message({
        message: $T("上传成功"),
        type: "success"
      });
    },
    handleDeleteDocument_out() {
      this.documentName = "";
      this.CetForm_1.data[this.documentField.propertyLabel] = "";
    },

    // 运行效率曲线上传
    handleBeforeUpload3(file) {
      this.loading = true;
      if (file.name.indexOf(".xls") != -1 || file.name.indexOf(".xlsx") != -1) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 10;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.fileList = [];
          this.$message.error($T("上传文件超过规定的最大上传大小"));
        } else {
          this.fileList = [file];
        }
        return isLimit100M;
      } else {
        this.fileList = [];
        this.$message({
          type: "warning",
          message: $T("只能上传xls/xlsx格式文件")
        });
        return false;
      }
    },
    handleRemove() {
      this.fileList = [];
    },
    /**
     * 上传理论运行效率曲线
     */
    importOperatingEfficiencyCurve(val) {
      let params = {
        id: this.currentDevice.id,
        label: this.currentDevice.modelLabel
      };
      const data = new FormData();
      data.append("file", val.file);
      customApi.importOperatingEfficiencyCurve(params, data).then(res => {
        if (res.code === 0) {
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    },
    async projectEnergy() {
      const res = await customApi.queryProjectEnergyList();
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getDeviceclassification() {
      const res = await customApi.deviceClassification();
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getBusbarseg() {
      const params = {
        parentNode: {
          modelLabel: "project",
          id: this.projectId_in
        },
        subLabelList: ["busbarsection"]
      };

      const res = await customApi.queryChildNodeByLabel(params);
      if (res.code !== 0) return [];
      return this._.get(res.data, "[0].children", []);
    },
    async getProduct() {
      const res = await customApi.queryProductList();
      if (res.code !== 0) return [];
      return this._.get(res, "data", []);
    },
    // 判断下拉框是否添加清空功能
    clearSelect(val) {
      let arr = val.validator ? val.validator() : val.rules || [];
      if (arr.find(item => item.required === true)) {
        return false;
      }
      return true;
    },
    clearSelectHandle(data, prop) {
      this.$set(data, prop, null);
    },
    showFormItem(index_in, fieldList) {
      if (!this.showExpand || !this.retractCard) {
        return true;
      }
      let num = 0;
      fieldList.forEach((item, index) => {
        if (
          index <= index_in &&
          (!item.show || item.show(this.CetForm_1.data))
        ) {
          if (!["pic", "document"].includes(item.type)) {
            if (item.span) {
              // 8 16 24
              if (item.span <= 8) {
                num++;
              } else if (item.span >= 24) {
                num += 3;
              } else {
                num += 2;
              }
            } else {
              num++;
            }
          }
        }
      });
      return num <= 15;
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  .CetForm {
    background-color: transparent;
    padding: 0;
    :deep(.el-form-item__content) {
      line-height: 1;
    }
  }
  .rowBox {
    padding: var(--J3) var(--J4);
    :deep(.el-row) {
      height: auto;
      overflow: initial;
    }
    :deep(.el-form-item__error) {
      min-width: 100%;
      background-color: var(--BG1);
    }
    .handleIcon {
      transform: rotate(90deg);
    }
    &.retract {
      .handleIcon {
        transform: rotate(-90deg);
      }
    }
    .date-picker {
      width: 100%;
    }
  }

  .upload,
  .elupload11 {
    width: 100%;
    :deep(.el-upload) {
      width: 100%;
    }
  }
}

.uploadImg {
  width: 100px;
  height: 100px;
}
.box_tip2 {
  display: inline-block;
  font-size: var(--Ab);
  color: var(--T3);
}
.eem-map-icon {
  position: absolute;
  right: 10px;
  z-index: 1;
  top: -3px;
  cursor: pointer;
}
.handle {
  cursor: pointer;
}
.notModifiable {
  color: var(--T4);
}
</style>
