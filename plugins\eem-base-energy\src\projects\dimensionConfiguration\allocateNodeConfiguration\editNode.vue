<template>
  <!-- 弹窗组件 -->
  <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
    <div class="p-J4 bg1 content">
      <div
        v-for="(item, index) in valueList"
        :key="index"
        class="item"
        :class="{
          errorMsgItem: item.errorMsg
        }"
      >
        <span class="label">{{ item.key }}</span>
        <div
          style="flex: 1; position: relative; width: 0"
          :class="{ 'bg-BG': index === 0, m0: !index }"
        >
          <div
            class="value text-ellipsis"
            v-if="!item.type || item.type === 'text'"
            :title="item.value"
          >
            {{ item.value }}
          </div>
          <ElInput
            class="value"
            v-if="item.type === 'input'"
            v-model="item.value"
            :placeholder="$T('请输入')"
            @input="nameValueInput(item, index)"
          ></ElInput>
          <ElSelect
            class="value"
            v-if="item.type === 'select'"
            filterable
            :allow-create="item.tagType === 1"
            default-first-option
            clearable
            v-model="item.value"
            @focus="valueFocus(item, index)"
          >
            <ElOption
              v-for="it in item.options"
              :key="it"
              :label="it"
              :value="it"
            ></ElOption>
          </ElSelect>
          <CustomElDatePicker
            v-if="item.type === 'date'"
            class="value w-full custom-date-picker"
            v-model="item.value"
            type="date"
            size="small"
            :placeholder="$T('选择日期')"
            :pickerOptions="common.pickerOptions_earlierThanYesterd11"
            clearable
          ></CustomElDatePicker>
          <span v-if="item.errorMsg" class="errorMsg">
            {{ item.errorMsg }}
          </span>
        </div>
      </div>
    </div>
    <template v-slot:footer>
      <span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  name: "addTree",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    parentNode: Object
  },
  data() {
    return {
      common,
      CetDialog_pagedialog: {
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "",
        width: "800px",
        appendToBody: true,
        event: {}
      },
      nodeConfigData: {}, // 接口请求到的节点维度的相关数据
      basicValueList: [
        { key: $T("维度属性") },
        {
          key: $T("节点名称"),
          prop: "configNode.name",
          value: "",
          type: "input"
        },
        { key: $T("生效时间"), prop: "enableTime", value: "", type: "date" }
      ],
      valueList: [],
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async openTrigger_in(val) {
      this.CetDialog_pagedialog.title = this.inputData_in.name + $T("维度属性");
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      this.valueList = this._.cloneDeep(this.basicValueList);
      await Promise.all([this.getTagList(), this.queryColumnName()]);
      this.queryNodeConfig();
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  },
  methods: {
    async queryNodeConfig() {
      const queryData = {
        id: this.inputData_in.id,
        modelLabel: this.inputData_in.modelLabel
      };
      const res = await customApi.attributedimensionNodeConfigQuery(queryData);
      const resData = res?.data?.[0] ?? {};
      this.nodeConfigData = resData;
      this.valueList.forEach(item => {
        // 管理层级赋值
        if (item.type === "text") {
          const value = resData.manageNodeName?.find(
            i => i.modelLabel === item.prop
          )?.name;
          this.$set(item, "value", value);
          return;
        }
        // 节点名称、生效时间赋值
        if (item.prop) {
          const value = this._.get(resData, item.prop, "") || "";
          this.$set(item, "value", value);
          return;
        }
        // 标签赋值
        if (item.type === "select") {
          const tagName =
            (resData.configVo || []).find(i => i.levelName === item.key)
              ?.tagName ?? "";
          this.$set(item, "value", tagName);
          return;
        }
      });
    },
    async queryColumnName() {
      const res = await customApi.attributedimensionNodeConfigColumnName();
      const resData = (res?.data || []).map(item => ({
        key: item.name,
        prop: item.modelLabel,
        value: "",
        type: "text"
      }));
      this.valueList.splice(2, 0, ...resData);
    },
    // 查询维度标签和赋值的列表
    async getTagList() {
      const res = await customApi.attributedimensionNodeConfigTag();
      const resData = (res?.data ?? []).map(i => {
        return {
          key: i.name,
          value: "",
          type: "select",
          options: i.tagNameList,
          tagType: i.tagType
        };
      });
      const listLength = this.valueList.length - 1;
      this.valueList.splice(listLength, 0, ...resData);
    },
    async CetButton_preserve_statusTrigger_out() {
      const name = this.valueList.find(i => i.prop === "configNode.name").value;
      if (name === "") {
        this.$message.warning($T("请输入节点名称"));
        return;
      }
      this.valueList.forEach((item, index) => {
        if (item.type !== "select" || item.tagType !== 1) return;
        this.valueFocus(item, index);
      });
      if (this.valueList.find(item => item.errorMsg)) return;

      const enableTime = this.valueList.find(
        i => i.prop === "enableTime"
      ).value;

      const configList = this.valueList.reduce((pre, cur) => {
        if (cur.type === "select" && cur.value) {
          pre.push(cur.value);
        }
        return pre;
      }, []);

      if (
        (!enableTime && configList.length) ||
        (enableTime && !configList.length)
      ) {
        this.$message.warning($T("生效时间、标签赋值不同时为空，请检查数据"));
        return;
      }

      const queryData = [
        {
          configNode: {
            id: this.inputData_in.id,
            modelLabel: this.inputData_in.modelLabel,
            name
          },
          configVo: this.valueList.reduce((pre, cur) => {
            if (cur.type === "select" && !this._.isEmpty(cur.value)) {
              pre.push({
                levelName: cur.key,
                tagName: cur.value
              });
            }
            return pre;
          }, []),
          enableTime,
          manageNodeName: this.valueList.reduce((pre, cur) => {
            if (cur.type === "text") {
              pre.push({
                modelLabel: cur.prop,
                name: cur.value
              });
            }
            return pre;
          }, [])
        }
      ];

      const res = await customApi.attributedimensionNodeConfigEdit(queryData);
      if (res.code !== 0) return;

      this.$message.success($T("操作成功"));
      this.$store.dispatch("importProgress/noticeProgress", {
        vm: this,
        initialProcessInfo: res.data,
        cb: async () => {
          this.$emit("finishTrigger_out");
        }
      });
      this.CetDialog_pagedialog.closeTrigger_in = Date.now();
    },
    valueFocus({ value, tagType }, index) {
      if (tagType !== 1) return;
      if (value.length && !common.check_space.pattern.test(value)) {
        this.$set(
          this.valueList[index],
          "errorMsg",
          common.check_space.message
        );
        return;
      }
      if (value.length > 50) {
        this.$set(
          this.valueList[index],
          "errorMsg",
          $T("长度在 1 到 {0} 个字符", 50)
        );
        return;
      }
      this.$set(this.valueList[index], "errorMsg", "");
    },
    nameValueInput({ value }, index) {
      if (
        value.length > common.check_name.max ||
        value.length < common.check_name.min
      ) {
        this.$set(this.valueList[index], "errorMsg", common.check_name.message);
        return;
      }
      if (!common.check_space.pattern.test(value)) {
        this.$set(
          this.valueList[index],
          "errorMsg",
          common.check_space.message
        );
        return;
      }
      if (!common.check_pattern_name.pattern.test(value.trim())) {
        this.$set(
          this.valueList[index],
          "errorMsg",
          common.check_pattern_name.message
        );
        return;
      }
      if (
        (this.parentNode?.children || []).find(
          i => i.name === value && i.tree_id !== this.inputData_in.tree_id
        )
      ) {
        this.$set(this.valueList[index], "errorMsg", $T("节点名称重复！"));
        return;
      }
      this.$set(this.valueList[index], "errorMsg", "");
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  overflow-y: auto;
  border-radius: var(--Ra1);
  height: 100%;
  box-sizing: border-box;
  .item {
    height: 40px;
    display: flex;
    border-bottom: 1px solid;
    @include border_color(B1);
    .label {
      border-right: 1px solid;
      @include border_color(B1);
      @include padding(0 J4);
      width: 160px;
      line-height: 40px;
      @include background_color(BG);
    }
    .value {
      width: calc(100% - 48px);
      line-height: 40px;
      @include margin(0 J4);
    }
  }
  .item.errorMsgItem {
    height: 64px;
    .label {
      line-height: 64px;
    }
    .errorMsg {
      position: absolute;
      left: mh-get(J4);
      bottom: mh-get(J1);
      @include font_color(Sta3);
      @include font_size(Ab);
    }
    :deep(.el-input__inner) {
      @include border_color(Sta3);
    }
  }
}
:deep(.el-dialog__body) {
  height: 480px;
}
:deep(.el-input__inner) {
  @include padding(0 J3);
}
.value.custom-date-picker {
  line-height: 32px;
  margin-top: 4px !important;
  :deep() {
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
      line-height: 1;
    }
  }
}
</style>
