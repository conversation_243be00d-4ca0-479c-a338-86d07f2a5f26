<template>
  <div class="pageContain page1">
    <el-button type="primary" id="btn1">第一步</el-button>
    <el-button type="primary" id="btn2">第二步</el-button>
    <el-button type="primary" id="btn3" @click="onPushClick">
      去引导页面2
    </el-button>
  </div>
</template>

<script>
import { api } from "@altair/knight";

import useCounter from "@/hooks/useDriver.js";
const [Driver] = useCounter();

export default {
  name: "noviceGuide-page1",
  data() {
    return {};
  },
  mounted() {
    this.initDriver();
  },
  methods: {
    initDriver() {
      const { step } = api.getRouterQuery();
      if (!step) return;
      Driver.setSteps([
        {
          element: "#btn1",
          popover: {
            title: $T("第一步"),
            description: "Description",
            onNextClick: () => {
              console.log("next1");
              Driver.moveNext();
            },
            onPrevClick: () => {
              console.log("prev");
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#btn2",
          popover: {
            title: "第二步",
            description: "Description",
            onNextClick: () => {
              console.log("next2");
              Driver.moveNext();
            },
            onPrevClick: () => {
              console.log("prev");
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#btn3",
          popover: {
            title: "去引导页面2",
            description: "Description",
            onNextClick: () => {
              console.log("next3");
              this.onPushClick();
              Driver.moveNext();
            },
            onPrevClick: () => {
              console.log("prev");
              Driver.movePrevious();
            }
          }
        }
      ]);

      Driver.drive();
    },
    onPushClick() {
      const config = {
        path: "/fusion/sub/noviceGuidePage2"
      };
      api.subRouterQuery(config);
    }
  }
};
</script>

<style lang="scss" scoped>
.page1 {
  background-color: var(--BG1);
}
</style>
