import fetch from "eem-base/utils/fetch";

/**
 * 查询计费方案
 */
export function getSchemeConfigFeeScheme(projectId, params) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/feeScheme/${projectId}`,
    method: "GET",
    params
  });
}

/**
 * 查询分时费率记录
 */
export function getSchemeConfigFeeRecordTimeShare(id, energytype) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/feeRecord/timeShare/${id}/${energytype}`,
    method: "GET"
  });
}

/**
 * 查询容需量费率记录
 */
export function demandMaintaingGetFeeSchemeItem(params) {
  return fetch({
    url: `/eem-service/v1/demand/maintain/getFeeSchemeItem`,
    method: "GET",
    params
  });
}

/**
 * 查询其他费率记录
 */
export function schemeConfigFeeRecord(feeRecordType, id) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/feeRecord/${feeRecordType}/${id}`,
    method: "GET"
  });
}

/**
 * 删除费率方案
 */
export function deleteSchemeConfig(id) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/feeScheme/${id}`,
    method: "DELETE"
  });
}

/**
 * 删除费率记录
 */
export function deleteSchemeConfigFeeRecord(id, modelLabel) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/feeRecord/${id}/${modelLabel}`,
    method: "DELETE"
  });
}

// 查询所有能源类型、费用类型、费率类型
export function queryEnergySchemeConfig(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/energys`,
    method: "POST",
    data
  });
}

/**
 * 查询分时费率方案列表
 */
export function schemeConfigTimeShareScheme(params) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/timeShareScheme`,
    method: "GET",
    params
  });
}
/**
 * 查询分时费率方案详情
 */
export function timeShareSchemeDetail(id) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/timeShareScheme/${id}`,
    method: "GET"
  });
}

/**
 * 保存容量计费
 */
export function saveVolumeFeeScheme(data) {
  return fetch({
    url: `/eem-service/v1/demand/maintain/saveVolumeFeeScheme`,
    method: "POST",
    data
  });
}

/**
 * 保存需量计费
 */
export function saveDemandFeeScheme(data) {
  return fetch({
    url: `/eem-service/v1/demand/maintain/saveDemandFeeScheme`,
    method: "POST",
    data
  });
}

/**
 * 保存单一费率
 */
export function singleFeeRecord(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/singleFeeRecord`,
    method: "PUT",
    data
  });
}

/**
 * 保存分时费率
 */
export function timeShareFeeRecord(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/timeShareFeeRecord`,
    method: "PUT",
    data
  });
}

/**
 * 保存阶梯费率
 */
export function stageFeeRecord(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/stageFeeRecord`,
    method: "PUT",
    data
  });
}

/**
 * 保存力调电费
 */
export function powerTariffFeeRecord(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/powerTariffFeeRecord`,
    method: "PUT",
    data
  });
}

/**
 * 导入力调费率表
 */
export function importPowerTariff(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/importPowerTariff`,
    method: "PUT",
    data
  });
}
