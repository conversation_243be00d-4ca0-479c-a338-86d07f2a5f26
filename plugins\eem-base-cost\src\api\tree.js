import fetch from "eem-base/utils/fetch";
// 查询维度节点树列表（包含id为-1的固定管理层级）
export function getAttributeDimensionTreeNodeConfig(data, params) {
  return fetch({
    url: `/eem-service/v1/attribute-dimension/tree/node-config`,
    method: "POST",
    data,
    params
  });
}

export function getNodeTreeSimple(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree/simple`,
    method: "POST",
    data
  });
}

// 成本配置-多维度成本分析节点树
export function queryCostcheckplanMultitree(data) {
  return fetch({
    url: `/eem-service/v2/cost-check-plan/multi-tree`,
    method: "POST",
    data
  });
}
