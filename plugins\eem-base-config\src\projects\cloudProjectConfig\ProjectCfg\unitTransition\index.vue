<template>
  <div class="h-full">
    <div class="h-full flex flex-col">
      <div class="mb-J3">
        <CetButton
          class="fr"
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
          v-permission="'nodeconfig_update'"
        ></CetButton>
        <CustomElSelect
          :prefix_in="typeName"
          class="fr mr-J1"
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </CustomElSelect>
        <CustomElSelect
          :prefix_in="$T('类型归类')"
          class="fr mr-J1"
          v-model="ElSelect_unitClass.value"
          v-bind="ElSelect_unitClass"
          v-on="ElSelect_unitClass.event"
        >
          <ElOption
            v-for="item in ElOption_unitClass.options_in"
            :key="item[ElOption_unitClass.key]"
            :label="item[ElOption_unitClass.label]"
            :value="item[ElOption_unitClass.value]"
            :disabled="item[ElOption_unitClass.disabled]"
          ></ElOption>
        </CustomElSelect>
      </div>
      <div class="flex-auto">
        <CetTable
          height="100%"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_energyType"
            :label="typeName"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_basicUnitSymbolName"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_uniten"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_unitcn"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_coef"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_handele"
            v-if="$checkPermission('nodeconfig_update')"
          >
            <template slot-scope="scope">
              <span class="handel fl mr-J3" @click="handelEdit(scope.row)">
                {{ $T("编辑") }}
              </span>
              <span class="handel fl delete" @click="handelDelete(scope.row)">
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </div>
    <add
      :projectEnergytype="ElOption_energyType.options_in"
      :visibleTrigger_in="add.visibleTrigger_in"
      :editData_in="add.editData_in"
      :tableData_in="CetTable_1.data"
      :unitClass_in="ElSelect_unitClass.value"
      :closeTrigger_in="add.closeTrigger_in"
      :typeName="typeName"
      @updata_out="getData"
    />
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import add from "./add.vue";
import { CustomElSelect } from "eem-base/components";
import omegaI18n from "@omega/i18n";
export default {
  name: "unitTransition",
  components: {
    add,
    CustomElSelect
  },
  props: {
    visibleTrigger_in: {
      type: Number
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    typeName() {
      const typeNameMap = {
        1: $T("能耗类型"),
        2: $T("产品类型"),
        4: $T("其他类型")
      };
      return typeNameMap[this.ElSelect_unitClass.value];
    }
  },

  data() {
    const language = omegaI18n.locale === "en";
    return {
      add: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        editData_in: null
      },
      ElSelect_energyType: {
        value: "",
        style: {
          width: language ? "310px" : "200px"
        },
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_unitClass: {
        value: 1,
        style: {
          width: language ? "310px" : "200px"
        },
        event: {
          change: this.ElSelect_unitClass_change_out
        }
      },
      ElOption_unitClass: {
        options_in: [
          {
            id: 1,
            name: $T("能耗")
          },
          {
            id: 2,
            name: $T("产品")
          },
          {
            id: 4,
            name: $T("其他")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {}
        },
        //组件输入项
        data: [],
        dynamicInput: {
          projectUnitClassify_in: null,
          energyType_in: null,
          projectId: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElTableColumn_index: {
        type: "index",
        width: "70",
        label: $T("序号"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_handele: {
        width: language ? 130 : 110, //绝对宽度
        label: $T("操作"),
        headerAlign: "left",
        align: "left",
        fixed: "right",
        showOverflowTooltip: true
      },
      ElTableColumn_energyType: {
        prop: "typeName",
        minWidth: language ? "220px" : "120px",
        label: $T("能耗类型"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_basicUnitSymbolName: {
        prop: "basicUnitSymbolName",
        minWidth: "120px",
        label: $T("基础单位"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_uniten: {
        prop: "uniten",
        minWidth: language ? "180px" : "120px",
        label: $T("目标单位(英文)"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_unitcn: {
        prop: "unitcn",
        minWidth: language ? "180px" : "120px",
        label: $T("目标单位(中文)"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_coef: {
        prop: "coef",
        minWidth: "120px",
        label: $T("系数"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      productList: [],
      energyList: [],
      otherUnitTypeList: []
    };
  },
  watch: {
    visibleTrigger_in() {
      this.init();
    }
  },

  methods: {
    async init() {
      this.CetTable_1.data = [];
      this.queryOtherUnitType_out();
      const promiseArr = [
        this.queryProductList_out(),
        this.queryProjectEnergyList_out()
      ];

      await Promise.all(promiseArr);
      this.ElSelect_unitClass_change_out(this.ElSelect_unitClass.value);
    },
    //获取产品计量参数列表
    async queryProductList_out() {
      const params = {
        projectId: this.projectId
      };
      const response = await commonApi.queryProductList(params);

      this.productList = response.data || [];
    },
    // 获取能耗计量参数列表
    async queryProjectEnergyList_out() {
      const params = {
        projectId: this.projectId
      };
      const response = await commonApi.queryProjectEnergyList(params);
      this.energyList = response.data || [];
    },
    // 获取其他类型列表
    queryOtherUnitType_out() {
      const list = this.$store.state.enumerations.otherunittype || [];
      this.otherUnitTypeList = list.map(item => {
        return {
          id: item.id,
          name: item.text
        };
      });
    },
    async getData() {
      const queryData = {
        type: null,
        projectId: this.projectId,
        projectUnitClassify: this.ElSelect_unitClass.value
      };
      const list = this.ElOption_energyType.options_in || [];
      const id = this.ElSelect_energyType.value;
      if (!id) {
        return;
      }
      const selectItem = list.find(item => item.id === id);

      const keyMap = {
        1: "energytype",
        2: "producttype",
        4: "id"
      };
      const key = keyMap[this.ElSelect_unitClass.value];
      queryData.type = selectItem[key];
      const res = await commonApi.getUnitTransition(queryData);
      this.CetTable_1.data = res.data || [];
    },
    handelEdit(row) {
      this.add.editData_in = this._.cloneDeep(row);
      this.add.visibleTrigger_in = new Date().getTime();
    },
    handelDelete(row) {
      this.$confirm($T("确定要删除吗?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const response = await commonApi.deleteUnitTransition([row.id]);
          if (response.code !== 0) {
            return;
          }
          this.$message.success($T("删除成功"));
          this.getData();
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    ElSelect_energyType_change_out() {
      this.getData();
    },
    ElSelect_unitClass_change_out(val) {
      this.ElSelect_energyType.value = null;
      this.ElOption_energyType.options_in = [];

      const optionsMap = {
        1: this.energyList,
        2: this.productList,
        4: this.otherUnitTypeList
      };

      this.ElOption_energyType.options_in = this._.cloneDeep(optionsMap[val]);

      this.ElSelect_energyType.value =
        this.ElOption_energyType.options_in?.[0]?.id ?? null;
      this.ElSelect_energyType_change_out();
    },
    CetButton_add_statusTrigger_out() {
      this.add.editData_in = null;
      this.add.visibleTrigger_in = new Date().getTime();
    }
  }
};
</script>
<style lang="scss" scoped>
.handel {
  cursor: pointer;
  color: var(--ZS);
}
.delete {
  color: var(--Sta3);
}
</style>
