import fetch from "eem-base/utils/fetch";

/**
 * 获取核算方案列表
 */
export function costCheckPlanList(projectId) {
  return fetch({
    url: `/eem-service/v2/cost-check-plan/${projectId}`,
    method: "GET"
  });
}

/**
 * 删除核算方案
 */
export function costCheckPlanDelete(id) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/costCheckPlan/${id}`,
    method: "DELETE"
  });
}

/**
 * 保存核算方案
 */
export function costCheckPlanSave(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/costCheckPlan`,
    method: "PUT",
    data
  });
}

/**
 * 获取核算方案的成本构成项
 */
export function getCostCheckPlanDetail(id) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/costCheckPlan/plan/${id}`,
    method: "GET"
  });
}

/**
 * 删除成本构成项
 */
export function costCheckPlanComponentDelete(data) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/costCheckPlan/component`,
    method: "DELETE",
    data
  });
}

/**
 * 获取费率列表
 */
export function getSchemeConfigFeeScheme(projectId, params) {
  return fetch({
    url: `/eem-service/v1/schemeConfig/feeScheme/${projectId}`,
    method: "GET",
    params
  });
}
