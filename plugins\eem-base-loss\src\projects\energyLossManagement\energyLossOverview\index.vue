<template>
  <div class="fullfilled">
    <div class="fullfilled flex-col flex" v-show="!showDetail">
      <div class="flex flex-row mb-J3 items-center">
        <ElInput
          class="mr-J3"
          v-model.trim="ElInput_search.value"
          v-bind="ElInput_search"
          v-on="ElInput_search.event"
        >
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </ElInput>
        <customElSelect
          class="mr-J3"
          v-model="ElSelect_energytype.value"
          v-bind="ElSelect_energytype"
          v-on="ElSelect_energytype.event"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_energytype.options_in"
            :key="item[ElOption_energytype.key]"
            :label="item[ElOption_energytype.label]"
            :value="item[ElOption_energytype.value]"
            :disabled="item[ElOption_energytype.disabled]"
          ></ElOption>
        </customElSelect>

        <el-radio-group
          class="mr-J3"
          v-model="ElSelect_3.value"
          v-on="ElSelect_3.event"
        >
          <el-radio-button
            v-for="item in ElOption_3.options_in"
            :label="item[ElOption_3.value]"
            :key="item[ElOption_3.value]"
          >
            {{ item[ElOption_3.label] }}
          </el-radio-button>
        </el-radio-group>
        <CetButton
          v-bind="CetButton_prv"
          v-on="CetButton_prv.event"
        ></CetButton>
        <CustomElDatePicker
          class="ml-J0"
          :prefix_in="cycleName[ElSelect_3.value]"
          v-bind="CetDatePicker_time.config"
          v-model="CetDatePicker_time.val"
          @change="getTableData"
        />
        <CetButton
          class="ml-J0"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
      </div>
      <div
        class="overview clearfix mb-J3 flex flex-row"
        v-if="!hideEnergyLossOverview"
      >
        <div class="bg-BG1 rounded-Ra p-J3 flex-auto mr-J3">
          <div class="label text-ellipsis">{{ $T("总损耗率") }}</div>
          <div
            class="value text-ellipsis mt-J1"
            :title="formatRate(totalLossRate, 2, '%')"
          >
            {{ formatRate(totalLossRate, 2, "%") }}
          </div>
        </div>
        <div class="bg-BG1 rounded-Ra p-J3 flex-auto mr-J3">
          <div class="label text-ellipsis">
            {{ $T("总损耗") }}({{ symbol }})
          </div>
          <div
            class="value text-ellipsis mt-J1"
            :title="formatValue(totalLoss)"
          >
            {{ formatValue(totalLoss) }}
          </div>
        </div>
        <div class="bg-BG1 rounded-Ra p-J3 flex-auto mr-J3">
          <div class="label text-ellipsis">{{ $T("损耗正常率") }}</div>
          <div
            class="value text-ellipsis mt-J1"
            :title="formatRate(normalLossRate, 2, '%')"
          >
            {{ formatRate(normalLossRate, 2, "%") }}
          </div>
        </div>
        <div class="bg-BG1 rounded-Ra p-J3 flex-auto mr-J3">
          <div class="label text-ellipsis">
            {{ $T("损耗正常环节数量(个)") }}
          </div>
          <div
            class="value text-ellipsis mt-J1"
            :title="formatValue(normalLossCount, 0)"
          >
            {{ formatValue(normalLossCount, 0) }}
          </div>
        </div>
        <div class="bg-BG1 rounded-Ra p-J3 flex-auto">
          <div class="label text-ellipsis">
            {{ $T("损耗异常环节数量(个)") }}
          </div>
          <div
            class="value text-ellipsis mt-J1"
            :title="formatValue(abnormalLossCount, 0)"
          >
            {{ formatValue(abnormalLossCount, 0) }}
          </div>
        </div>
      </div>
      <div class="flex-auto flex-col flex p-J4 bg-BG1 rounded-Ra">
        <CetTable
          ref="CetTable"
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn
            type="index"
            :label="$T('序号')"
            width="70"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name">
            <template slot-scope="{ row }">
              <el-tooltip
                :content="`${row.name}<br>${row.name}`"
                placement="top"
              >
                <div slot="content">
                  <div>
                    {{ `${row.name}${row.changeNodeData?.length ? ":" : ""}` }}
                  </div>
                  <div
                    v-for="(item, index) in row.changeNodeData ?? []"
                    :key="index"
                  >
                    {{ item.name }}
                  </div>
                </div>
                <div class="custom-tooltip text-ellipsis">{{ row.name }}</div>
              </el-tooltip>
            </template>
          </ElTableColumn>
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_totalLossRate">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row[ElTableColumn_totalLossRate.prop] > 0.05
                    ? 'text-Sta3'
                    : ''
                "
              >
                {{ formatRate(scope.row[ElTableColumn_totalLossRate.prop], 2) }}
              </span>
            </template>
          </ElTableColumn>
          <template v-for="item in Columns_2">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn
            :label="$T('操作')"
            :width="language ? 130 : 90"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <span class="handle text-ZS" @click.stop="analyse(scope)">
                {{ $T("损耗分析") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
        <div class="mt-J3 text-right">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentPageChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
          />
        </div>
      </div>
    </div>
    <Detail
      v-show="showDetail"
      v-bind="detail"
      @back_out="detail_back"
      :energytypePptions_in="ElOption_energytype.options_in"
    />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import { CustomElDatePicker } from "eem-base/components";
import Detail from "./detail/index.vue";
import common from "eem-base/utils/common";
import omegaI18n from "@omega/i18n";
export default {
  name: "energyLossOverview",
  components: {
    CustomElDatePicker,
    Detail
  },
  computed: {
    language() {
      return omegaI18n.locale === "en";
    },
    hideEnergyLossOverview() {
      return this.$store.state.systemCfg.hideEnergyLossOverview;
    },
    standardEnergyType() {
      return this.$store.state.standardEnergyType;
    }
  },
  data(vm) {
    return {
      symbol: "--",
      totalLoss: null,
      totalLossRate: null,
      normalLossRate: null,
      normalLossCount: null,
      abnormalLossCount: null,
      showDetail: false,
      common: common,
      ElInput_search: {
        value: "",
        placeholder: $T("请输入监测对象"),
        style: {
          width: "300px"
        },
        event: {
          change: this.searchChange
        }
      },
      ElSelect_energytype: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.getTableData
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 14,
        style: {
          width: "180px"
        },
        event: {
          change: this.ElSelect_3_change
        }
      },
      cycleName: {
        12: $T("选择日期"),
        14: $T("选择月份"),
        17: $T("选择年份")
      },
      cycle: {
        12: "d",
        14: "month",
        17: "year"
      },
      ElOption_3: {
        options_in: [
          { id: 12, text: $T("日") },
          { id: 14, text: $T("月") },
          { id: 17, text: $T("年") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_time: {
        val: this.$moment().add(0, "month").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          clearable: false,
          pickerOptions: {
            // 添加回到今天的快捷键
            shortcuts: [
              {
                text: this.$T("当月"),
                onClick(picker) {
                  picker.$emit("pick", new Date());
                }
              }
            ]
          }
        }
      },
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      pageSize: 10,
      currentPage: 1,
      totalCount: 0,
      fliterNameTableData: [],
      originalTableData: [],
      sortTableData: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "total, sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        cellClassName: ({ row, column }) => {
          if (row.changeNodeData?.length && column.property === "name") {
            return "node-merge";
          }
        },
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          "sort-change": this.CetTable_1_sortChange_out
        }
      },
      Columns_1: [
        {
          prop: "roomName", // 支持path a[0].b
          label: $T("房间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "130", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "totalLoss", // 支持path a[0].b
          "render-header": h => {
            return h("span", $T("总损耗") + `(${vm.symbol})`);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          sortable: "custom",
          minWidth: "160", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(2)
        }
      ],
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("监测对象"), //列名
        headerAlign: "left",
        align: "left",
        minWidth: "150", //该宽度会自适应
        formatter: common.formatTextCol()
      },
      ElTableColumn_totalLossRate: {
        prop: "totalLossRate", // 支持path a[0].b
        label: $T("总损耗率") + "(%)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: false,
        sortable: "custom",
        minWidth: "180" //该宽度会自适应
      },
      Columns_2: [
        {
          prop: "normalLossRate", // 支持path a[0].b
          label: $T("损耗正常率") + "(%)", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          sortable: "custom",
          minWidth: "180", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (cellValue || cellValue === 0) {
              return `${common.formatNum((cellValue * 100).toFixed2(2))}`;
            }
            return "--";
          }
        },
        {
          prop: "normalLossCount", // 支持path a[0].b
          label: $T("损耗正常环节数量(个)"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          sortable: "custom",
          minWidth: "190", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(0)
        },
        {
          prop: "abnormalLossCount", // 支持path a[0].b
          label: $T("损耗异常环节数量(个)"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          sortable: "custom",
          minWidth: "190", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(0)
        }
      ],
      detail: {
        initTrigger_in: new Date().getTime(),
        date_in: null,
        energytype_in: null,
        inputData_in: null,
        searchData_in: null,
        objectOptions_in: []
      }
    };
  },
  watch: {
    "ElSelect_energytype.value": {
      handler: function (val) {
        let obj = this.ElOption_energytype.options_in.find(
          item => item.energytype === val
        );
        this.symbol = obj?.symbol || "--";
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.totalLoss = null;
      this.totalLossRate = null;
      this.normalLossRate = null;
      this.normalLossCount = null;
      this.pageSize = 20;
      this.currentPage = 1;
      this.totalCount = 0;
      this.fliterNameTableData = [];
      this.originalTableData = [];
      this.sortTableData = [];
      this.showDetail = false;
      this.ElInput_search.value = "";
      this.ElSelect_energytype.value = null;
      this.ElSelect_3.value = 14;
      this.CetDatePicker_time.val = this.$moment().add(0, "month").valueOf();
      this.CetDatePicker_time.config.type = "month";
      this.CetDatePicker_time.config.pickerOptions.shortcuts[0].text =
        this.$T("当月");
      this.$nextTick(() => {
        this.$refs.CetTable.$refs.cetTable.clearSort();
      });
      this.getProjectEnergy();
    },
    initTable() {
      this.CetTable_1.data = [];
      this.totalCount = 0;
    },
    ElSelect_3_change() {
      let cycle = {
        12: "date",
        14: "month",
        17: "year"
      };
      this.CetDatePicker_time.config.type = cycle[this.ElSelect_3.value];
      this.CetDatePicker_time.config.pickerOptions.shortcuts[0].text =
        this.ElSelect_3.value == 12
          ? this.$T("今天")
          : this.ElSelect_3.value == 14
          ? this.$T("当月")
          : this.$T("今年");
      this.getTableData();
    },
    CetButton_prv_statusTrigger_out() {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date
        .subtract(1, this.cycle[this.ElSelect_3.value])
        .valueOf();
      this.getTableData();
    },
    CetButton_next_statusTrigger_out() {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date
        .add(1, this.cycle[this.ElSelect_3.value])
        .valueOf();
      this.getTableData();
    },
    getTableData() {
      const vm = this;
      if (!vm.ElSelect_energytype.value) {
        vm.initTable();
        return;
      }
      vm.$nextTick(() => {
        vm.$refs.CetTable.$refs.cetTable.clearSort();
      });
      let date = vm.$moment(vm.CetDatePicker_time.val);
      let queryData = {
        energyType: vm.ElSelect_energytype.value,
        startTime: date.startOf(vm.cycle[vm.ElSelect_3.value]).valueOf(),
        endTime: date.endOf(vm.cycle[vm.ElSelect_3.value]).valueOf() + 1,
        aggregationCycle: vm.ElSelect_3.value
      };
      customApi
        .lossConfigOverview(queryData, { keepTransformer: false })
        .then(res => {
          if (res.code === 0) {
            let tableData = vm._.get(res, "data.nodeList", []) || [];
            let objectOptions = tableData.map(item => {
              return {
                name: item.name,
                id: item.id,
                modelLabel: item.modelLabel,
                tree_id: `${item.modelLabel}_${item.id}`
              };
            });
            vm.detail.objectOptions_in = objectOptions;
            vm.fliterNameTableData = vm._.cloneDeep(tableData);
            vm.originalTableData = vm._.cloneDeep(tableData);
            vm.sortTableData = vm._.cloneDeep(tableData);
            vm.totalCount = tableData.length;
            vm.searchChange(vm.ElInput_search.value);
            vm.totalLoss = vm._.get(res, "data.totalLoss");
            vm.totalLossRate = vm._.get(res, "data.totalLossRate");
            vm.normalLossRate = vm._.get(res, "data.normalLossRate");
            vm.normalLossCount = vm._.get(res, "data.normalLossCount");
            vm.abnormalLossCount = vm._.get(res, "data.abnormalLossCount");
          }
        });
    },
    // 关键字过滤
    searchChange(val) {
      let tableData = this.fliterNameTableData.filter(
        item => item.name.toUpperCase().indexOf(val.toUpperCase()) !== -1
      );
      this.originalTableData = this._.cloneDeep(tableData);
      this.sortTableData = this._.cloneDeep(tableData);
      this.totalCount = tableData.length;
      this.$refs.CetTable.$refs.cetTable.clearSort();
      this.handleCurrentPageChange(1);
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentPageChange(1);
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.CetTable_1.data = this._.cloneDeep(
        this.sortTableData.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
      );
    },
    CetTable_1_sortChange_out({ order, prop }) {
      const vm = this;
      if (!order) {
        vm.sortTableData = vm._.cloneDeep(vm.originalTableData);
      } else {
        let obj = {
          ascending: (a, b) => a[prop] - b[prop],
          descending: (a, b) => b[prop] - a[prop]
        };
        let originalTableData = vm._.cloneDeep(vm.originalTableData);
        originalTableData.sort(obj[order]);
        vm.sortTableData = originalTableData;
      }
      vm.$refs.CetTable.orders = null;
      vm.handleCurrentPageChange(1);
    },
    analyse(scope) {
      this.showDetail = true;
      this.detail.date_in = {
        time: this.CetDatePicker_time.val,
        timeType: this.ElSelect_3.value
      };
      this.detail.energytype_in = this.ElSelect_energytype.value;
      this.detail.inputData_in = this._.cloneDeep(scope.row);
      this.detail.initTrigger_in = new Date().getTime();
    },
    detail_back() {
      this.showDetail = false;
    },
    async getProjectEnergy() {
      const response = await customApi.getProjectEnergy();
      if (response.code !== 0) {
        return;
      }

      const data = response.data || [];
      this.ElOption_energytype.options_in = data.filter(
        item => this.standardEnergyType.indexOf(item.energytype) === -1
      );

      this.ElSelect_energytype.value =
        this.ElOption_energytype.options_in?.[0].energytype;
      this.getTableData();
    },
    formatValue(val, precision = 2, unit = "") {
      if (val || val === 0) {
        return `${common.formatNum(val.toFixed2(precision))}${unit}`;
      }
      return "--";
    },
    formatRate(val, precision = 2, unit = "") {
      if (val || val === 0) {
        return `${common.formatNum((val * 100).toFixed2(precision))}${unit}`;
      }
      return "--";
    },
    formatNumberColWithPercision(precision) {
      return function (row, column, cellValue) {
        return common.formatNum(
          common.formatNumberWithPrecision(cellValue, precision)
        );
      };
    }
  },
  mounted() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.overview {
  margin-left: 0 - var(--J1);
  margin-right: 0 - var(--J1);
  & > div {
    width: 20%;
    box-sizing: border-box;
    float: left;
    .label {
      @include font_color(T3);
    }
    .value {
      font-weight: bold;
      @include font_size(H);
      @include line_height(H);
    }
  }
}
.handle {
  cursor: pointer;
}
.datePicker :deep(.el-date-editor.el-input) {
  width: 220px !important;
}
:deep() {
  .el-table__row {
    .node-merge {
      background-color: rgba(#ffc531, 0.15);
    }
  }
}
</style>
