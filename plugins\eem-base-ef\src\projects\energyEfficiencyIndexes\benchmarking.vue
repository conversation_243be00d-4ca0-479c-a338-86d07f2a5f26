<template>
  <div class="h-full flex flex-col">
    <div
      class="border border-B1 border-solid rounded-Ra flex flex-col flex-auto p-J3"
    >
      <div class="flex flex-row items-center justify-between">
        <div>
          {{ indicatorType_in?.name ?? "--" }}
        </div>

        <el-checkbox v-model="maxAndMin" @change="checkChange">
          {{ $T("最值") }}
        </el-checkbox>
      </div>

      <CetChart
        class="flex-auto"
        ref="cetChart"
        v-bind="CetChart_energyConsume"
      ></CetChart>
    </div>
    <div class="flex flex-row items-center h-[320px] mt-J3">
      <div
        class="flex-[9] border border-B1 border-solid rounded-Ra h-full flex flex-col p-J3 box-border"
      >
        <div class="flex flex-row justify-between items-center">
          <span class="text-H3 font-bold">{{ $T("能效最值") }}</span>
          <span class="">
            {{ $T("单位") }}:{{ indicatorType_in?.symbol ?? "--" }}
          </span>
        </div>
        <el-row class="mt-J3 row-item">
          <el-col :span="8">{{ currentText }} KPI {{ $T("最大值") }}</el-col>
          <el-col :span="16">
            <span>
              {{ formatterDate(historyData.maxTime, currentStr) }}
            </span>
            <el-tooltip
              :content="
                common.formatNum(
                  formatNumberWithPrecision(historyData.maxValue, 3)
                )
              "
              effect="light"
            >
              <span class="fs28 text-ellipsis maxVal">
                {{
                  common.formatNum(
                    formatNumberWithPrecision(historyData.maxValue, 3)
                  )
                }}
              </span>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row class="mt-J3 row-item">
          <el-col :span="8">{{ currentText }} KPI {{ $T("最小值") }}</el-col>
          <el-col :span="16">
            <span>
              {{ formatterDate(historyData.minTime, currentStr) }}
            </span>
            <el-tooltip
              :content="
                common.formatNum(
                  formatNumberWithPrecision(historyData.minValue, 3)
                )
              "
              effect="light"
            >
              <span class="fs28 text-ellipsis minVal">
                {{
                  common.formatNum(
                    formatNumberWithPrecision(historyData.minValue, 3)
                  )
                }}
              </span>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row class="mt-J3 row-item">
          <el-col :span="8">{{ currentText }} KPI {{ $T("平均值") }}</el-col>
          <el-col :span="16">
            <span>{{ formatAvgVal }}</span>
            <el-tooltip
              :content="
                common.formatNum(
                  formatNumberWithPrecision(historyData.avgValue, 3)
                )
              "
              effect="light"
            >
              <span class="fs28 text-ellipsis avgVal">
                {{
                  common.formatNum(
                    formatNumberWithPrecision(historyData.avgValue, 3)
                  )
                }}
              </span>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
      <div
        class="history flex-[6] border border-B1 border-solid rounded-Ra ml-J3 mr-J3 h-full p-J3 box-border flex flex-col"
      >
        <div class="flex flex-row justify-between items-center">
          <span class="text-H3 font-bold">{{ $T("历史最优值") }}</span>
          <span class="">
            {{ $T("单位") }}:{{ indicatorType_in?.symbol ?? "--" }}
          </span>
        </div>

        <div
          class="flex-auto content"
          :class="{
            dark: !isLight
          }"
        >
          <el-tooltip
            :content="
              common.formatNum(
                formatNumberWithPrecision(bestData.extremevalue, 3)
              )
            "
            effect="light"
            placement="top"
          >
            <span class="bestVal text-ellipsis">
              {{
                common.formatNum(
                  formatNumberWithPrecision(bestData.extremevalue, 3)
                )
              }}
            </span>
          </el-tooltip>
          <span class="time">
            {{ formatterDate(bestData.extremetime, currentStr) }}
          </span>
        </div>
      </div>
      <div
        class="flex-[9] border border-B1 border-solid rounded-Ra h-full p-J3 box-border flex flex-col"
      >
        <div class="flex flex-row justify-between items-center">
          <span class="text-H3 font-bold">{{ $T("能效对标") }}</span>
          <span class="">
            {{ $T("单位") }}:{{ indicatorType_in?.symbol ?? "--" }}
          </span>
        </div>
        <div class="mt-J3">{{ $T("时间") }}:{{ benchmarkingTime }}</div>
        <CetChart class="flex-auto" v-bind="CetChart_benchmarking"></CetChart>
      </div>
    </div>
  </div>
</template>
<script>
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import { getEventGradeColor } from "@/utils/eventColor.js";
const ENERGY_CONSUME_OPTIONS = {
  tooltip: {
    trigger: "axis"
  },
  title: {
    text: "",
    left: "center"
  },
  legend: {
    top: "10px"
  },
  grid: {
    left: "15px",
    right: "45px",
    top: "45px",
    bottom: "10px",
    containLabel: true
  },
  xAxis: [
    {
      type: "category",
      name: "",
      nameLocation: "end",
      data: [],
      axisPointer: {
        type: "shadow"
      }
    }
  ],
  yAxis: [
    {
      type: "value",
      name: "",
      nameLocation: "end"
    }
  ],
  series: []
};
const MARKPOINT = {
  symbolSize: 60,
  label: {
    show: true,
    fontSize: 10,
    fontWeight: 700,
    formatter: val => {
      return val.value || val.value === 0 ? val.value.toFixed(3) : "--";
    }
  },
  data: [
    {
      type: "max",
      name: $T("最大值"),
      itemStyle: {
        color: omegaTheme.theme === "light" ? "#6ade9a" : "#0c82f8",
        opacity: 1
      }
    },
    {
      type: "min",
      name: $T("最小值"),
      itemStyle: {
        color: omegaTheme.theme === "light" ? "#6ade9a" : "#0c82f8",
        opacity: 1
      }
    }
  ]
};
const MARKLINE = {
  symbol: "none",
  precision: 4,
  data: [
    {
      type: "average",
      label: {
        formatter: $T("能效平均值"),
        color: omegaTheme.theme === "light" ? "#54C081" : "#0D71DA",
        fontSize: 14,
        position: "insideEndTop"
      },
      lineStyle: {
        color: omegaTheme.theme === "light" ? "#54C081" : "#0D71DA",
        width: 2
      }
    }
  ]
};
export default {
  name: "BenchmarkingComponent",
  props: {
    indicatorType_in: Object,
    queryTime_in: Object,
    clickNode_in: Object,
    energyType_in: Number,
    update_in: Number,
    dimConfigId_in: Number,
    rootNode_in: Object
  },

  computed: {
    currentText() {
      const cycle = this.queryTime_in?.cycle;
      const nameMap = {
        7: $T("时"),
        12: $T("日"),
        13: $T("周"),
        14: $T("月"),
        17: $T("年")
      };
      return nameMap[cycle] || "--";
    },
    currentStr() {
      const cycle = this.queryTime_in?.cycle;

      const currentStrMap = {
        7: $T("YYYY年MM月DD日HH时"),
        12: $T("YYYY年MM月DD日"),
        13: $T("YYYY年第ww周"),
        14: $T("YYYY年MM月"),
        17: $T("YYYY年")
      };
      return currentStrMap[cycle];
    },
    formatAvgVal() {
      const cycle = this.queryTime_in?.cycle;
      const startTime = this.queryTime_in?.startTime;
      const endTime = this.queryTime_in?.endTime;
      const str =
        cycle === 7
          ? $T("YYYY年MM月DD日")
          : cycle === 12
          ? $T("YYYY年MM月")
          : $T("YYYY年");
      if (cycle === 17) {
        return (
          this.$moment(startTime).format(str) +
          " -" +
          this.$moment(endTime).subtract(1, "y").format(str)
        );
      }
      return str ? this.$moment(startTime).format(str) : "";
    },
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  data(vm) {
    return {
      common,
      maxAndMin: true,
      // 单位产量能耗
      CetChart_energyConsume: {
        //组件输入项
        inputData_in: null,
        options: this._.cloneDeep(ENERGY_CONSUME_OPTIONS)
      },
      levelOneDataList: null,
      levelTwoDataList: null,
      levelThreeDataList: null,
      historyData: {}, // 能效最值数据
      bestData: {}, // 历史最优值
      benchmarkingTime: "--",
      // 能效对标
      CetChart_benchmarking: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter(params) {
              return (
                params[0].name +
                common.formatNum(
                  vm.formatNumberWithPrecision(params[0].value, 3)
                )
              );
            }
          },
          grid: {
            left: "3%",
            right: "10%",
            top: "5%",
            bottom: "-20px",
            containLabel: true
          },
          xAxis: {
            show: false,
            type: "value",
            max: function (value) {
              return value.max * 1.25;
            },
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: "category",
            axisLine: { show: false },
            axisTick: {
              show: false
            },
            axisLabel: {
              fontSize: 16,
              formatter(params) {
                params = params.toString();
                var maxlength = 5;
                if (params.length > maxlength) {
                  return params.substring(0, maxlength - 1) + "...";
                } else {
                  return params;
                }
              }
            },
            data: []
          },
          series: [
            {
              type: "bar",
              barWidth: 20,
              label: {
                show: true,
                position: "right",
                fontSize: 16,
                formatter: function (params) {
                  if (params.value == 0) {
                    //为0时不显示
                    return "";
                  } else {
                    return common.formatNum(params.value);
                  }
                }
              },
              data: [],
              showBackground: true
            }
          ]
        }
      }
    };
  },
  watch: {
    update_in() {
      this.getChartData();
      this.getMaxMinAvgEnergyEfficiency();
      this.getHistoryOptimumValue();
      this.getEnergyEfficiencyBenchmark();
    }
  },
  methods: {
    init() {
      this.CetChart_energyConsume.options = this._.cloneDeep(
        ENERGY_CONSUME_OPTIONS
      );
      this.historyData = {};
      this.bestData = {};
      this.benchmarkingTime = "--";
      this.CetChart_benchmarking.options.series[0].data = [];
      this.CetChart_benchmarking.options.yAxis.data = [
        `${$T("计划目标")}:`,
        `${$T("历史最优")}:`,
        `${$T("能效实绩")}:`
      ];
    },
    getQueryBody() {
      if (!this.clickNode_in?.id) {
        return;
      }
      return {
        nodes: [
          {
            modelLabel: this.clickNode_in.modelLabel,
            nodes: [
              {
                id: this.clickNode_in.id,
                name: this.clickNode_in.name
              }
            ]
          }
        ],
        startTime: this.queryTime_in.startTime,
        endTime: this.queryTime_in.endTime,
        aggregationCycle: this.queryTime_in.cycle,
        energyTypes: [this.energyType_in],
        unittype: [this.indicatorType_in.unittype],
        energyefficiencysetId: [this.indicatorType_in.id],
        type: true,
        dimConfigId: this.dimConfigId_in,
        rootNode: {
          id: this.rootNode_in.id,
          modelLabel: this.rootNode_in.modelLabel
        }
      };
    },
    /**
     * 获取能效最值
     */
    async getMaxMinAvgEnergyEfficiency() {
      if (!this.clickNode_in?.id) {
        return;
      }
      const queryBody = this.getQueryBody();
      const res = await customApi.queryMaxMinAvgEnergyEfficiency(queryBody);
      if (res.code !== 0) {
        return;
      }
      this.historyData = res.data;
    },
    /**
     * 获取历史最优值
     */
    async getHistoryOptimumValue() {
      if (!this.clickNode_in?.id) {
        return;
      }
      const queryBody = this.getQueryBody();
      const res = await customApi.queryHistoryOptimumValue(queryBody);
      if (res.code !== 0) {
        return;
      }
      this.bestData = res.data;
    },
    // 能效对标
    async getEnergyEfficiencyBenchmark() {
      if (!this.clickNode_in?.id) {
        return;
      }
      const queryBody = this.getQueryBody();
      const res = await customApi.queryEnergyEfficiencyBenchmark(queryBody);
      if (res.code !== 0) {
        return;
      }
      const data = this._.cloneDeep(res.data);
      for (const key in data) {
        data[key] = this.formatNumberWithPrecision(data[key], 3);
      }
      const options = this.CetChart_benchmarking.options;
      options.yAxis.data = [
        `${$T("计划目标")}:`,
        `${$T("历史最优")}:`,
        `${$T("能效实绩")}:`
      ];
      options.series[0].data = [
        data.planObjectValue,
        data.historyOptimumValue,
        data.energyEfficiencyValue
      ];

      if (!res.data?.sets?.length) {
        return;
      }
      res.data.sets.forEach(item => {
        options.yAxis.data.unshift(item.name + ":");
        options.series[0].data.unshift(
          this.formatNumberWithPrecision(item.limitvalue, 3)
        );
      });
    },
    async getChartData() {
      if (!this.clickNode_in?.id) {
        return;
      }
      const params = {
        node: {
          id: this.clickNode_in.id,
          modelLabel: this.clickNode_in.modelLabel
        },
        rootNode: {
          id: this.rootNode_in.id,
          modelLabel: this.rootNode_in.modelLabel
        },
        startTime: this.queryTime_in.startTime,
        endTime: this.queryTime_in.endTime,
        aggregationCycle: this.queryTime_in.cycle,
        energyType: this.energyType_in,
        unitType: this.indicatorType_in.unittype,
        energyEfficiencySetId: this.indicatorType_in.id,
        type: true,
        dimConfigId: this.dimConfigId_in
      };
      this.levelOneDataList = [];
      this.levelTwoDataList = [];
      this.levelThreeDataList = [];
      const res = await customApi.queryEnergyEfficiencyDataWithLevel(params);
      if (res.code !== 0) {
        return;
      }
      // 自然周期和非自然周期返回的开始时间和结束时间
      const data = res.data[0].data;
      const startTime = data[0].logTime;
      const endTime = data[data.length - 1].logTime;
      const current = res.data[0];
      // 十年不显示上周期
      let series = [];
      //添加报警等级
      if (this.nonEmptyLevelData(current.levelOneData)) {
        series.push({
          name: $T("报警等级一"),
          type: "line",
          smooth: true,
          symbolSize: 1,
          itemStyle: {
            color: getEventGradeColor(1).bgColor
          },
          data: current.levelOneData
        });
        this.levelOneDataList = current.levelOneData.map(item => item.value);
      }
      if (this.nonEmptyLevelData(current.levelTwoData)) {
        series.push({
          name: $T("报警等级二"),
          type: "line",
          smooth: true,
          symbolSize: 1,
          itemStyle: {
            color: getEventGradeColor(2).bgColor
          },
          data: current.levelTwoData
        });
        this.levelTwoDataList = current.levelTwoData.map(item => item.value);
      }
      if (this.nonEmptyLevelData(current.levelThreeData)) {
        series.push({
          name: $T("报警等级三"),
          type: "line",
          smooth: true,
          symbolSize: 1,
          itemStyle: {
            color: getEventGradeColor(3).bgColor
          },
          data: current.levelThreeData
        });
        this.levelThreeDataList = current.levelThreeData.map(
          item => item.value
        );
      }
      //添加的等级数量
      const alarmNum = series.length;
      series.push({
        type: "bar",
        name: this.getLegendName(startTime, endTime),
        barWidth: this.queryTime_in.cycle === 14 ? "30px" : "",
        data: this.setBarStyle(data),
        markLine: MARKLINE,
        markPoint: this.getMarkPoint(data)
      });

      this.setChartOptions(series, alarmNum);
      this.getProductionData(startTime, endTime, params.aggregationCycle);

      const unit =
        this.queryTime_in.cycle === 7
          ? "H"
          : this.queryTime_in.cycle === 12
          ? "d"
          : this.queryTime_in.cycle === 14
          ? "M"
          : "";
      this.benchmarkingTime =
        this.formatterDate(startTime, this.currentStr) +
          "-" +
          this.formatterDate(
            this.$moment(endTime).add(1, unit),
            this.currentStr
          ) || "--";
    },
    // 获取产量数据，能效对标中需要
    async getProductionData(startTime, endTime, cycle) {
      let indicatorType = this.indicatorType_in;
      if (!indicatorType || indicatorType.unittype !== 1) {
        return null;
      }
      switch (cycle) {
        case 7:
          endTime = this.$moment(endTime).endOf("h").valueOf() + 1;
          break;
        case 12:
          endTime = this.$moment(endTime).endOf("d").valueOf() + 1;
          break;
        case 13:
          endTime = this.$moment(endTime).endOf("w").valueOf() + 1;
          break;
        case 14:
          endTime = this.$moment(endTime).endOf("M").valueOf() + 1;
          break;
        case 17:
          endTime = this.$moment(endTime).endOf("y").valueOf() + 1;
          break;
      }
      const res = await customApi.querySystemDataInput({
        aggregationCycle: cycle,
        startTime: startTime,
        endTime: endTime,
        measureTypes: [indicatorType.producttype],
        dataEntryType: 1,
        id: this.clickNode_in.id,
        modelLabel: this.clickNode_in.modelLabel
      });
      const details = this._.get(res, "data[0].details", []) || [];
      const unit = this._.get(res, "data[0].unit");
      this.CetChart_energyConsume.options.yAxis.push({
        type: "value",
        name: unit,
        nameLocation: "end",
        scale: true,
        nameTextStyle: {
          align: "right"
        }
      });
      this.CetChart_energyConsume.options.series.push({
        name: $T("产量"),
        type: "line",
        smooth: true,
        yAxisIndex: 1,
        data: details.map(i => {
          return {
            logTime: i.logTime,
            value: i.value,
            showDataSymbol: true,
            symbol: unit
          };
        })
      });
    },
    getLegendName(startTime, endTime) {
      const cycle = this.queryTime_in.cycle;
      const unit1 =
        {
          7: "H",
          12: "d",
          13: "w",
          14: "M"
        }[cycle] ?? "";
      const currentStr = this.currentStr;
      let str = "";
      if (cycle === 13) {
        str +=
          this.formatterDate(startTime, currentStr) +
          "-" +
          this.formatterDate(this.$moment(endTime), currentStr);
      } else {
        str +=
          this.formatterDate(startTime, currentStr) +
          "-" +
          this.formatterDate(this.$moment(endTime).add(1, unit1), currentStr);
      }
      return str;
    },
    nonEmptyLevelData(list) {
      let mark = 0;
      list.forEach(item => {
        mark += item.value || item.value === 0 ? 1 : 0;
      });
      return mark > 0;
    },
    setBarStyle(data) {
      const currentColor = omegaTheme.theme === "light" ? "#6ade9a" : "#0c82f8";
      const newData = data.map((item, index) => {
        return {
          ...item,
          itemStyle: {
            color:
              this.levelOneDataList[index] &&
              item.value > this.levelOneDataList[index]
                ? getEventGradeColor(1).bgColor
                : this.levelTwoDataList[index] &&
                  item.value > this.levelTwoDataList[index]
                ? getEventGradeColor(2).bgColor
                : this.levelThreeDataList[index] &&
                  item.value > this.levelThreeDataList[index]
                ? getEventGradeColor(3).bgColor
                : currentColor
          }
        };
      });
      return newData;
    },
    setChartOptions(series, alarmNum) {
      const vm = this;
      const { cycle, unit } = this.queryTime_in;
      const xAxisNameMap = {
        7: $T("小时"),
        12: $T("天数"),
        13: $T("周份"),
        14: $T("月份")
      };
      const xAxisName = xAxisNameMap[cycle] || $T("年份");

      const cycleMap = {
        7: $T("YYYY-MM-DD HH时"),
        12: "YYYY-MM-DD",
        13: $T("YYYY年第ww周"),
        14: "YYYY-MM"
      };
      const formatStr = cycleMap[cycle] || "YYYY";

      this.CetChart_energyConsume.options = {
        toolbox: {
          top: 0,
          right: 10,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        tooltip: {
          trigger: "axis",
          formatter(params) {
            const logTime = params[0].data.logTime;
            if (!logTime) return;

            let str = "";
            params.forEach(item => {
              let str1;
              if (
                item.componentSubType === "bar" &&
                item.seriesIndex === alarmNum
              ) {
                str1 = vm.formatterDate(logTime, formatStr);
              } else if (
                cycle !== 17 &&
                item.componentSubType === "bar" &&
                item.seriesIndex === alarmNum + 1
              ) {
                str1 = vm.formatterDate(
                  vm.$moment(logTime).subtract(1, unit),
                  formatStr
                );
              } else if (
                [12, 7].includes(cycle) &&
                item.componentSubType === "bar" &&
                item.seriesIndex === alarmNum + 2
              ) {
                str1 = vm.formatterDate(
                  vm.$moment(logTime).subtract(1, cycle === 7 ? "M" : "y"),
                  formatStr
                );
              } else {
                str1 = item.seriesName;
              }
              let value = "--";
              if (item.data.value || item.data.value === 0) {
                value = Number(item.data.value).toFixed(
                  item.seriesName === $T("产量") ? 2 : 3
                );
              }
              const symbol = item.data.showDataSymbol
                ? item.data.symbol
                : vm.indicatorType_in.symbol;
              str += `${item.marker} ${str1}: ${common.formatNum(value)}(${
                symbol ? `${symbol}` : "--"
              })<br />`;
            });
            return str;
          }
        },
        title: {
          text: "",
          textStyle: {
            fontSize: 14
          },
          left: "center"
        },
        legend: {
          top: "10px"
        },
        grid: {
          left: "15px",
          right: "45px",
          top: "55px",
          bottom: "10px",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            name: xAxisName,
            nameLocation: "end",
            data: [],
            axisPointer: {
              type: "shadow"
            },
            nameTextStyle: {
              padding: [10, 0, 0, 0],
              verticalAlign: "top"
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            name: this.indicatorType_in?.symbol,
            nameLocation: "end",
            nameTextStyle: {
              align: "left"
            }
          }
        ],
        series
      };
      // 处理x轴显示
      this.CetChart_energyConsume.options.series[0].data.forEach(item => {
        this.CetChart_energyConsume.options.xAxis[0].data.push(
          this.getAxixs(item.logTime, cycle)
        );
      });
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 7) {
        return this.$moment(date).format("HH");
      } else if (type === 12) {
        return this.$moment(date).format("DD");
      } else if (type === 13) {
        return this.$moment(date).format($T("YYYY年第ww周"));
      } else if (type === 14) {
        return this.$moment(date).format("YYYY/MM");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY");
      }
    },
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    checkChange() {
      const series = this.CetChart_energyConsume.options.series;
      for (let i = 0; i < series.length; i++) {
        if (series[i].type === "bar") {
          series[i].markPoint = this.maxAndMin
            ? this.getMarkPoint(series[i].data, 2)
            : null;
          return;
        }
      }
      this.CetChart_energyConsume.options.series = series;
    },
    getMarkPoint(data, type = 1) {
      //能效对标时是当前柱子的颜色
      let dataList = data;
      if (type === 1) {
        dataList = this.setBarStyle(data);
      }
      const max = this._.maxBy(dataList, "value");
      const min = this._.minBy(dataList, "value");
      const markPoint = _.cloneDeep(MARKPOINT);
      markPoint.data[0].itemStyle = {
        color: max?.itemStyle?.color,
        opacity: 1
      };
      markPoint.data[1].itemStyle = {
        color: min?.itemStyle?.color,
        opacity: 1
      };
      return markPoint;
    },
    formatNumberWithPrecision: common.formatNumberWithPrecision
  }
};
</script>
<style lang="scss" scoped>
.row-item {
  display: flex;
  flex: 1;
  div {
    display: flex;
    justify-content: center;
    align-items: center;
    &:first-of-type {
      flex: 1;
      font-size: 16px;
      font-weight: 900;
    }
    &:nth-of-type(2) {
      flex: 2;
      padding: 0 24px;
      display: flex;
      span {
        &:nth-of-type(1) {
          flex: 2;
        }
        &:nth-of-type(2) {
          font-weight: 900;
          flex: 3;
          text-align: right;
        }
      }
    }
    .maxVal {
      // @include font_color(T7);
    }
    .minVal {
      // @include font_color(T8);
    }
    .avgVal {
      // @include font_color(T9);
    }
  }
  &:nth-of-type(1) {
    div {
      // @include font_color(T7);
      // @include background(BG5);
    }
  }
  &:nth-of-type(2) {
    div {
      // @include font_color(T8);
      // @include background(BG6);
    }
  }
  &:nth-of-type(3) {
    div {
      // @include font_color(T9);
      // @include background(BG7);
    }
  }
}
.history {
  @include background_color(BG1);
  .content {
    background-image: url(./assets/historyBest_light.png);
    &.dark {
      background-image: url(./assets/historyBest_dark.png);
    }
    background-repeat: no-repeat;
    background-position: center;
    background-size: 80%;
    position: relative;
    span {
      position: absolute;
    }
  }
  .bestVal {
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
    color: #07dbd6;
    font-weight: 900;
    display: inline-block;
    width: 170px;
    text-align: center;
  }
  .time {
    width: 170px;
    left: 50%;
    bottom: 34%;
    transform: translateX(-50%);
    text-align: center;
  }
}
</style>
