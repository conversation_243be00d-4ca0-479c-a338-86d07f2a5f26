<template>
  <div class="customElSelectWrap">
    <ElInput
      v-bind="$attrs"
      v-on="$listeners"
      class="customElSelect"
      ref="customElSelect"
    >
      <span ref="prefix" class="prefix" slot="prefix">
        {{ prefix_in }}
      </span>
      <slot></slot>
    </ElInput>
  </div>
</template>

<script>
export default {
  name: "customElSelect",
  props: {
    prefix_in: String
  },
  components: {},
  computed: {},
  data() {
    return {};
  },
  watch: {
    prefix_in() {
      this.init();
    }
  },
  methods: {
    init() {
      let span = document.createElement("span");
      span.innerText = this.prefix_in;
      document.getElementsByTagName("body")[0].append(span);
      let offsetWidth = span.offsetWidth;
      span.remove();
      setTimeout(() => {
        $(this.$refs.customElSelect.$el)
          .find(".el-input__inner")
          .css({
            "padding-left": offsetWidth + 24 + "px"
          });
        if ($(this.$refs.customElSelect.$el).height()) {
          $(this.$refs.customElSelect.$el).css({
            "line-height": $(this.$refs.customElSelect.$el).height() + "px"
          });
        } else {
          $(this.$refs.customElSelect.$el).css({
            "line-height": "32px"
          });
        }

        $(this.$refs.customElSelect.$el)
          .find(".el-select__tags")
          .css({
            "padding-left": offsetWidth + 20 + "px",
            "box-sizing": "border-box",
            watch: `calc(100% - ${offsetWidth + 28}px)`,
            "max-width": `calc(300px - ${offsetWidth + 50}px)!important`
          });
      });
    }
  },
  mounted() {
    this.init();
  },
  activated() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.customElSelectWrap {
  display: inline-block;
  .customElSelect {
    width: 100%;
    :deep .el-input__prefix {
      left: 12px;
      @include font_color(ZS);
    }
    :deep .el-input__inner {
      border-top-left-radius: var(--Ra) !important;
      border-bottom-left-radius: var(--Ra) !important;
    }
  }
}
</style>
