/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "/energyAlarmConfig",
        component: () => import("@/projects/energyAlarmConfig/index.vue")
      },
      {
        path: "/cycleconfig",
        component: () => import("@/projects/cycleconfig/index.vue")
      },
      {
        path: "/timesharingConfig",
        component: () => import("@/projects/timesharingConfig/index.vue")
      },
      {
        path: "/publicSchemeConfig",
        component: () => import("@/projects/publicSchemeConfig/index.vue")
      },
      {
        path: "/lossSchemeConfig",
        component: () => import("@/projects/lossSchemeConfig/index.vue")
      },
      {
        path: "/dimensionConfiguration",
        component: () => import("@/projects/dimensionConfiguration/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
