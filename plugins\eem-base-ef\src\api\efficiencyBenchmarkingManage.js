import fetch from "eem-base/utils/fetch";

// 查询通用能效配置列表
export function getEfSet(data) {
  const expressions = data?.rootCondition?.filter?.expressions ?? [];
  const rootNode =
    expressions.find(item => item.prop === "rootNode")?.limit ?? "";
  const energyType =
    expressions.find(item => item.prop === "energyType")?.limit ?? "";
  const name = expressions.find(item => item.prop === "keyword")?.limit ?? "";
  const unitType =
    expressions.find(item => item.prop === "unitType")?.limit ?? "";

  const queryData = {
    energyType: energyType,
    page: data.rootCondition.page,
    rootNode: {
      id: rootNode.id,
      modelLabel: rootNode.modelLabel
    },
    unitType: unitType,
    name: name
  };

  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/efSet/group`,
    method: "POST",
    transformResponse: [
      function (response) {
        let res = response;
        if (typeof res === "string") {
          res = JSON.parse(response);
        }
        if (res.code == 0) {
          var resData = _.get(res, "data", []);
          resData.forEach(item => {
            item.children = [];
          });
          res.data = resData;
        }
        return res;
      }
    ],
    data: queryData
  });
}
// 根据能效通用配置查询对标管理
export function getBenchMarkSet(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/benchMarkSet/group`,
    method: "POST",
    data: data
  });
}

// 删除指标
export function deleteEnergyefficiencyset(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/efSet`,
    method: "DELETE",
    data: data
  });
}

// 删除对标
export function deleteBenchmarkset(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/benchMarkSet`,
    method: "DELETE",
    data: data
  });
}

// 根据能效通用配置查询已经关联的节点
export function getEfSetIdByRelevance(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/kpiSet/efSetId`,
    method: "GET",
    params: data
  });
}

// 通用能效配置关联节点，全量修改
export function addEfSetByNodeRelevanceAll(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/kpiSet/group`,
    method: "PUT",
    data: data
  });
}

// 获取维度列表信息
export function dimDetailList() {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/detailList`,
    method: "GET"
  });
}

// 获取产品类型
export function queryProducts(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/queryProducts`,
    method: "GET",
    data
  });
}

// 写指标
export function writeEfSetMutiCycle(data, params) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/efSetMutiCycle`,
    method: "PUT",
    data,
    params
  });
}

// 写指标
export function writeEnergyefficiencyset(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/efSet`,
    method: "PUT",
    data
  });
}

// 写对标
export function writeBenchmarkset(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef/benchMarkSet/group`,
    method: "PUT",
    data
  });
}
