﻿<template>
  <ElDrawer
    class="drawer"
    :title="$T('事件详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div class="flex flex-col flex-auto m-J1 content">
      <div class="text-H2 font-bold mb-J3">{{ $T("基本信息") }}</div>
      <div class="flex flex-row mb-J1">
        <div class="flex-auto mr-J1 flex flex-row">
          <div class="mr-J1">{{ $T("时间") }}:</div>
          <el-tooltip :content="formatterDate(detailInfo.eventtime)">
            <div class="flex-auto text-ellipsis text-right">
              {{ formatterDate(detailInfo.eventtime) }}
            </div>
          </el-tooltip>
        </div>
        <div class="flex-auto flex flex-row ml-J1">
          <div class="mr-J1">{{ $T("事件类型") }}:</div>
          <el-tooltip :content="detailInfo.eventtype$text">
            <div class="flex-auto text-ellipsis text-right">
              {{ detailInfo.eventtype$text }}
            </div>
          </el-tooltip>
        </div>
      </div>

      <div class="flex flex-row mb-J1">
        <div class="flex-auto mr-J1 flex flex-row">
          <div class="mr-J1">{{ $T("设备名称") }}:</div>
          <el-tooltip :content="detailInfo.name">
            <div class="flex-auto text-ellipsis text-right">
              {{ detailInfo.name }}
            </div>
          </el-tooltip>
        </div>
        <div class="flex-auto flex flex-row ml-J1">
          <div class="mr-J1">{{ $T("状态") }}:</div>
          <div class="flex-auto text-ellipsis text-right">
            <el-tag :type="isState(detailInfo)">
              {{ detailInfo.confirmeventstatus$text }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="flex flex-row mb-J1">
        <div class="flex-auto mr-J1 flex flex-row">
          <div class="mr-J1">{{ $T("事件等级") }}:</div>
          <div class="flex-auto text-ellipsis text-right">
            <el-tag
              :color="isLevel(detailInfo).bgColor"
              :style="`color: ${isLevel(detailInfo).color}`"
            >
              {{ detailInfo.level$text }}
            </el-tag>
          </div>
        </div>
        <div class="flex-auto flex flex-row ml-J1">
          <div class="mr-J1">{{ $T("收敛事件") }}:</div>
          <el-tooltip :content="detailInfo.convergence">
            <div class="flex-auto text-ellipsis text-right">
              {{ detailInfo.convergence }}
            </div>
          </el-tooltip>
        </div>
      </div>

      <div class="flex flex-row mb-J3">
        <div class="mr-J1">{{ $T("描述") }}:</div>
        <el-tooltip :content="detailInfo.description">
          <div class="flex-auto text-ellipsis text-right">
            {{ detailInfo.description }}
          </div>
        </el-tooltip>
      </div>

      <div class="text-H2 font-bold mb-J3">{{ $T("层级对象") }}</div>

      <div class="mb-J3">{{ detailInfo.levelName || "--" }}</div>

      <template v-if="detailInfo.confirmeventstatus !== 1">
        <div class="text-H2 font-bold mb-J3">{{ $T("确认信息") }}</div>

        <div class="flex flex-row mb-J1">
          <div class="flex-auto mr-J1 flex flex-row">
            <div class="mr-J1">{{ $T("确认人") }}:</div>
            <el-tooltip :content="detailInfo.operator">
              <div class="flex-auto text-ellipsis text-right">
                {{ detailInfo.operator }}
              </div>
            </el-tooltip>
          </div>
          <div class="flex-auto flex flex-row ml-J1">
            <div class="mr-J1">{{ $T("联系方式") }}:</div>
            <el-tooltip :content="detailInfo.phone">
              <div class="flex-auto text-ellipsis text-right">
                {{ detailInfo.phone }}
              </div>
            </el-tooltip>
          </div>
        </div>
        <div class="flex flex-row mb-J1">
          <div class="flex-auto flex flex-row">
            <div class="mr-J1">{{ $T("确认意见") }}:</div>
            <el-tooltip :content="detailInfo.remark">
              <div class="flex-auto text-ellipsis text-right">
                {{ detailInfo.remark }}
              </div>
            </el-tooltip>
          </div>
        </div>
      </template>
      <template v-if="showConfirm">
        <div class="text-H2 font-bold mb-J3">{{ $T("处理详情") }}</div>
        <div class="mb-J1">
          {{ $T("确认意见") }}
        </div>
        <ElInput
          class="confirm-input mb-J1"
          v-model="ElInput_1.value"
          v-bind="ElInput_1"
          v-on="ElInput_1.event"
        ></ElInput>
        <div class="mb-J1 flex flex-row">
          <div class="mr-J1">{{ $T("联系方式") }}:</div>
          <el-tooltip :content="userInfo.mobilePhone">
            <div class="flex-auto text-ellipsis">
              {{ userInfo.mobilePhone }}
            </div>
          </el-tooltip>
        </div>
        <div class="mb-J1 flex flex-row">
          <div class="mr-J1">{{ $T("操作人") }}:</div>
          <el-tooltip :content="userInfo.nicName">
            <div class="flex-auto text-ellipsis">
              {{ userInfo.nicName }}
            </div>
          </el-tooltip>
        </div>
      </template>
    </div>
    <div class="flex flex-row justify-end footer" v-if="showConfirm">
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </div>
  </ElDrawer>
</template>
<script>
import moment from "moment";
import customApi from "@/api/custom";
import { getEventTypeColor, getEventGradeColor } from "@/utils/eventColor.js";
export default {
  name: "EnergyConsumptionEventDetail",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    showConfirm() {
      return (
        this.$checkPermission("systemevent_confirm") &&
        this.inputData_in?.confirmeventstatus === 1
      );
    }
  },

  data() {
    return {
      detailInfo: {},
      openDrawer: false,
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 8,
        type: "textarea",
        maxlength: 255,
        showWordLimit: true,
        required: true,
        placeholder: $T("请输入内容（必需）"),
        event: {
          input: this.ElInput_1_input_out
        }
      }
    };
  },
  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      this.init();
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },

  methods: {
    init() {
      this.detailInfo = this._.cloneDeep(this.inputData_in);
      if (this.inputData_in.confirmeventstatus === 1) {
        this.ElInput_1.value = "";
      } else {
        this.getUserinfo();
      }
    },
    async getUserinfo() {
      const userId = this.inputData_in.operator_id;

      if (!userId) {
        this.detailInfo.operatorname = "";
        return;
      }
      const response = await customApi.queryUserInfoById(userId);
      if (response.code !== 0) {
        return;
      }

      const userinfo = response.data;
      this.$set(this.detailInfo, "operatorname", userinfo.nicName || "--");
      this.$set(this.detailInfo, "phone", userinfo.mobilePhone || "--");
    },
    CetButton_confirm_statusTrigger_out() {
      var eventList = [this.inputData_in];
      this.eventConfirm_out(eventList);
    },
    ElInput_1_input_out(val) {
      this.CetButton_confirm.disable_in = !val.length;
    },
    getConfirmEventData_out(events) {
      if (!events?.length) {
        return;
      }
      events.forEach(item => {
        item.modelLabel = item.modelLabel ? item.modelLabel : "systemevent";
        item.confirmeventstatus = 3;
        item.operator = this.userInfo.nicName;
        item.operator_id = this.userInfo.id;
        item.remark = this.ElInput_1.value;
        item.updatetime = new Date().getTime();
      });
      return events;
    },

    async eventConfirm_out(events) {
      const list = this.getConfirmEventData_out(events);
      if (!list || list.length === 0) {
        return;
      }

      const res = await customApi.confirmEvents(list);
      if (res.code !== 0) {
        return;
      }
      this.$message.success($T("操作成功"));
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    isLevel(val) {
      // 预警直接展示“预警”
      if (val.eventtype == 701) {
        const { bgColor, color } = getEventTypeColor(4);
        return { bgColor, color };
      } else {
        const { bgColor, color } = getEventGradeColor(val.level);
        return { bgColor, color };
      }
    },
    isState(val) {
      if (val.confirmeventstatus === 3) {
        return "success";
      } else {
        return "danger";
      }
    },
    formatterDate(value) {
      if (value) {
        return moment(value).format("YYYY-MM-DD HH:mm:ss.SSS");
      } else {
        return "--";
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.drawer {
  :deep() {
    .el-drawer__body {
      display: flex;
      flex-direction: column;
      padding: 0;
    }
  }
  .footer {
    border-radius: 0;
  }
  .content {
    margin-bottom: var(--J1);
    .confirm-input {
      border: solid;
      border-width: 1px;
      border-radius: var(--Ra);
      box-sizing: border-box;
      @include border_color(B1);
      :deep(.el-textarea__inner) {
        border-width: 0;
      }
    }
  }
}
</style>
