﻿<template>
  <div class="h-full">
    <el-container class="fullheight flex-col flex">
      <el-tooltip
        effect="light"
        :content="clickNode?.name"
        placement="top-start"
      >
        <div
          class="text-H2 font-bold text-ellipsis mb-J3"
          style="display: inline-block; width: 250px"
        >
          {{ clickNode?.name || "--" }}
        </div>
      </el-tooltip>
      <div class="flex-col flex flex-auto">
        <div class="mb-J3 flex-row flex items-center justify-between">
          <div class="flex-row flex items-center">
            <el-input
              class="search-input mr-J1"
              :placeholder="$T('输入关键字搜索')"
              suffix-icon="el-icon-search"
              v-model="searchParams.description"
              size="small"
            />
            <customElSelect
              v-model="searchParams.alarmAggregationCycle"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              class="mr-J1"
              :prefix_in="$T('报警类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
            <TimeRange
              class="mr-J1"
              @change="handleChange_out"
              :val="defaultTime"
              :disabledDate="pickerOptions.disabledDate"
              :onPick="pickerOptions.onPick"
            ></TimeRange>
          </div>
          <div class="flex-row flex items-center">
            <div class="border-switch h-[30px] flex items-center">
              <span class="mr-J1 text-Aa">{{ ElCheckbox_1.text }}</span>
              <el-switch
                class="switch_style"
                v-model="searchParams.convergence"
                :active-color="themeLight ? '#29b061' : '#0D86FF'"
                :inactive-color="themeLight ? '#d7d7d7' : '#414B6E'"
              ></el-switch>
            </div>
            <CetButton
              class="ml-J1"
              v-bind="CetButton_6"
              v-on="CetButton_6.event"
            ></CetButton>
            <CetButton
              class="ml-J1"
              v-bind="CetButton_1"
              v-on="CetButton_1.event"
            ></CetButton>
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link ml-J1">
                {{ $T("更多") }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="export">
                  {{ $T("导出") }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="confirm"
                  :disabled="!!tableSelectData?.length"
                  :class="tableSelectData?.length ? 'disabled' : ''"
                  v-permission="'systemevent_confirm'"
                >
                  {{ $T("批量确认") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="flex-auto">
          <EnergyConsumptionEventTable
            v-bind="energyConsumptionEventTable"
            v-on="energyConsumptionEventTable.event"
            :searchParams_in="searchParams"
            :clickNode_in="clickNode"
          ></EnergyConsumptionEventTable>
        </div>
      </div>
    </el-container>
    <EnergyConsumptionEventAdvancedQuery
      v-bind="energyConsumptionEventAdvancedQuery"
      v-on="energyConsumptionEventAdvancedQuery.event"
      :treeData_in="treeData_in"
      :clickNode_in="clickNode"
    />
    <EventConfirm
      :visibleTrigger_in="energyConsumptionEventConfirm.visibleTrigger_in"
      :closeTrigger_in="energyConsumptionEventConfirm.closeTrigger_in"
      :inputData_in="energyConsumptionEventConfirm.inputData_in"
      @confirmedTrigger_out="energyConsumptionEventConfirm_confirmedTrigger_out"
    />
  </div>
</template>
<script>
import EnergyConsumptionEventTable from "./EnergyConsumptionEventTable.vue";
import EnergyConsumptionEventAdvancedQuery from "./EnergyConsumptionEventAdvancedQuery.vue";
import EventConfirm from "./EventConfirm.vue";
import TimeRange from "@/compomemts/TimeRange";
import omegaTheme from "@omega/theme";
import omegaI18n from "@omega/i18n";
export default {
  name: "EnergyConsumptionEvent",
  components: {
    EnergyConsumptionEventTable,
    EnergyConsumptionEventAdvancedQuery,
    EventConfirm,
    TimeRange
  },

  computed: {
    themeLight() {
      return omegaTheme.theme === "light";
    }
  },

  props: {
    clickNode: {
      type: Object
    },
    treeData_in: {
      type: Array
    },
    dimTreeConfigId: Number
  },

  data(vm) {
    const en = omegaI18n.locale === "en";
    const defaultTime = [
      this.$moment().startOf("date").valueOf(),
      this.$moment().endOf("date").valueOf() + 1
    ];
    return {
      tableSelectData: [],
      defaultTime: defaultTime,
      searchParams: {
        description: "",
        alarmAggregationCycle: 12,
        starttime: defaultTime[0],
        endtime: defaultTime[1],
        convergence: false,
        levels: [1, 2, 3, 4],
        status: null,
        types: [708, 709]
      },
      ENERGY_CONSUMPTION: [701, 702], // 能耗报警
      ENERGY_EFFICIECY: [708, 709], // 能效报警
      ENERGY_ALL: [701, 702, 708, 709],
      ElCheckbox_1: {
        value: false,
        text: $T("收敛事件"),
        disabled: false,
        event: {}
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("高级查询"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_6: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      energyConsumptionEventTable: {
        refreshTrigger_in: Date.now(),
        exportTrigger_in: Date.now(),
        event: {
          selectionChange_out:
            this.energyConsumptionEventTable_selectionChange_out
        }
      },
      energyConsumptionEventAdvancedQuery: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        searchParams_in: null,
        event: {
          saveData_out: this.energyConsumptionEventAdvancedQuery_saveData_out
        }
      },
      energyConsumptionEventConfirm: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 报警类型下拉框
      ElSelect_1: {
        value: 12,
        style: {
          width: en ? "170px" : "150px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          {
            id: 12,
            text: $T("日")
          },
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 时间范围限制
      pickerOptions: {
        // 禁止选择器选中同一天
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            vm.disabledTime = minDate;
          }
          if (maxDate) {
            vm.disabledTime = null;
          }
        },
        disabledDate(time) {
          let disabledTime = vm.disabledTime;
          if (disabledTime && vm.searchParams.alarmAggregationCycle === 12) {
            return time.getTime() === disabledTime.getTime();
          }
        }
      }
    };
  },

  methods: {
    CetButton_1_statusTrigger_out() {
      this.energyConsumptionEventAdvancedQuery.searchParams_in =
        this.searchParams;
      this.energyConsumptionEventAdvancedQuery.visibleTrigger_in = Date.now();
    },
    CetButton_2_statusTrigger_out() {
      this.energyConsumptionEventTable.exportTrigger_in = Date.now();
    },
    eventConfirm() {
      this.energyConsumptionEventConfirm.inputData_in = {
        eventlist: this.tableSelectData
      };
      this.energyConsumptionEventConfirm.visibleTrigger_in = Date.now();
    },
    CetButton_6_statusTrigger_out() {
      this.energyConsumptionEventTable.refreshTrigger_in = Date.now();
    },

    energyConsumptionEventConfirm_confirmedTrigger_out() {
      this.energyConsumptionEventTable.refreshTrigger_in = Date.now();
    },
    energyConsumptionEventTable_selectionChange_out(val) {
      this.tableSelectData = this._.cloneDeep(val);
    },
    handleChange_out(val) {
      this.defaultTime = val;
      this.searchParams.starttime = val[0];
      this.searchParams.endtime = val[1];

      this.$emit("time_out", val);
      // 如果是多维度树，则更新节点树，走节点树点击输出事件，阻止表格的刷新
      if (this.dimTreeConfigId > 0) {
        this.$emit("refreshTree");
      }
    },
    // 更多选择
    handleCommand(val) {
      if (val === "export") {
        this.CetButton_2_statusTrigger_out();
      } else if (val === "confirm") {
        this.eventConfirm();
      }
    },
    energyConsumptionEventAdvancedQuery_saveData_out(node, params) {
      // 阻止表格的刷新,走节点点击逻辑
      this.searchParams = this._.cloneDeep(params);
      this.$emit("setClickNode", node);
    }
  }
};
</script>
<style lang="scss" scoped>
.search-input {
  width: 220px;
}
.disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
.border-switch {
  border: 1px solid;
  border-radius: var(--Ra);
  @include border_color(B1);
  padding: 0px var(--J1);
  .switch_style {
    // 按照ui要求进行修改暗色情况下的按钮颜色
    :deep(.el-switch__core:after) {
      background-color: #fff;
    }
  }
}
</style>
