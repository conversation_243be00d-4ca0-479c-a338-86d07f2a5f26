<template>
  <div class="h-full flex-col flex">
    <el-table
      ref="eventTable"
      class="flex-auto"
      :data="eventTableData"
      tooltip-effect="light"
      border
      highlight-current-row
      height="true"
      row-key="tree_id"
      :default-expand-all="false"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @current-change="handlerCurrentChange"
      @selection-change="handleSelectionChange"
      @expand-change="handleExpandChange"
    >
      <el-table-column
        type="selection"
        width="50"
        align="left"
        reserve-selection
        :selectable="canConfirm"
      ></el-table-column>
      <el-table-column
        :label="$T('序号')"
        type="index"
        :width="language ? 50 : 65"
        align="left"
      ></el-table-column>

      <template v-for="item in renderColumns">
        <template v-if="item.type === 'operate'">
          <ElTableColumn :key="item.label" v-bind="item">
            <template slot-scope="scope">
              <i
                class="analysis cursor-pointer"
                @click="handleIcon(scope.$index, scope.row)"
              ></i>
            </template>
          </ElTableColumn>
        </template>
        <template v-else-if="item.type === 'level'">
          <ElTableColumn :key="item.label" v-bind="item">
            <template slot-scope="scope">
              <el-tag
                :color="isLevel(scope.row).bgColor"
                :style="`color: ${isLevel(scope.row).color}`"
              >
                {{ formatTable(scope.row[item.prop]) }}
              </el-tag>
            </template>
          </ElTableColumn>
        </template>
        <template v-else-if="item.type === 'confirmeventstatus'">
          <ElTableColumn :key="item.label" v-bind="item">
            <template slot-scope="scope">
              <el-tag
                :type="isState(scope.row)"
                effect="plain"
                size="small"
                disable-transitions
              >
                {{ formatTable(scope.row[item.prop]) }}
              </el-tag>
            </template>
          </ElTableColumn>
        </template>
        <ElTableColumn v-else :key="item.label" v-bind="item"></ElTableColumn>
      </template>
      <el-table-column
        :label="$T('操作')"
        :width="language ? 140 : 100"
        headerAlign="left"
        align="left"
        fixed="right"
      >
        <template slot-scope="scope">
          <span
            class="text-ZS cursor-pointer"
            @click.stop="handleDetail(scope.row)"
          >
            {{ $T("事件详情") }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="text-right mt-J3"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      layout="prev, pager, next, jumper"
      :total="totalCount"
      background
    ></el-pagination>
    <div class="flex-auto mt-J1 flex-col flex" v-if="showChart">
      <div style="height: 24px; position: relative">
        <span class="icon-bottom-hidden" @click="hiddenChart()"></span>
      </div>
      <div class="mt-J3 mb-J3">
        <span class="analysis"></span>
        {{ $T("告警分析") }}
      </div>
      <div class="flex-auto">
        <CetChart
          :inputData_in="CetChart_1.inputData_in"
          v-bind="CetChart_1.config"
        />
      </div>
    </div>
    <EnergyConsumptionEventDetail
      v-bind="energyConsumptionEventDetail"
      v-on="energyConsumptionEventDetail.event"
    />
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import * as echarts from "echarts";
import { getEventTypeColor, getEventGradeColor } from "@/utils/eventColor.js";
import omegaI18n from "@omega/i18n";
import customApi from "@/api/custom.js";
import EnergyConsumptionEventDetail from "./EnergyConsumptionEventDetail.vue";
export default {
  name: "EnergyConsumptionEventTable",
  components: {
    EnergyConsumptionEventDetail
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    language() {
      return omegaI18n.locale === "en";
    },
    isConvergence() {
      return !!this.searchParams_in.convergence;
    }
  },
  props: {
    exportTrigger_in: Number,
    refreshTrigger_in: Number,
    clickNode_in: Object,
    searchParams_in: Object
  },

  data() {
    return {
      page: {
        currentPage: 1,
        pageSize: 100
      },
      totalCount: 0,
      renderColumns: [
        {
          prop: "levelName",
          label: $T("节点层级"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 120,
          formatter: common.formatTextCol()
        },
        {
          prop: "efSetName",
          label: $T("指标类型"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 140,
          formatter: common.formatTextCol()
        },
        {
          prop: "cycle$text",
          label: $T("报警类型"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 120,
          formatter: common.formatTextCol()
        },
        {
          prop: "description",
          label: $T("描述"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 200,
          formatter: common.formatTextCol()
        },
        {
          prop: "level$text",
          label: $T("事件等级"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 120,
          type: "level"
        },
        {
          label: $T("分析"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 100,
          type: "operate"
        },
        {
          prop: "eventtime",
          label: $T("发生时间"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 200,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss.SSS")
        },
        {
          prop: "endTime",
          label: $T("结束时间"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 200,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss.SSS")
        },
        {
          prop: "durationTime",
          label: $T("持续时长"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 200,
          formatter: common.formatTextCol()
        },
        {
          prop: "confirmeventstatus$text",
          label: $T("状态"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 200,
          type: "confirmeventstatus"
        }
      ],
      currentRow: null,
      eventTableData: [],
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              top: "12%",
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true
            },
            legend: {
              data: [$T("预测值"), $T("实际值"), $T("阈值")]
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: [
              {
                name: $T("预测值"),
                type: "line",
                smooth: true,
                barWidth: "60%",
                encode: { x: "product", y: "yAxis1" }
              },
              {
                name: $T("实际值"),
                type: "line",
                smooth: true,
                encode: { x: "product", y: "yAxis2" }
              },
              {
                name: $T("阈值"),
                type: "line",
                smooth: true,
                encode: { x: "product", y: "yAxis3" }
              }
            ]
          }
        }
      },
      showChart: false,
      energyConsumptionEventDetail: {
        visibleTrigger_in: 0,
        closeTrigger_in: 0,
        inputData_in: null,
        event: {
          finishTrigger_out: this.energyConsumptionEventDetail_finishTrigger_out
        }
      }
    };
  },
  watch: {
    exportTrigger_in() {
      this.exportTable_out();
    },
    clickNode_in: {
      deep: true,
      handler: function () {
        this.page.currentPage = 1;
        this.queryTableData();
      }
    },
    searchParams_in: {
      deep: true,
      handler: function () {
        this.page.currentPage = 1;
        this.queryTableData();
      }
    },
    refreshTrigger_in() {
      this.queryTableData();
    }
  },

  methods: {
    handleSelectionChange(val) {
      this.$emit("selectionChange_out", val);
    },
    handleExpandChange(row, expanded) {
      this.$set(row, "expanded", expanded);
      row.children.forEach(item => {
        this.$set(item, "childrenFlag", expanded);
      });
    },
    canConfirm(row) {
      let confirmeventstatus = row.confirmeventstatus;
      return !(confirmeventstatus && confirmeventstatus == 3);
    },

    //分页大小变化
    handleSizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.queryTableData();
    },
    //分页当前页变化
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.queryTableData();
    },
    /**
     * 聚合参数
     */
    getParams() {
      const params = {
        ...this.searchParams_in,
        id: this.clickNode_in.id,
        modelLabel: this.clickNode_in.modelLabel,
        index: (this.page.currentPage - 1) * this.page.pageSize,
        limit: this.page.pageSize
      };

      function fn(children) {
        var arr = [];
        if (children && children.length > 0) {
          children.forEach(item => {
            var obj = {};
            if (item.children && item.children.length > 0) {
              obj = {
                nodeId: item.id || item.nodeId,
                name: item.name,
                modelLabel: item.modelLabel,
                children: fn(item.children)
              };
            } else {
              obj = {
                nodeId: item.id || item.nodeId,
                name: item.name,
                modelLabel: item.modelLabel,
                children: []
              };
            }
            arr.push(obj);
          });
        }
        return arr;
      }
      params.children = fn(this.clickNode_in.children);
      return params;
    },
    /**
     * 获取表格数据
     */
    queryTableData: _.debounce(async function () {
      const queryData = this.getParams();
      const response = await customApi.queryEvents(queryData);
      if (response.code !== 0) {
        return;
      }
      // 隐藏趋势曲线
      this.hiddenChart();
      const data = response.data || [];
      this.eventTableData = this.analysisField(data);
      this.totalCount = response.total;
      // 加载新数据时清除掉原来已选择的事件。
      await this.$nextTick();
      this.$refs.eventTable.clearSelection();
    }, 300),
    /**
     * 解析字段
     */
    analysisField(data) {
      if (!data?.length) {
        return data;
      }

      data.forEach(item => {
        item.confirmeventstatus$text =
          item.confirmeventstatus === 1 ? $T("待处理") : $T("已处理");
        item.cycle$text = this.filCycle(item.cycle);
        item.eventtype$text = this.filEventType(item.eventtype);
        item.level$text = this.filLevel(item);
        item.tree_id = item.modelLabel + "_" + item.id;
        let starttime = item.eventtime,
          endtime = item.endTime,
          duration = endtime - starttime,
          hour = parseInt(duration / 1000 / 60 / 60),
          minu = parseInt((duration / 1000 / 60) % 60);
        if (isNaN(hour) || hour === null || hour === undefined) {
          hour = "--";
        }
        if (isNaN(hour) || minu === null || minu === undefined) {
          minu = "--";
        }
        item.durationTime = `${hour}${$T("小时")}${minu}${$T("分")}`;
        if (!starttime || !endtime) {
          item.durationTime = "--";
        }
        if (!this.isConvergence) {
          item.children = [];
          item.convergence = 0;
        } else {
          item.children = item.children || [];
          item.convergence = item.children.length;
        }
        if (item.children && item.children.length > 0) {
          this.analysisField(item.children);
        }
      });

      return data;
    },
    handleDetail(row) {
      this.energyConsumptionEventDetail.inputData_in = this._.cloneDeep(row);
      this.energyConsumptionEventDetail.visibleTrigger_in = Date.now();
    },
    energyConsumptionEventDetail_finishTrigger_out() {
      this.queryTableData();
    },
    exportTable_out() {
      const queryData = this.getParams();
      common.downExcel("/eem-service/v1/alarmEvent/energydata", queryData);
    },
    isLevel(row) {
      // 预警直接展示“预警”
      if (row.eventtype == 701) {
        const { bgColor, color } = getEventTypeColor(4);
        return { bgColor, color };
      } else {
        const { bgColor, color } = getEventGradeColor(row.level);
        return { bgColor, color };
      }
    },
    isState(row) {
      if (row.confirmeventstatus === 3) {
        return "success";
      } else {
        return "danger";
      }
    },
    formatTable(cellValue) {
      if (cellValue) {
        return cellValue;
      }
      if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    //当前行变化
    handlerCurrentChange(currentRow) {
      this.currentRow = currentRow;
      if (!this.showChart) {
        return;
      }
      this.getChartData(currentRow);
    },
    handleIcon(index, row) {
      if (this.showChart) {
        return;
      }
      this.getChartData(row);
      this.showChart = true;
    },
    //分析显示图表
    hiddenChart() {
      this.showChart = false;
    },
    // 获取事件能效趋势曲线
    async getChartData(row) {
      if (!row) {
        return;
      }
      const params = {
        endtime: this.$moment().endOf("M").valueOf() + 1,
        energytype: row.energytype,
        id: row.object_id,
        level: row.level,
        modelLabel: row.object_label,
        starttime: this.$moment().startOf("M").valueOf(),
        alarmtype: row.eventtype,
        eventId: row.id,
        projectId: this.projectId
      };
      params.endtime = row.updatetime || null;
      params.starttime = row.eventtime || null;
      const response = await customApi.eventAnalysis(params);
      if (response.code !== 0) {
        return;
      }

      const data = response.data;
      this.filChartData1(data, row.eventtype);
    },
    async filChartData1(data, eventtype) {
      if (!data) {
        return;
      }

      const cycle = this.currentRow.cycle || 14;
      const symbol = data.symbol || "MWh";

      let interceptText = cycle === 7;

      this.CetChart_1.config.options = {
        tooltip: {
          trigger: "axis"
        },
        grid: {
          top: "12%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {},
        dataset: {
          source: []
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          splitLine: { show: false },
          axisLabel: {
            formatter: (value, index) => {
              if (interceptText && index) {
                return value.split(" ")[1];
              }
              return value;
            }
          }
        },
        yAxis: { name: $T("（{0}）", symbol) },
        series: []
      };

      const threshold = data.threshold || [];

      let actual = data.actual || [], //实际值
        predictAction = data.predictAction || [], //预测动作
        predictReturn = data.predictReturn || [], //预测返回
        limit = data.limit, //定额
        level1 = threshold.find(item => item.level === 1)?.value ?? null,
        level2 = threshold.find(item => item.level === 2)?.value ?? null,
        level3 = threshold.find(item => item.level === 3)?.value ?? null;

      const source = [];
      for (let i = 0, len = actual.length; i < len; i++) {
        let value1 =
            actual[i] || actual[i] === 0
              ? common.formatNumberWithPrecision(actual[i].value, 2)
              : null,
          value2 = null,
          value3 = null,
          value4 = common.formatNumberWithPrecision(limit, 2),
          level1value = common.formatNumberWithPrecision(level1, 2),
          level2value = common.formatNumberWithPrecision(level2, 2),
          level3value = common.formatNumberWithPrecision(level3, 2),
          product = this.getAxixs(actual[i].time, cycle, eventtype, actual);

        let predictActionObj = predictAction.find(
          item => this.getAxixs(item.time, cycle, eventtype, actual) == product
        );
        if (predictActionObj) {
          value2 = predictActionObj
            ? common.formatNumberWithPrecision(predictActionObj.value, 2)
            : null;
        }
        let predictReturnObj = predictReturn.find(
          item => this.getAxixs(item.time, cycle, eventtype, actual) == product
        );
        if (predictReturnObj) {
          value3 = predictReturnObj
            ? common.formatNumberWithPrecision(predictReturnObj.value, 2)
            : null;
        }

        let sour = {
          time: actual[i].time,
          product: product,
          yAxis1: value1 || value1 === 0 ? value1 : "--",
          yAxis2: value2 || value2 === 0 ? value2 : "--",
          yAxis3: value3 || value3 === 0 ? value3 : "--",
          yAxis4: value4 || value4 === 0 ? value4 : "--",
          level1value: level1value || level1value === 0 ? level1value : "--",
          level2value: level2value || level2value === 0 ? level2value : "--",
          level3value: level3value || level3value === 0 ? level3value : "--"
        };

        source.push(sour);
      }
      // 0 实际 1预测 2预测 3定额
      let seriesArr = [
          {
            name: $T("实际能效"),
            type: "line",
            showSymbol: true,
            symbolSize: 10,
            stack: $T("总量"),
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0a6db9"
                },
                {
                  offset: 1,
                  color: "#091545"
                }
              ])
            },
            itemStyle: {
              color: "#3693D9"
            },
            barWidth: "60%",
            encode: { x: "product", y: "yAxis1" }
          },
          {
            name: $T("预测动作"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "yAxis2" },
            itemStyle: {
              color: "#1E96F3"
            },
            lineStyle: {
              type: "dashed"
            }
          },
          {
            name: $T("预测返回"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "yAxis3" },
            itemStyle: {
              color: "#8B658B"
            },
            lineStyle: {
              type: "dashed"
            }
          },
          {
            name: $T("能效定额"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "yAxis4" },
            itemStyle: {
              color: "#5BE27B"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#5BE27B",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          },
          {
            name: $T("一级告警"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "level1value" },
            itemStyle: {
              color: "#ff0000"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#ff0000",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          },
          {
            name: $T("二级告警"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "level2value" },
            itemStyle: {
              color: "#ff9900"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#ff9900",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          },
          {
            name: $T("三级告警"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "level3value" },
            itemStyle: {
              color: "#0152d9"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#0152d9",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          }
        ],
        series = [];

      seriesArr[0].areaStyle = null;
      series.push(
        seriesArr[0],
        seriesArr[4],
        seriesArr[5],
        seriesArr[6],
        seriesArr[3]
      );
      const dataset = {
        source: source
      };
      await this.$nextTick();
      this.CetChart_1.config.options.dataset = dataset;
      this.CetChart_1.config.options.series = series;
    },
    getAxixs(date_in, type, eventtype, times) {
      var date = new Date(date_in),
        y = date.getFullYear(),
        M = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 7) {
        // 自然周期中以第二个0点转为24点展示，非自然周期中以第一个0点转为24点展示
        // 时间坐标轴第一个刻度显示年月日时
        const index = times.findIndex(i => i.time === date_in);
        let str = M + "-" + d + " ";
        if (h == 0 && m == 0) {
          if (
            this.$moment(times[0]).hour() === 0 &&
            this.$moment(times[0]).minute() === 0
          ) {
            if (index > 0) {
              return str + "24:" + m;
            }
          } else {
            return str + "24:" + m;
          }
        }
        return str + h + ":" + m;
      } else if (type === 12) {
        return M + "-" + d;
      } else if (type === 14) {
        return y + "-" + M;
      } else if (type === 17) {
        return y;
      } else {
        return y + "-" + M + "-" + d;
      }
    },
    filEventType(type = 0) {
      let eventtypeList = this.$store.state.enumerations.eventtype || [];
      eventtypeList = eventtypeList.filter(item => {
        return item.id > 700;
      });
      let fil = {};
      eventtypeList.forEach(item => {
        if (item.id == type) {
          fil = item;
        }
      });
      return fil.text || "--";
    },
    filCycle(cycle) {
      const cycleMap = {
        7: $T("小时告警"),
        12: $T("日告警"),
        13: $T("周告警"),
        14: $T("月告警"),
        17: $T("年告警")
      };
      return cycleMap[cycle] || "--";
    },
    filLevel(item) {
      // 预警直接展示“预警”
      if (item.eventtype == 701) {
        return $T("预警");
      }

      const labelMap = {
        1: $T("一级告警"),
        2: $T("二级告警"),
        3: $T("三级告警"),
        4: $T("其他")
      };
      return labelMap[item.level] || "--";
    }
  }
};
</script>
<style lang="scss" scoped>
.icon-bottom-hidden {
  display: inline-block;
  width: 100px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/u7706.png") no-repeat center center;
  position: absolute;
  left: calc(50% - 50px);
}
.analysis {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("./assets/u10740.png") no-repeat center #0350da;
  border-radius: 50%;
  position: relative;
  top: 4px;
}
</style>
