<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="flex flex-col h-full">
        <customElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          class="mb-J1"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-model="ElSelect_cycle.value"
          v-bind="ElSelect_cycle"
          v-on="ElSelect_cycle.event"
          class="mb-J1"
          :prefix_in="$T('统计周期')"
        >
          <ElOption
            v-for="item in ElOption_cycle.options_in"
            :key="item[ElOption_cycle.key]"
            :label="item[ElOption_cycle.label]"
            :value="item[ElOption_cycle.value]"
            :disabled="item[ElOption_cycle.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-model="ElSelect_indicatorType.value"
          v-bind="ElSelect_indicatorType"
          v-on="ElSelect_indicatorType.event"
          class="mb-J1"
          :prefix_in="$T('指标类型')"
        >
          <ElOption
            v-for="item in ElOption_indicatorType.options_in"
            :key="item[ElOption_indicatorType.key]"
            :label="item[ElOption_indicatorType.label]"
            :value="item[ElOption_indicatorType.value]"
            :disabled="item[ElOption_indicatorType.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-model="ElSelect_treeType.value"
          v-bind="ElSelect_treeType"
          v-on="ElSelect_treeType.event"
          class="mb-J1"
          :prefix_in="$T('节点树类型')"
          v-if="multidimensional"
        >
          <ElOption
            v-for="item in ElOption_treeType.options_in"
            :key="item[ElOption_treeType.key]"
            :label="item[ElOption_treeType.label]"
            :value="item[ElOption_treeType.value]"
            :disabled="item[ElOption_treeType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto cetTree"
          ref="cetTree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
          :key="treeKey"
        >
          <span
            class="custom-tree-node el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
            :class="{ 'focus-node': foucsNode?.id === node.id }"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
            <div class="icon" v-if="node?.data?.changeStatus">
              <el-tooltip effect="light" :content="effTimeFormat(node.data)">
                <omega-icon symbolId="collect-lin" />
              </el-tooltip>
            </div>
          </span>
        </CetTree>
      </div>
    </template>
    <template #container>
      <div class="h-full flex flex-col">
        <div class="flex-row flex mb-J3 items-center">
          <div class="flex-auto text-ellipsis">
            <el-tooltip
              effect="light"
              :content="clickNode?.name"
              placement="top-start"
            >
              <span class="text-H2 font-bold">
                {{ clickNode?.name || "--" }}
              </span>
            </el-tooltip>
          </div>
          <div class="flex flex-row items-center">
            <el-radio-group
              v-model="activeTab"
              class="mr-J3"
              @change="handlerChangeTab"
            >
              <el-radio-button
                v-for="(item, index) in tabList"
                :label="item.label"
                :key="index"
              >
                {{ item.text }}
              </el-radio-button>
            </el-radio-group>
            <time-tool
              :showOption="false"
              :typeID="ElSelect_cycle.value"
              :val.sync="startTime"
              @change="changeQueryTime"
              :timeType_in="timeType"
            ></time-tool>
            <!-- export按钮组件 -->
            <CetButton
              class="ml-J3"
              v-bind="CetButton_export"
              v-on="CetButton_export.event"
              :disable_in="
                activeTab === 'node' && !CetTree_1.checkedNodes.length
              "
            ></CetButton>
          </div>
        </div>
        <div class="flex-auto">
          <Benchmarking
            v-if="activeTab === 'benchmark'"
            :queryTime_in="queryTime"
            :clickNode_in="clickNode"
            :energyType_in="ElSelect_1.value"
            :indicatorType_in="indicatorType"
            :dimConfigId_in="ElSelect_treeType.value"
            v-bind="benchmarking"
          />
          <Compare
            v-if="activeTab === 'compare'"
            :indicatorType_in="indicatorType"
            :queryTime_in="queryTime"
            :clickNode_in="clickNode"
            :energyType_in="ElSelect_1.value"
            :dimConfigId_in="ElSelect_treeType.value"
            v-bind="compare"
          />
          <Node
            v-if="activeTab === 'node'"
            :indicatorType_in="indicatorType"
            :queryTime_in="queryTime"
            :checkedNodes_in="CetTree_1.checkedNodes"
            :energyType_in="ElSelect_1.value"
            :dimConfigId_in="ElSelect_treeType.value"
            v-bind="nodeComponent"
            @expandNode="expandNode"
            @cancelSelect="cancelSelect"
          />
        </div>
      </div>
    </template>
  </CetAside>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import TimeTool from "./TimeTool.vue";
import Benchmarking from "./benchmarking.vue";
import Compare from "./compare.vue";
import Node from "./node.vue";

export default {
  name: "EnergyEfficiencyIndexes",
  components: {
    TimeTool,
    Benchmarking,
    Compare,
    Node
  },

  computed: {
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    numberOfNodesCompared() {
      return this.$store.state.systemCfg.numberOfNodesCompared || 4;
    },
    indicatorType() {
      const id = this.ElSelect_indicatorType.value;
      const list = this.ElOption_indicatorType.options_in || [];
      return list.find(item => item.id === id);
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      ElSelect_1: {
        value: null,
        style: {},
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      // 统计周期下拉
      ElSelect_cycle: {
        value: 12,
        style: {},
        event: {
          change: this.ElSelect_cycle_change_out
        }
      },
      ElOption_cycle: {
        options_in: [
          {
            id: 7,
            text: $T("小时")
          },
          {
            id: 12,
            text: $T("日")
          },
          {
            id: 13,
            text: $T("周")
          },
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 指标类型下拉
      ElSelect_indicatorType: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_indicatorType_change_out
        }
      },
      ElOption_indicatorType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_treeType: {
        value: -1,
        style: {},
        event: {
          change: this.getTreeData
        }
      },
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      clickNode: null,
      treeKey: 1,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children",
          disabled: this.setDisabled
        },
        highlightCurrent: true,
        showCheckbox: false,
        defaultExpandedKeys: [],
        checkStrictly: true,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300),
          check: this.CetTree_1_checkedNodes_out
        }
      },
      activeTab: "benchmark",
      tabList: [
        { label: "benchmark", text: $T("能效对标") },
        { label: "compare", text: $T("同比环比") },
        { label: "node", text: $T("节点对比") }
      ],

      startTime: Date.now(),
      timeType: [
        {
          type: "date",
          text: $T("日"),
          typeID: 7,
          number: 1,
          unit: "d"
        },
        {
          type: "week",
          text: $T("周"),
          typeID: 13,
          number: 1,
          unit: "w"
        },
        {
          type: "month",
          text: $T("月"),
          typeID: 12,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          number: 1,
          typeID: 14,
          unit: "y"
        },
        {
          type: "year",
          text: $T("十年"),
          number: 1,
          typeID: 17,
          unit: "y"
        }
      ],
      queryTime: {
        startTime: null,
        endTime: null,
        cycle: 12 //17年，14月，12日，20自定义，7小时
      },
      CetButton_export: {
        visible_in: true,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      benchmarking: {
        update_in: Date.now()
      },
      compare: {
        update_in: Date.now()
      },
      nodeComponent: {
        update_in: Date.now()
      },
      foucsNode: null
    };
  },
  watch: {},

  methods: {
    async init() {
      await this.getProjectEnergy();
      await this.queryTreeType();
      this.ElSelect_cycle_change_out();
    },
    /**
     * 获取项目能源类型
     */
    async getProjectEnergy() {
      this.ElOption_1.options_in = [];
      const res = await customApi.getProjectEnergy();
      const selectData = res?.data || [];
      this.ElOption_1.options_in = selectData;
      this.ElSelect_1.value = selectData?.[0]?.energytype ?? null;
    },
    /**
     * 获取节点树类型
     */
    async queryTreeType() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (
        res?.data || [
          {
            id: -1,
            name: $T("固定管理层级")
          }
        ]
      ).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
    },
    /**
     * 能源类型变化
     */
    async ElSelect_1_change_out() {
      this.CetTree_1.selectNode = null;
      this.clickNode = null;
      this.CetTree_1.inputData_in = [];
      await this.getEnergyefficiencyset();
      this.getTreeData();
    },
    /**
     * 统计周期变化
     */
    async ElSelect_cycle_change_out() {
      this.CetTree_1.selectNode = null;
      this.clickNode = null;
      this.CetTree_1.inputData_in = [];
      this.ElSelect_indicatorType.value = null;
      await this.getEnergyefficiencyset();
      this.getTreeData();
    },
    /**
     * 获取指标类型
     */
    async getEnergyefficiencyset() {
      if (!this.ElSelect_cycle.value || !this.ElSelect_1.value) {
        return;
      }
      const param = {
        aggregationCycle: this.ElSelect_cycle.value,
        energyTypes: [this.ElSelect_1.value]
      };
      const res = await customApi.queryEnergyefficiencyset(param);
      if (res.code !== 0) {
        return;
      }
      const data = res.data || [];
      this.ElOption_indicatorType.options_in = data;
      this.ElSelect_indicatorType.value = data[0]?.id;
    },
    /**
     * 指标类型变化
     */
    ElSelect_indicatorType_change_out() {
      this.getTreeData();
    },
    /**
     * 获取节点树
     */
    getTreeData() {
      if (!this.ElSelect_indicatorType.value) {
        this.CetTree_1.inputData_in = [];
        return;
      }
      this.ElSelect_treeType.value === -1
        ? this.getTreeData1()
        : this.getTreeData2();
    },
    async getTreeData1() {
      const queryData = {
        efId: this.indicatorType.id
      };
      const res = await customApi.treeByEf(queryData);
      const data = res.data || [];
      // 选中第一个有数据 childSelectState = 1 的节点并展开节点
      const obj = this._.find(this.dataTransform(data), {
        childSelectState: 1,
        correlationState: 1
      });
      this.CetTree_1.inputData_in = data;
      if (!obj) {
        this.initCharts();
        return;
      }
      this.CetTree_1.selectNode = obj;
      if (this.activeTab === "node") {
        this.CetTree_1.checkedNodes = [obj];
      }
      this.treeKey++;
    },
    async getTreeData2(keepSelectNode) {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        energyType: this.ElSelect_1.value,
        energyEfficiencySetId: this.indicatorType.id,
        startTime: this.queryTime?.startTime,
        endTime: this.queryTime?.endTime,
        aggregationCycle: this.queryTime.cycle
      };
      const res = await customApi.getEfNodeTreeAnalysis(queryData);
      const data = res?.data || [];

      this.CetTree_1.inputData_in = data;
      let obj,
        checkedNodes = [];
      const nodeList = this.dataTransform(data);

      if (keepSelectNode) {
        obj = this._.find(nodeList, ["tree_id", this.clickNode.tree_id]);
        if (this.activeTab === "node") {
          this.CetTree_1.checkedNodes.forEach(item => {
            const node = this._.find(nodeList, ["tree_id", item.tree_id]);
            node && checkedNodes.push(node);
          });
        }
      }

      if (!obj) {
        // 选中第一个有数据 childSelectState = 1 的节点并展开节点
        obj = this._.find(nodeList, {
          childSelectState: 1,
          correlationState: 1
        });
      }

      if (!obj) {
        this.initCharts();
        return;
      }

      if (!checkedNodes?.length) {
        checkedNodes.push(obj);
      }

      this.CetTree_1.selectNode = obj;

      if (this.activeTab === "node") {
        this.CetTree_1.checkedNodes = checkedNodes;
        if (keepSelectNode) {
          // 更新节点树时会输出一次当前选中节点，后续的逻辑会将勾选节点重置，这里需要阻止此逻辑
          this.stopNodeOut = true;
          this.CetTree_1_checkedNodes_out(obj, { checkedNodes });
        }
      }

      this.treeKey++;
    },
    initCharts() {
      this.clickNode = null;
    },
    CetTree_1_currentNode_out(val) {
      if (this.stopNodeOut) {
        this.stopNodeOut = false;
        return;
      }
      if (this.activeTab === "node") {
        return;
      }
      if (this._.isEmpty(val)) {
        return;
      }
      if (val.childSelectState === 2 && val.correlationState === 2) {
        return this.$message.warning(
          $T("没有该节点权限，该节点没有关联当前方案")
        );
      } else if (val.childSelectState === 1 && val.correlationState === 2) {
        return this.$message.warning($T("该节点没有关联当前方案"));
      } else if (val.childSelectState === 2 && val.correlationState === 1) {
        return this.$message.warning($T("没有该节点权限"));
      }

      this.clickNode = this._.cloneDeep(val);
      this.getData();
    },
    CetTree_1_checkedNodes_out(val, checkData) {
      const nodeCheck = checkData.checkedNodes || [];
      if (nodeCheck.length > this.numberOfNodesCompared) {
        this.$message.warning(
          $T("最多对比{0}个节点", this.numberOfNodesCompared)
        );
        this.CetTree_1.checkedNodes = nodeCheck.filter(
          item => item.tree_id !== val.tree_id
        );
        return;
      }
      this.getData();
    },
    getData: _.debounce(function () {
      if (this.activeTab === "benchmark") {
        this.benchmarking.rootNode_in = this.CetTree_1.inputData_in[0];
        this.benchmarking.update_in = Date.now();
        return;
      }
      if (this.activeTab === "compare") {
        this.compare.rootNode_in = this.CetTree_1.inputData_in[0];
        this.compare.update_in = Date.now();
        return;
      }
      if (this.activeTab === "node") {
        this.nodeComponent.rootNode_in = this.CetTree_1.inputData_in[0];
        this.nodeComponent.update_in = Date.now();
        return;
      }
    }, 200),
    /**
     * 类型变化
     * @param val 选项卡
     */
    handlerChangeTab(val) {
      this.maxAndMin = true;
      this.CetTree_1.showCheckbox = val === "node";
      if (val === "node") {
        if (!this.clickNode) {
          return;
        }
        this.CetTree_1.checkedNodes = [this.clickNode];
        this.getData();
      }
      this.treeKey++;
    },
    /**
     * 时间变化
     */
    changeQueryTime: _.debounce(function ({ val, timeOption }) {
      const date = this.$moment(val);
      let queryTime;
      // 十年数据单独处理
      if (timeOption.typeID === 17) {
        queryTime = {
          startTime: this.$moment(val)
            .subtract(9, timeOption.unit)
            .startOf(timeOption.unit)
            .valueOf(),
          endTime: date.endOf(timeOption.unit).valueOf() + 1,
          cycle: timeOption.typeID,
          unit: timeOption.unit
        };
      } else if (timeOption.typeID === 13) {
        //十周数据单独处理
        queryTime = {
          startTime: this.$moment(val)
            .subtract(9, timeOption.unit)
            .startOf("isoWeek")
            .valueOf(),
          endTime: date.endOf("isoWeek").valueOf() + 1,
          cycle: timeOption.typeID,
          unit: timeOption.unit
        };
      } else {
        queryTime = {
          startTime: date.startOf(timeOption.unit).valueOf(),
          endTime: date.endOf(timeOption.unit).valueOf() + 1,
          cycle: timeOption.typeID,
          unit: timeOption.unit
        };
      }
      this.queryTime = queryTime;
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true);
      } else {
        this.getData();
      }
    }, 300),
    /**
     * 导出
     */
    CetButton_export_statusTrigger_out() {
      if (this._.isEmpty(this.clickNode)) {
        return;
      }
      if (this.activeTab === "node") {
        let params = {
          nodes: this.CetTree_1.checkedNodes.map(i => ({
            nodeId: i.id,
            modelLabel: i.modelLabel,
            name: i.name
          })),
          startTime: this.queryTime.startTime,
          endTime: this.queryTime.endTime,
          aggregationCycle: this.getCyclePreLevel(this.queryTime.cycle),
          energyType: this.ElSelect_1.value,
          unitType: this.indicatorType?.unittype,
          efSetId: this.indicatorType?.id,
          unitCycle: this.queryTime.cycle,
          dimConfigId: this.ElSelect_treeType.value
        };
        common.downExcel(
          "/eem-base/energy-efficiency/v1/analysis/query/ef/data/export",
          params
        );
        return;
      }
      if (this.activeTab === "compare") {
        const data = {
          nodes: [
            {
              modelLabel: this.clickNode.modelLabel,
              nodes: [
                {
                  id: this.clickNode.id,
                  name: this.clickNode.name
                }
              ]
            }
          ],
          startTime: this.queryTime.startTime,
          endTime: this.queryTime.endTime,
          aggregationCycle: this.queryTime.cycle,
          energyTypes: [this.ElSelect_1.value],
          unittype: [this.indicatorType.unittype],
          energyefficiencysetId: [this.indicatorType.id],
          type: true,
          dimConfigId: this.ElSelect_treeType.value
        };
        common.downExcel(
          "/eem-base/energy-efficiency/v1/analysis/energyEfficiency/withLevel/export",
          data
        );
        return;
      }
      if (this.activeTab === "benchmark") {
        const data = {
          startTime: this.queryTime.startTime,
          endTime: this.queryTime.endTime,
          aggregationCycle: this.queryTime.cycle,
          dimConfigId: this.ElSelect_treeType.value,
          energyEfficiencySetId: this.indicatorType.id,
          energyType: this.ElSelect_1.value,
          node: {
            id: this.clickNode.id,
            modelLabel: this.clickNode.modelLabel
          },
          type: true,
          unitType: this.indicatorType.unittype
        };
        common.downExcel(
          "/eem-base/energy-efficiency/v1/analysis/energyEfficiency/withLevel/export",
          data
        );
        return;
      }
    },
    getSelectNode() {
      let list = this.$refs.cetTree.$refs.tree.getCheckedNodes() || [];

      let resArr = []; //存放结果数组
      list.forEach(item => {
        resArr.push({
          nodeId: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        });
      });

      return resArr;
    },
    getCyclePreLevel(iLevel) {
      if (iLevel === 7) {
        return 12;
      } else if (iLevel === 12) {
        return 14;
      } else if ([13, 14, 17].includes(iLevel)) {
        return 17;
      }
      return 0;
    },
    setDisabled(data) {
      if (data.childSelectState === 2 || data.correlationState === 2) {
        return true;
      }
      return false;
    },
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const childSelectState = this._.get(node, "data.childSelectState", null);
      const correlationState = this._.get(node, "data.correlationState", null);
      if (childSelectState === 2 || correlationState === 2) {
        return "#989898";
      }
      return;
    },
    expandNode(val) {
      //展开选中的节点，并将其他节点收缩，确保快速找到该节点
      const nodesMap = this.$refs.cetTree.$refs.tree.store.nodesMap;
      Object.keys(nodesMap).forEach(key => {
        nodesMap[key].expanded = false;
        nodesMap[key].isCurrent = false;
      });
      const node = this.$refs.cetTree.$refs.tree.getNode(val.name);
      this.foucsNode = node;
      this.CetTree_1.defaultExpandedKeys = [node.parent.key];
    },
    cancelSelect(name) {
      this.$refs.cetTree.$refs.tree.setChecked(name, false);
      // 高亮取消的节点
      const nodesMap = this.$refs.cetTree.$refs.tree.store.nodesMap;
      Object.keys(nodesMap).forEach(key => {
        nodesMap[key].expanded = false;
        nodesMap[key].isCurrent = false;
      });
      const node = this.$refs.cetTree.$refs.tree.getNode(name);
      this.foucsNode = node;
      this.CetTree_1.defaultExpandedKeys = [node.parent.key];
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
</style>
