<template>
  <CetChart
    :inputData_in="CetChart_1.inputData_in"
    v-bind="CetChart_1.config"
  />
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  name: "networkEnergyTbhb",
  props: {
    queryBody_in: {
      type: Object
    },
    thbSelectList: {
      type: Array
    },
    energy_in: {
      type: Object
    },
    CustomDatePicker_1: {
      type: Object
    },
    queryTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            color: ["#3398DB", "#AA4643", "#89A54E"],
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              left: "10",
              right: "0",
              bottom: "0",
              containLabel: true
            },
            legend: {
              data: []
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      }
    };
  },
  watch: {
    queryTrigger_in() {
      this.queryData();
    }
  },
  methods: {
    async queryData() {
      if (!this.queryBody_in) {
        this.filChartData1([]);
        return;
      }
      this.CetChart_1.config.options = {};
      let queryBody = this.queryBody_in;

      const response = await customApi.energyTbhbTrend(queryBody);
      if (response.code !== 0) {
        return;
      }
      this.filChartData1(response.data);
    },
    filChartData1(data) {
      let self = this;
      let customIndex = 1;
      this.CetChart_1.config.options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            let list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              let showValue = self.getLegendTitle(
                val[i].data.dataInfo,
                val[i].data.time,
                customIndex
              );
              if (i === 0) {
                //TODO:可以做成标题不按照x轴来
                formatterStr += `${val[i].name}`;
              }
              formatterStr += `<br/>${val[i].marker}${showValue} : ${
                val[i].value !== null ? val[i].value : "--"
              }(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          left: "16",
          right: "0",
          bottom: "16",
          containLabel: true
        },
        legend: {
          data: []
        },

        // dataset: {
        //   source: []
        // },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            align: "left"
          }
        },
        series: []
      };
      var _this = this,
        currentdata = data.currentdata || [],
        tbdata = data.tbdata || [],
        hbdata = data.hbdata || [];
      if (currentdata.length === 0) {
        return;
      }
      currentdata.map((item, index) => {
        item.hbValue = hbdata.length > index && hbdata[index].value;
        item.tbValue = tbdata.length > index && tbdata[index].value;
      });
      let xAxisData = [],
        yAxisData = [[], [], []];
      customIndex = this.getMarginType(
        currentdata[0].time,
        currentdata[currentdata.length - 1].time
      );

      let source = [];
      let unit = data.symbol || "--";
      let typeLabel = ["base", "tb", "hb"];
      for (let i = 0, len = currentdata.length; i < len; i++) {
        let value = [];
        value[0] = common.formatNumberWithPrecision(currentdata[i].value, 2);
        value[1] = common.formatNumberWithPrecision(currentdata[i].tbValue, 2);
        value[2] = common.formatNumberWithPrecision(currentdata[i].hbValue, 2);
        let product = this.getAxixs(currentdata[i].time, customIndex);
        let sour = {
          time: currentdata[i].time,
          unit: unit,
          product: product,
          yAxis1: value[0],
          yAxis2: value[1],
          yAxis3: value[2]
        };
        source.push(sour);
        xAxisData.push(product);
        for (let index = 0; index < 3; index++) {
          yAxisData[index].push({
            time: currentdata[i].time,
            unit: unit,
            name: product,
            value: value[index],
            dataInfo: typeLabel[index]
          });
        }
      }

      let legend = [];
      let CheckedArray = this.thbSelectList.map(item => item.id),
        len = CheckedArray.length,
        queryType;
      let series = [
        {
          name: $T("本期"),
          type: "bar",
          smooth: true,
          barWidth: "60%",
          data: yAxisData[0]
        }
      ];

      if (len === 0) {
        queryType = 0;
      } else if (len === 1) {
        queryType = CheckedArray[0];
      } else if (len === 2) {
        queryType = 3;
      }
      if (this.cycle === 17 && len !== 0) {
        queryType = 1;
      }

      //1:同比；2：环比；3：同比环比
      if (queryType === 1) {
        series.push({
          name: $T("同比"),
          type: "line",
          smooth: true,
          data: yAxisData[1]
        });
      } else if (queryType === 2) {
        series.push({
          name: $T("环比"),
          type: "line",
          smooth: true,
          data: yAxisData[2]
        });
      } else if (queryType === 3) {
        series = series.concat(
          {
            name: $T("同比"),
            type: "line",
            smooth: true,
            data: yAxisData[1]
          },
          {
            name: $T("环比"),
            type: "line",
            smooth: true,
            data: yAxisData[2]
          }
        );
      }

      var dataset = {
        source: source
      };
      series.forEach(item => {
        legend.push(item.name);
      });
      series[0].data.forEach(item => {
        if (Number(item.value) < 0) {
          item.value = 0;
        }
      });
      _this.$nextTick(function () {
        _this.CetChart_1.config.options.dataset = dataset;
        var ElOption_1Text;
        ElOption_1Text = _this.energy_in && _this.energy_in.text;
        _this.CetChart_1.config.options.yAxis.name = ElOption_1Text
          ? `${ElOption_1Text}${$T("（{0}）", unit)}`
          : "";

        _this.CetChart_1.config.options.xAxis.data = xAxisData;
        _this.CetChart_1.config.options.series = series;
        _this.CetChart_1.config.options.legend.data = legend;
      });
    },
    //1:同比；2：环比；3：同比环比
    getLegendTitle(dataType, time, customIndex) {
      let type = this.CustomDatePicker_1.queryTime.cycle;
      if (dataType === "base") {
        return this.getLegend(time, customIndex);
      } else if (dataType === "tb") {
        if (type === 12) {
          return this.getLegend(this.$moment(time).subtract(1, "month"));
        } else if (type === 14) {
          return this.getLegend(this.$moment(time).subtract(1, "year"));
        } else {
          return this.getLegend(this.$moment(time).subtract(1, "year"));
        }
      } else if (dataType === "hb") {
        if (type === 12) {
          return this.getLegend(this.$moment(time).subtract(1, "day"));
        } else if (type === 14) {
          return this.getLegend(this.$moment(time).subtract(1, "month"));
        } else {
          console.error("年度是没有环比数据的");
          return "--";
        }
      }
      return "--";
    },
    //获取图表图例
    getLegend(pDate, customIndex) {
      let sFormat = "";
      let cycle = this.CustomDatePicker_1.queryTime.cycle;
      if (cycle === 12) {
        sFormat = "YYYY-MM-DD";
      } else if (cycle === 14) {
        sFormat = "YYYY-MM";
      } else if (cycle === 17) {
        sFormat = "YYYY";
      } else if (cycle === 20) {
        if (customIndex === 1) {
          sFormat = "HH";
        } else if (customIndex === 2) {
          sFormat = "YYYY-MM";
        } else if (customIndex === 3) {
          sFormat = "YYYY";
        } else {
          sFormat = "YYYY";
        }
      }

      return this.$moment(pDate).format(sFormat);
    },
    getMarginType(iStartTime1, iEndTime) {
      let iMarginDay = this.$moment(iEndTime).subtract(2, "day");
      let iMarginMonth = this.$moment(iEndTime).subtract(3, "month");
      let iMarginYear = this.$moment(iEndTime).subtract(3, "year");
      if (iStartTime1 > iMarginDay) {
        return 1;
      } else if (iStartTime1 > iMarginMonth) {
        return 2;
      } else if (iStartTime1 > iMarginYear) {
        return 3;
      } else {
        return 4;
      }
    },
    //过滤获取图表x轴对应值
    getAxixs(pDate, customIndex) {
      let oDate = this.$moment(pDate);
      let type = this.CustomDatePicker_1.queryTime.cycle;
      if (type === 12) {
        if (oDate.format("HH:mm") == "00:00") {
          return oDate.format("DD");
        }
        return oDate.format("HH:mm");
      } else if (type === 14) {
        return oDate.format("DD");
      } else if (type === 17) {
        return oDate.format("MM");
      } else if (type === 20) {
        if (customIndex === 1) {
          if (oDate.format("HH:mm") == "00:00") {
            return oDate.format("YYYY-MM-DD");
          }
          return oDate.format("HH:mm");
        } else if (customIndex === 2) {
          if (oDate.format("DD") == "01") {
            return oDate.format("MM-DD");
          }
          return oDate.format("DD");
        } else if (customIndex === 3) {
          if (oDate.format("MM") == "01") {
            return oDate.format("YYYY-MM");
          }
          return oDate.format("MM");
        } else if (customIndex === 4) {
          return oDate.format("YYYY");
        }
      }
      return oDate.format("YYYY-MM-DD");
    }
  }
};
</script>

<style></style>
