<template>
  <div
    class="custom-steps-wrap bg-BG1"
    :class="{
      existenceSubName: existenceSubName
    }"
  >
    <div class="wrap flex flex-row centered">
      <template v-for="(item, index) in steps">
        <div
          :key="index"
          class="stepItem"
          :class="{
            finish: active > index,
            current: active === index
          }"
        >
          <div class="flex flex-row">
            <div class="steo-icon">
              <i class="el-icon-check centered" v-if="active > index"></i>
              <span class="step-num centered" v-else>{{ index + 1 }}</span>
            </div>
            <div class="text">
              {{ item.title }}
            </div>
            <div v-if="index < steps.length - 1" class="line"></div>
          </div>

          <div v-if="existenceSubName" class="description">
            {{ item.description }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "customSteps",
  props: {
    active: Number,
    steps: {
      type: Array,
      required: true,
      validator(steps) {
        // 确保每一项都有 `title` , `description`选传，且都是字符串
        return steps.every(step => {
          return (
            step &&
            typeof step.title === "string" &&
            (typeof step.description === "string" || !step.description)
          );
        });
      },
      default: () => []
    }
  },
  computed: {
    existenceSubName() {
      const val = this.steps.find(i => i.description);
      return !!val;
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-steps-wrap {
  position: relative;
  height: 80px;
  border-radius: var(--Ra);
  &.existenceSubName {
    height: 84px;
    .wrap {
      height: 54px;
    }
  }
  .centered {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .wrap {
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    .stepItem {
      position: relative;
      .steo-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        border-radius: 32px 32px 32px 32px;
        border: 1px solid;
        color: var(--T4);
        position: relative;
        margin-right: 24px;
        .step-num {
          color: var(--T4);
        }
      }
      .text {
        font-size: 16px;
        width: max-content;
        color: var(--T4);
      }
      .line {
        position: relative;
        width: 148px;
        margin: 0 24px;
        &:before {
          content: "";
          display: block;
          width: 100%;
          height: 2px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          background-color: var(--T4);
          color: var(--T4);
        }
      }
      &.current {
        .text {
          color: var(--ZS);
        }
        .steo-icon {
          background-color: var(--ZS);
          color: var(--T1);
          border-color: var(--ZS);
          .step-num {
            color: #fff;
          }
        }
      }
      &.finish {
        .steo-icon {
          border-color: var(--ZS);
        }
        .text {
          color: var(--T1);
        }
        .line:before {
          background-color: var(--ZS);
        }
        .el-icon-check {
          color: var(--ZS);
        }
      }
    }
    .description {
      margin-left: 50px;
      font-size: 14px;
      line-height: 22px;
      width: max-content;
      color: var(--T4);
    }
  }
}
</style>
