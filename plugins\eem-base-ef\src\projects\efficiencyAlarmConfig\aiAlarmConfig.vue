<template>
  <div class="h-full w-full flex flex-col">
    <ElForm
      :model="form.data"
      :rules="form.rules"
      label-position="top"
      ref="form"
    >
      <el-form-item :label="$T('方案名称')" prop="name">
        <el-input
          class="w-[300px]"
          v-model.trim="form.data.name"
          :placeholder="$T('输入方案名称')"
        ></el-input>
      </el-form-item>
    </ElForm>
    <div class="flex flex-row mb-J3 justify-between">
      <div class="flex flex-row items-center">
        <el-button
          size="small"
          icon="el-icon-arrow-left"
          @click="queryPrv('yearTime', 'years', yearTimeChange)"
          class="mr-J0"
        ></el-button>
        <CustomElDatePicker
          class="yearPicker"
          :prefix_in="$T('时间')"
          v-model="yearTime"
          type="year"
          size="small"
          value-format="timestamp"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :format="$T('yyyy 年')"
          @change="yearTimeChange"
        ></CustomElDatePicker>
        <el-button
          size="small"
          icon="el-icon-arrow-right"
          @click="queryNext('yearTime', 'years', yearTimeChange)"
          class="ml-J0"
        ></el-button>
      </div>
      <div class="flex flex-row justify-end">
        <el-button
          size="small"
          icon="el-icon-arrow-left"
          @click="queryPrv('monthTime', 'months', monthTimeChange)"
          class="mr-J0"
        ></el-button>
        <CustomElDatePicker
          :prefix_in="$T('时间')"
          v-model="monthTime"
          type="month"
          size="small"
          value-format="timestamp"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :format="$T('yyyy 年 MM 月')"
          @change="monthTimeChange"
        ></CustomElDatePicker>
        <el-button
          size="small"
          icon="el-icon-arrow-right"
          @click="queryNext('monthTime', 'months', monthTimeChange)"
          class="ml-J0"
        ></el-button>
      </div>
    </div>
    <div class="flex flex-row flex-auto">
      <div class="w-[220px] mr-J3 flex flex-col">
        <div
          class="flex flex-row items-center border rounded-Ra border-B1 border-solid"
        >
          <span class="w-[120px] text-center p-J0">
            {{ $T("年度") }}
          </span>
          <span
            class="p-J0 box-border text-center flex-auto border-l border-B1 border-solid h-full border-t-0 border-r-0 border-b-0"
          >
            {{ yearConfigValue }}
          </span>
        </div>
        <el-table
          class="flex-auto mt-J3"
          :data="monthData"
          highlight-current-row
          border
          height="true"
        >
          <el-table-column
            prop="key1"
            :label="$T('时间')"
            align="left"
            width="80"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="key2"
            align="left"
            :label="$T('能效阈值')"
            show-overflow-tooltip
            :formatter="val => (val.key2 != null ? val.key2.toFixed(2) : '--')"
          ></el-table-column>
        </el-table>
      </div>
      <div class="flex-auto flex flex-col">
        <div class="flex-auto flex flex-row">
          <div class="w-[200px] mr-J3">
            <el-table class="h-full" :data="weekTableData" border height="true">
              <el-table-column
                prop="key1"
                :label="$T('周数/年')"
                align="left"
                width="90"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="key2"
                align="left"
                :label="$T('周能效阈值')"
                show-overflow-tooltip
                :formatter="
                  val => (val.key2 != null ? val.key2.toFixed(2) : '--')
                "
              ></el-table-column>
            </el-table>
          </div>
          <div class="flex-auto overflow-auto">
            <EditMonth v-bind="editMonth" :data_in="benchmarksetConfig" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-base/utils/common.js";
import EditMonth from "./EditMonth.vue";
export default {
  name: "aiAlarmConfig",
  props: {
    data_in: Object
  },
  components: {
    EditMonth
  },
  computed: {
    benchmarksetConfig() {
      return this.data_in?.benchmarkset_model ?? [];
    }
  },
  data() {
    const initMonthData = [
      {
        key1: $T("1月"),
        key2: null
      },
      {
        key1: $T("2月"),
        key2: null
      },
      {
        key1: $T("3月"),
        key2: null
      },
      {
        key1: $T("4月"),
        key2: null
      },
      {
        key1: $T("5月"),
        key2: null
      },
      {
        key1: $T("6月"),
        key2: null
      },
      {
        key1: $T("7月"),
        key2: null
      },
      {
        key1: $T("8月"),
        key2: null
      },
      {
        key1: $T("9月"),
        key2: null
      },
      {
        key1: $T("10月"),
        key2: null
      },
      {
        key1: $T("11月"),
        key2: null
      },
      {
        key1: $T("12月"),
        key2: null
      }
    ];
    return {
      form: {
        data: {},
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入方案名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.check_pattern_name
          ]
        }
      },
      yearTime: +this.$moment(),
      monthTime: +this.$moment(),
      yearConfigValue: "",
      initMonthData: initMonthData,
      monthData: this._.cloneDeep(initMonthData),
      weekTableData: [],
      calendarValue: Date.now(),
      editMonth: {
        data: [],
        date: Date.now()
      }
    };
  },
  watch: {
    data_in: {
      handler(val) {
        this.init(val);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    init() {
      this.form.data = {
        name: this.data_in?.name || ""
      };
      this.yearTime = +this.$moment();
      this.monthTime = +this.$moment();
      this.yearTimeChange();
      this.monthTimeChange();
    },
    queryPrv(dataStr, type, callback) {
      const value = this.$moment(this[dataStr]);
      this[dataStr] = +value.subtract(1, type);
      callback && callback();
    },
    queryNext(dataStr, type, callback) {
      const value = this.$moment(this[dataStr]);
      this[dataStr] = +value.add(1, type);
      callback && callback();
    },
    yearTimeChange() {
      const year = +this.$moment(this.yearTime).startOf("year");

      // 处理年数据
      const yearConfig = this.benchmarksetConfig.find(
        item => item.validtime === year
      );
      this.yearConfigValue =
        yearConfig?.limitvalue != null ? yearConfig.limitvalue : "--";

      // 处理月数据
      this.monthData = this._.cloneDeep(this.initMonthData);
      this.benchmarksetConfig.forEach(
        ({ aggregationcycle, validtime, limitvalue }) => {
          if (
            +this.$moment(validtime).startOf("year") === year &&
            aggregationcycle === 14
          ) {
            this.monthData[this.$moment(validtime).month()].key2 = limitvalue;
          }
        }
      );
    },
    monthTimeChange() {
      // 处理周数据
      const startDay = this.$moment(this.monthTime)
        .startOf("month")
        .day(1)
        .startOf("date");
      const month = this.$moment(this.monthTime).month();
      const weekTableData = [
        {
          key1: startDay.week(),
          key2: null,
          time: +this.$moment(startDay)
        }
      ];
      while (startDay.add("days", 7).month() == month) {
        const time = +this.$moment(startDay);
        const weekConfig = this.benchmarksetConfig.find(
          ({ validtime, aggregationcycle }) => {
            return validtime === time && aggregationcycle === 13;
          }
        );
        weekTableData.push({
          key1: this.$moment(startDay).week(),
          key2: weekConfig?.limitvalue ?? null,
          time
        });
      }
      this.weekTableData = weekTableData;

      this.editMonth.date = this.monthTime;
    },
    async validate() {
      try {
        await this.$refs.form.validate();
        return this.form.data;
      } catch (error) {
        return false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.yearPicker :deep(.el-date-editor.el-input) {
  width: 160px;
}
.calendar :deep(.el-calendar__header) {
  display: none;
}
</style>
