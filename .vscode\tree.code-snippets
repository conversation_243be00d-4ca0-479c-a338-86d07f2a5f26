{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-tree的代码片段
  "cet-tree-template": {
    "prefix": "cet-tree-template",
    "body": [
      "   <CetTree                                                                 ",
      "    :selectNode.sync=\"CetTree_$1.selectNode\"                                      ",
      "    :checkedNodes.sync=\"CetTree_$1.checkedNodes\"                      ",
      "    :searchText_in.sync=\"CetTree_$1.searchText_in\"                      ",
      "    v-bind=\"CetTree_$1\"                                                  ",
      "    v-on=\"CetTree_${1:请输入组件唯一识别字符串}.event\"                   ",
      "   ></CetTree>                                                            "
    ],
    "description": ""
  },
  "cet-tree-data": {
    "prefix": "cet-tree-data",
    "body": [
      "// ${1:设置组件唯一识别字段}树组件                            ",
      "CetTree_$1: {                                                                             ",
      "  inputData_in: [],                                                                             ",
      "  selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: \"city_53\" }                                                                             ",
      "  checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: \"city_54\" }]                                                                             ",
      "  filterNodes_in: null, //[]表示过滤掉所有节点                                                                             ",
      "  searchText_in: \"\",                                                                             ",
      "  showFilter: true,                                                                             ",
      "  nodeKey: \"tree_id\",                                                                             ",
      "  expandWhenChecked: true, //组件节点可选择状态下，可设置选择节点是否展开expandWhenChecked,默认为true   ",
      "  props: {                                                                             ",
      "    label: \"name\",                                                                             ",
      "    children: \"children\"                                                                             ",
      "  },                                                                             ",
      "  highlightCurrent: true,                                                                             ",
      "  event: {                                                                             ",
      "    currentNode_out: this.CetTree_$1_currentNode_out,                                                                             ",
      "    parentList_out: this.CetTree_$1_parentList_out,                                                                             ",
      "    checkedNodes_out: this.CetTree_$1_checkedNodes_out,                                                                             ",
      "    halfCheckNodes_out: this.CetTree_$1_halfCheckNodes_out,                                                                             ",
      "    allCheckNodes_out: this.CetTree_$1_allCheckNodes_out                                                                             ",
      "  }                                                                             ",
      "},                                                                             "
    ],
    "description": ""
  },
  "cet-tree-method": {
    "prefix": "cet-tree-method",
    "body": [
      "// ${1:设置组件唯一识别字段} 输出                                ",
      "CetTree_$1_currentNode_out(val) {                              ",
      "},                                                          ",
      "CetTree_$1_parentList_out(val) {                      ",
      "},                                                ",
      "CetTree_$1_checkedNodes_out(val) {                   ",
      "},                                                       ",
      "CetTree_$1_halfCheckNodes_out(val) {                    ",
      "},                                                         ",
      "CetTree_$1_allCheckNodes_out(val) {                          ",
      " },                                                         "
    ],
    "description": ""
  }
}
