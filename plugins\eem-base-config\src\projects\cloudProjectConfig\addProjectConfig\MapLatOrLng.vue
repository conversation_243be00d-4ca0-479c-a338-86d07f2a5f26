<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
    <el-container class="content" style="height: 100%">
      <!-- <el-header height="40px" style="padding:0px;line-height:40px;">
        所属设备
        <span>XXXXX</span>
      </el-header> -->
      <el-main style="height: 100%; padding: 0px; position: relative">
        <div ref="container">
          <BaiduMap
            ref="baiduMap"
            :points="PowerPoints"
            @customPoint="AquireAddress"
            :searchSite="ImportantSearch"
            :customMark="CustomMark"
            :showArea="showArea"
            :customPolygon="customPolygon"
            style="width: 100%; height: 400px"
          ></BaiduMap>
        </div>
      </el-main>
    </el-container>
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import BaiduMap from "./BaiduMap";
import { loadBMap } from "eem-base/utils/loadBMap.js";
export default {
  name: "MapLatOrLng",
  components: {
    BaiduMap
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    mapInfo: {
      default: () => {}
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    showArea: {
      type: Boolean
    }
  },

  computed: {
    isOnline() {
      return this.$store.state.systemCfg.onLine;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("选择地点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CustomMark: true,
      customPolygon: "",
      ImportantSearch: {
        isNeed: true,
        position: "absolute",
        left: "30px",
        top: "50px",
        right: "auto",
        bottom: "auto",
        zIndex: 30000
      },
      PowerPoints: [{}],
      locationInfo: {
        lng: 0,
        lat: 0
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.setPointAndArea(this.mapInfo);
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    checkPoint(oPoint) {
      if (
        oPoint &&
        !this._.isNil(oPoint.latitude) &&
        !this._.isNil(oPoint.longitude)
      ) {
        return true;
      }
      return false;
    },

    setPointAndArea(val) {
      if (this.checkPoint(val.point)) {
        this.locationInfo = {
          lat: val.point.latitude,
          lng: val.point.longitude
        };

        this.PowerPoints[0] = val.point;

        this.customPolygon = val.areaJson || "";
        this.$nextTick(() => {
          this.$refs.baiduMap.reset();
        });
      } else {
        this.getlocation();
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.$refs.baiduMap.closeEditMap();
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      let areaJson = this.$refs.baiduMap.getAreaData();
      this.$refs.baiduMap.closeEditMap();
      this.$emit("finishData_out", {
        ...this.locationInfo,
        areaJson: areaJson ? JSON.stringify(areaJson) : ""
      });
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(new Date().getTime());
    },
    AquireAddress(data) {
      if (data) {
        //缓存坐标信息
        this.locationInfo = data[0];
        this.customPolygon = this.$refs.baiduMap.getPolygonData(
          this.locationInfo.lng,
          this.locationInfo.lat
        );
      }
    },
    //获取当前位置经纬度
    getlocation() {
      var _this = this;
      if (!_this.isOnline) {
        _this.PowerPoints = [
          {
            latitude: 39.895881243312516,
            longitude: 116.41505864656071
          }
        ];
        _this.locationInfo = {
          lat: 39.895881243312516,
          lng: 116.41505864656071
        };
      } else {
        $.ajax({
          url: "https://apis.map.qq.com/ws/location/v1/ip?&key=F35BZ-7GHK3-O5Q3L-3EAPP-ZDKWQ-C5BWS&output=jsonp",
          type: "get",
          dataType: "jsonp",
          jsonp: "callback",
          hideNotice: true,
          success: function (data) {
            if (data.status === 0) {
              var lat = _this._.get(data, ["result", "location", "lat"], null);
              var lng = _this._.get(data, ["result", "location", "lng"], null);
              _this.PowerPoints = [
                {
                  latitude: lat,
                  longitude: lng
                }
              ];
              _this.locationInfo = {
                lat: lat,
                lng: lng
              };
            }
          }
        });
      }
    }
  },
  created: function () {},
  async mounted() {
    await loadBMap();
    this.getlocation();
  }
};
</script>
<style lang="scss" scoped></style>
<style>
.BMap_cpyCtrl {
  display: none !important;
}
</style>
