<template>
  <ElDrawer
    :title="$T('详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div ref="detailWrap" class="detail-wrap h-full">
      <div class="font-bold text-H3">{{ $T("基本信息") }}</div>
      <el-row :gutter="16">
        <el-col
          :span="8"
          v-for="(item, index) in items"
          :key="index"
          class="mt-J3"
        >
          <ElTooltip placement="top" :content="item.label">
            <div class="detail-label mb-J1 text-ellipsis">{{ item.label }}</div>
          </ElTooltip>
          <ElTooltip
            placement="top"
            :content="findDataByKey(dialogMsg1, item.key)"
          >
            <div class="value text-ellipsis">
              {{ findDataByKey(dialogMsg1, item.key) }}
            </div>
          </ElTooltip>
        </el-col>
        <el-col
          :span="24"
          v-if="dialogMsg1.modelLabel == 'project'"
          class="mt-J3"
        >
          <div class="detail-label mb-J1">{{ $T("项目简介") }}</div>

          <div class="value">
            {{ dialogMsg1.projectabstract || "--" }}
          </div>
        </el-col>
        <el-col :span="24" class="mt-J3">
          <div class="detail-label mb-J1">{{ $T("主图") }}</div>
          <div class="value">
            <UploadImg class="img" :static_in="true" :imgUrl.sync="imgSrc" />
          </div>
        </el-col>
      </el-row>
    </div>
  </ElDrawer>
</template>
<script>
import common from "eem-base/utils/common";
import UploadImg from "@/components/uploadImg.vue";
export default {
  components: {
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      openDrawer: false,
      dialogMsg1: {},
      items: [],
      imgSrc: null
    };
  },
  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      this.show_dialog_out(this.inputData_in);
      this.$nextTick(() => {
        $(this.$refs.detailWrap).scrollTop(0);
      });
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },

  methods: {
    findDataByKey(...arg) {
      return common.findDataByKey(...arg);
    },
    show_dialog_out(val) {
      this.items = [];
      this.imgSrc = null;
      val.commissiondate$text = val.commissiondate
        ? this.$moment(val.commissiondate).format("YYYY-MM-DD")
        : null;
      val.cooperativedeadline$text = val.cooperativedeadline
        ? this.$moment(val.cooperativedeadline).format("YYYY-MM-DD")
        : null;

      this.dialogMsg1 = val;

      this.items = [
        {
          label: $T("名称"),
          key: "name",
          required: true
        },
        {
          label: $T("项目编号"),
          key: "code"
        },
        {
          label: $T("所属区域"),
          key: "hierarchy"
        },
        {
          label: $T("所属公司"),
          key: "enterprisename"
        },
        {
          label: $T("法人代表"),
          key: "cooperaterepresentative"
        },
        {
          label: $T("负责人"),
          key: "director"
        },
        {
          label: $T("负责人电话"),
          key: "directornumber"
        },
        {
          label: $T("负责人邮箱"),
          key: "directoremail"
        },
        {
          label: $T("联系人"),
          key: "contact"
        },
        {
          label: $T("联系人邮箱"),
          key: "contactemail"
        },
        {
          label: $T("联系地址"),
          key: "address"
        },
        {
          label: $T("年产值(万元)"),
          key: "annualoutput"
        },
        {
          label: $T("投运时间"),
          key: "commissiondate$text"
        },
        {
          label: $T("经度"),
          key: "longitude"
        },
        {
          label: $T("纬度"),
          key: "latitude"
        },
        {
          label: $T("面积(m²)"),
          key: "area"
        },
        {
          label: $T("人数(人)"),
          key: "population"
        },
        // {
        //   label: "合作截止时间",
        //   key: "cooperativedeadline$text"
        // }
        {
          label: $T("模型ID"),
          key: "id"
        },
        {
          label: $T("模型名称"),
          key: "modelLabel"
        }
      ];

      this.getImgUrl(val);
    },
    getImgUrl(val) {
      var uploadPath = val.pic || val.picture || "";
      this.imgSrc = this._.cloneDeep(uploadPath);
    }
  }
};
</script>
<style lang="scss" scoped>
.detail-wrap {
  overflow-y: auto;
}
.detail-label {
  color: var(--T3);
  line-height: 1;
}
.value {
  line-height: 1.5;
}
.img {
  height: 80px;
  width: 80px;
}
</style>
