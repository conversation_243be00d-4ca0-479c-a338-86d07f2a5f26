<template>
  <div class="h-full flex flex-col">
    <div
      class="border border-B1 border-solid rounded-Ra flex flex-col flex-auto p-J3"
    >
      <div class="flex flex-row items-center justify-between">
        <div>
          {{ indicatorType_in?.name ?? "--" }}
        </div>

        <el-checkbox v-model="maxAndMin" @change="checkChange">
          {{ $T("最值") }}
        </el-checkbox>
      </div>

      <CetChart
        class="flex-auto"
        ref="cetChart"
        v-bind="CetChart_energyConsume"
      ></CetChart>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
const ENERGY_CONSUME_OPTIONS = {
  tooltip: {
    trigger: "axis"
  },
  title: {
    text: "",
    left: "center"
  },
  legend: {
    top: "10px"
  },
  grid: {
    left: "15px",
    right: "45px",
    top: "45px",
    bottom: "10px",
    containLabel: true
  },
  xAxis: [
    {
      type: "category",
      name: "",
      nameLocation: "end",
      data: [],
      axisPointer: {
        type: "shadow"
      }
    }
  ],
  yAxis: [
    {
      type: "value",
      name: "",
      nameLocation: "end"
    }
  ],
  series: []
};
const MARKLINE = {
  symbol: "none",
  precision: 4,
  data: [
    {
      type: "average",
      label: {
        formatter: $T("能效平均值"),
        color: omegaTheme.theme === "light" ? "#54C081" : "#0D71DA",
        fontSize: 14,
        position: "insideEndTop"
      },
      lineStyle: {
        color: omegaTheme.theme === "light" ? "#54C081" : "#0D71DA",
        width: 2
      }
    }
  ]
};
const MARKPOINT = {
  symbolSize: 60,
  label: {
    show: true,
    fontSize: 10,
    fontWeight: 700,
    formatter: val => {
      return val.value || val.value === 0 ? val.value.toFixed(3) : "--";
    }
  },
  data: [
    {
      type: "max",
      name: $T("最大值"),
      itemStyle: {
        color: omegaTheme.theme === "light" ? "#6ade9a" : "#0c82f8",
        opacity: 1
      }
    },
    {
      type: "min",
      name: $T("最小值"),
      itemStyle: {
        color: omegaTheme.theme === "light" ? "#6ade9a" : "#0c82f8",
        opacity: 1
      }
    }
  ]
};
export default {
  name: "CompareComponent",
  props: {
    indicatorType_in: Object,
    queryTime_in: Object,
    clickNode_in: Object,
    energyType_in: Number,
    update_in: Number,
    dimConfigId_in: Number,
    rootNode_in: Object
  },

  computed: {
    currentStr() {
      const cycle = this.queryTime_in?.cycle;

      const currentStrMap = {
        7: $T("YYYY年MM月DD日HH时"),
        12: $T("YYYY年MM月DD日"),
        13: $T("YYYY年第ww周"),
        14: $T("YYYY年MM月"),
        17: $T("YYYY年")
      };
      return currentStrMap[cycle];
    }
  },
  data() {
    return {
      maxAndMin: true,
      // 单位产量能耗
      CetChart_energyConsume: {
        //组件输入项
        inputData_in: null,
        options: this._.cloneDeep(ENERGY_CONSUME_OPTIONS)
      }
    };
  },
  watch: {
    update_in() {
      this.getChartData();
    }
  },
  methods: {
    async getChartData() {
      if (!this.clickNode_in?.id) {
        return;
      }
      const params = {
        nodes: [
          {
            modelLabel: this.clickNode_in.modelLabel,
            nodes: [
              {
                id: this.clickNode_in.id,
                name: this.clickNode_in.name
              }
            ]
          }
        ],
        startTime: this.queryTime_in.startTime,
        endTime: this.queryTime_in.endTime,
        aggregationCycle: this.queryTime_in.cycle,
        energyTypes: [this.energyType_in],
        unittype: [this.indicatorType_in.unittype],
        energyefficiencysetId: [this.indicatorType_in.id],
        type: true,
        dimConfigId: this.dimConfigId_in,
        rootNode: {
          id: this.rootNode_in.id,
          modelLabel: this.rootNode_in.modelLabel
        }
      };
      const energyefficiencysetId = this.indicatorType_in.id;
      const res = await customApi.queryEnergyEfficiencyData(params);
      if (res.code !== 0 || !res.data[energyefficiencysetId]?.[0]) {
        return;
      }

      // 自然周期和非自然周期返回的开始时间和结束时间
      const data = res.data[energyefficiencysetId][0].data;
      const startTime = data[0].time;
      const endTime = data[data.length - 1].time;

      let series = [];
      if (this.queryTime_in.cycle === 17) {
        series = [
          {
            type: "bar",
            barWidth: 30,
            name: this.getLegendName($T("本周期"), startTime, endTime),
            data: res.data[energyefficiencysetId][0].data,
            markLine: MARKLINE,
            markPoint: MARKPOINT
          }
        ];
      } else if ([12, 7].includes(this.queryTime_in.cycle)) {
        // 小时和日增加同比数据
        series = [
          {
            type: "bar",
            name: this.getLegendName($T("本周期"), startTime, endTime),
            barWidth: this.queryTime_in.cycle === 14 ? "30px" : "",
            data: res.data[energyefficiencysetId][0].data,
            markLine: MARKLINE,
            markPoint: MARKPOINT
          },
          {
            type: "bar",
            name: this.getLegendName($T("上周期"), startTime, endTime),
            barWidth: this.queryTime_in.cycle === 14 ? "30px" : "",
            data: res.data[energyefficiencysetId][0].preData
          },
          {
            type: "bar",
            name: this.getLegendName($T("同比"), startTime, endTime),
            barWidth: this.queryTime_in.cycle === 14 ? "30px" : "",
            data: res.data[energyefficiencysetId][0].tbData ?? []
          }
        ];
      } else {
        series = [
          {
            type: "bar",
            name: this.getLegendName($T("本周期"), startTime, endTime),
            barWidth: this.queryTime.cycle === 14 ? "30px" : "",
            data: res.data[energyefficiencysetId][0].data,
            markLine: MARKLINE,
            markPoint: MARKPOINT
          },
          {
            type: "bar",
            name: this.getLegendName($T("上周期"), startTime, endTime),
            barWidth: this.queryTime.cycle === 14 ? "30px" : "",
            data: res.data[energyefficiencysetId][0].preData
          }
        ];
      }
      this.setChartOptions(series);
    },
    setChartOptions(series) {
      const vm = this;
      const { cycle, unit } = this.queryTime_in;
      const xAxisNameMap = {
        7: $T("小时"),
        12: $T("天数"),
        13: $T("周份"),
        14: $T("月份")
      };
      const xAxisName = xAxisNameMap[cycle] || $T("年份");

      const cycleMap = {
        7: $T("YYYY-MM-DD HH时"),
        12: "YYYY-MM-DD",
        13: $T("YYYY年第ww周"),
        14: "YYYY-MM"
      };
      const formatStr = cycleMap[cycle] || "YYYY";

      this.CetChart_energyConsume.options = {
        toolbox: {
          top: 0,
          right: 10,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        tooltip: {
          trigger: "axis",
          formatter(params) {
            const time = params[0].data.time;
            if (!time) return;

            let str = "";
            params.forEach(item => {
              let str1;
              if (item.componentSubType === "bar" && item.seriesIndex === 0) {
                str1 = vm.formatterDate(time, formatStr);
              } else if (
                cycle !== 17 &&
                item.componentSubType === "bar" &&
                item.seriesIndex === 0 + 1
              ) {
                str1 = vm.formatterDate(
                  vm.$moment(time).subtract(1, unit),
                  formatStr
                );
              } else if (
                [12, 7].includes(cycle) &&
                item.componentSubType === "bar" &&
                item.seriesIndex === 0 + 2
              ) {
                str1 = vm.formatterDate(
                  vm.$moment(time).subtract(1, cycle === 7 ? "M" : "y"),
                  formatStr
                );
              } else {
                str1 = item.seriesName;
              }
              let value = "--";
              if (item.data.value || item.data.value === 0) {
                value = Number(item.data.value).toFixed(
                  item.seriesName === $T("产量") ? 2 : 3
                );
              }
              const symbol = item.data.showDataSymbol
                ? item.data.symbol
                : vm.indicatorType_in.symbol;
              str += `${item.marker} ${str1}: ${common.formatNum(value)}(${
                symbol ? `${symbol}` : "--"
              })<br />`;
            });
            return str;
          }
        },
        title: {
          text: "",
          textStyle: {
            fontSize: 14
          },
          left: "center"
        },
        legend: {
          top: "10px"
        },
        grid: {
          left: "15px",
          right: "45px",
          top: "55px",
          bottom: "10px",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            name: xAxisName,
            nameLocation: "end",
            data: [],
            axisPointer: {
              type: "shadow"
            },
            nameTextStyle: {
              padding: [10, 0, 0, 0],
              verticalAlign: "top"
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            name: this.indicatorType_in?.symbol,
            nameLocation: "end",
            nameTextStyle: {
              align: "left"
            }
          }
        ],
        series
      };
      // 处理x轴显示
      this.CetChart_energyConsume.options.series[0].data.forEach(item => {
        this.CetChart_energyConsume.options.xAxis[0].data.push(
          this.getAxixs(item.time, cycle)
        );
      });
    },
    getLegendName(name, startTime, endTime) {
      const cycle = this.queryTime_in.cycle;
      const unit = this.queryTime_in.unit;
      const unit1 =
        {
          7: "H",
          12: "d",
          13: "w",
          14: "M"
        }[cycle] ?? "";
      let str = "";
      if (name === $T("上周期")) {
        if (cycle === 13) {
          str +=
            this.formatterDate(
              this.$moment(startTime).subtract(1, unit),
              this.currentStr
            ) +
            "-" +
            this.formatterDate(
              this.$moment(endTime).subtract(1, unit1),
              this.currentStr
            );
        } else {
          str +=
            this.formatterDate(
              this.$moment(startTime).subtract(1, unit),
              this.currentStr
            ) +
            "-" +
            this.formatterDate(startTime, this.currentStr);
        }
      } else if (name === $T("同比")) {
        if (cycle === 12) {
          str +=
            this.formatterDate(
              this.$moment(startTime).subtract(1, "year"),
              this.currentStr
            ) +
            "-" +
            this.formatterDate(
              this.$moment(startTime).subtract(1, "year").endOf("M") + 1,
              this.currentStr
            );
        } else if (cycle === 7) {
          str +=
            this.formatterDate(
              this.$moment(startTime).subtract(1, "M"),
              this.currentStr
            ) +
            "-" +
            this.formatterDate(
              this.$moment(startTime).subtract(1, "M").endOf("d") + 1,
              this.currentStr
            );
        }
      } else {
        if (cycle === 13) {
          str +=
            this.formatterDate(startTime, this.currentStr) +
            "-" +
            this.formatterDate(this.$moment(endTime), this.currentStr);
        } else {
          str +=
            this.formatterDate(startTime, this.currentStr) +
            "-" +
            this.formatterDate(
              this.$moment(endTime).add(1, unit1),
              this.currentStr
            );
        }
      }
      return str;
    },
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    getAxixs(date, type) {
      if (type === 7) {
        return this.$moment(date).format("HH");
      } else if (type === 12) {
        return this.$moment(date).format("DD");
      } else if (type === 13) {
        return this.$moment(date).format($T("YYYY年第ww周"));
      } else if (type === 14) {
        return this.$moment(date).format("YYYY/MM");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY");
      }
    },
    checkChange() {
      const series = this.CetChart_energyConsume.options.series;
      for (let i = 0; i < series.length; i++) {
        if (series[i].type === "bar") {
          series[i].markPoint = this.maxAndMin
            ? this.getMarkPoint(series[i].data, 2)
            : null;
          return;
        }
      }
      this.CetChart_energyConsume.options.series = series;
    }
  }
};
</script>
