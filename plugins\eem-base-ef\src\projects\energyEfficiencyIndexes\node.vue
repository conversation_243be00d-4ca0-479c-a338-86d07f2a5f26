<template>
  <div class="h-full flex flex-col">
    <div
      class="border border-B1 border-solid rounded-Ra flex flex-col flex-auto p-J3"
    >
      <div class="flex flex-row items-center justify-between">
        <div>
          {{ indicatorType_in?.name ?? "--" }}
        </div>
        <el-radio-group v-model="chartType" @change="chartTypeChange">
          <el-radio-button :label="1">
            <omega-icon class="cetIcon" symbolId="bar" />
          </el-radio-button>
          <el-radio-button :label="2">
            <omega-icon class="cetIcon" symbolId="line" />
          </el-radio-button>
        </el-radio-group>
      </div>

      <div class="relative flex-auto mt-J3">
        <legendInteraction
          class="legend-model"
          @changeModel="handlerChangeModel"
        />
        <CetChart
          ref="cetChart"
          v-bind="CetChart_energyConsume"
          @legendselectchanged="handlerLegendClick"
        ></CetChart>
      </div>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import legendInteraction from "eem-base/components/legendInteraction.vue";
const ENERGY_CONSUME_OPTIONS = {
  tooltip: {
    trigger: "axis"
  },
  title: {
    text: "",
    left: "center"
  },
  legend: {
    top: "10px"
  },
  grid: {
    left: "15px",
    right: "45px",
    top: "45px",
    bottom: "10px",
    containLabel: true
  },
  xAxis: [
    {
      type: "category",
      name: "",
      nameLocation: "end",
      data: [],
      axisPointer: {
        type: "shadow"
      }
    }
  ],
  yAxis: [
    {
      type: "value",
      name: "",
      nameLocation: "end"
    }
  ],
  series: []
};
export default {
  name: "NodeComponent",
  props: {
    indicatorType_in: Object,
    queryTime_in: Object,
    checkedNodes_in: Array,
    energyType_in: Number,
    update_in: Number,
    dimConfigId_in: Number,
    rootNode_in: Object
  },
  components: {
    legendInteraction
  },

  computed: {},
  data() {
    return {
      chartType: 1,
      currentModel: 1,
      CetChart_energyConsume: {
        //组件输入项
        inputData_in: null,
        options: this._.cloneDeep(ENERGY_CONSUME_OPTIONS)
      }
    };
  },
  watch: {
    update_in() {
      this.getChartData();
    }
  },
  methods: {
    async getChartData() {
      const vm = this;
      if (!this.checkedNodes_in?.length) {
        return;
      }
      let params = {
        nodes: this.checkedNodes_in.map(i => ({
          nodeId: i.id,
          modelLabel: i.modelLabel,
          name: i.name
        })),
        startTime: this.queryTime_in.startTime,
        endTime: this.queryTime_in.endTime,
        aggregationCycle: this.getCyclePreLevel(this.queryTime_in.cycle),
        energyType: this.energyType_in,
        unitType: this.indicatorType_in?.unittype,
        efSetId: this.indicatorType_in?.id,
        unitCycle: this.queryTime_in.cycle,
        rootNode: {
          id: this.rootNode_in.id,
          modelLabel: this.rootNode_in.modelLabel
        }
      };

      const res = await customApi.queryNodeCompare(params);
      if (res.code !== 0) {
        return;
      }

      let legendArr = [];
      const options = {
        toolbox: {
          top: 2,
          right: 0,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            const list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${val[i].name}`;
              }
              const legendName =
                legendArr.find(v => v.name === val[i].seriesName)?.text ??
                val[i].seriesName;
              formatterStr += `<br/>${
                val[i].marker
              }${legendName} : ${common.formatNum(val[i].value)}(${
                vm.indicatorType_in.symbol || "--"
              })`;
            }
            return formatterStr;
          }
        },
        grid: {
          top: "80",
          left: "16",
          right: "16",
          bottom: "40",
          containLabel: true
        },
        legend: {
          type: "scroll",
          data: [],
          width: "70%",
          formatter: name => {
            const text = legendArr.find(v => v.name === name)?.text ?? name;
            return text;
          }
        },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          name: this.indicatorType_in?.symbol,
          nameLocation: "end",
          nameTextStyle: {
            align: "left"
          }
        },
        series: []
      };
      const cycle = this.queryTime_in.cycle;
      const data = res.data || [];

      const legend = [];
      const series = [];
      const xAxisData = [];
      data.forEach((item, index) => {
        const yAxisData = [];
        const itemData1 = item.timeValues || [];
        itemData1.forEach(item1 => {
          const product = this.getAxixs(item1.logTime, cycle);
          yAxisData.push({
            name: product,
            time: item1.logTime,
            value: common.formatNumberWithPrecision(item1.value, 3)
          });
          if (!index) {
            xAxisData.push(product);
          }
        });
        const name = `${item.node.modelLabel}_${item.node.nodeId}`;
        const serie = {
          name: name,
          type: "bar",
          smooth: true,
          data: yAxisData
        };
        legend.push(name);
        series.push(serie);
        legendArr.push({
          name: name,
          text: item.node.name
        });
      });
      options.xAxis.data = xAxisData;
      options.series = series;
      options.legend.data = legend;
      this.CetChart_energyConsume.options = options;
      this.chartTypeChange();
    },
    // 图表类型切换
    chartTypeChange() {
      this.CetChart_energyConsume.options.series.forEach(item => {
        item.type = this.chartType === 1 ? "bar" : "line";
      });
      this.CetChart_energyConsume.options.dataZoom =
        this.chartType === 1
          ? [
              {
                type: "inside",
                startValue: 0,
                endValue: 30,
                zoomOnMouseWheel: false
              },
              {
                startValue: 0,
                endValue: 30,
                brushSelect: false,
                height: 22,
                handleSize: "30"
              }
            ]
          : null;
      this.CetChart_energyConsume.options.grid.bottom =
        this.chartType === 1 ? 40 : 8;
      this.$set(
        this.CetChart_energyConsume.options.yAxis,
        "scale",
        this.chartType !== 1
      );
      this.CetChart_energyConsume.options = this._.cloneDeep(
        this.CetChart_energyConsume.options
      );
    },
    handlerLegendClick(val) {
      if (this.currentModel === 2) {
        this.expandNode(val);
      } else if (this.currentModel === 3) {
        this.cancelSelect(val.name);
      }
    },
    expandNode(val) {
      this.$refs.cetChart.chart.dispatchAction({
        type: "legendSelect",
        name: val.name
      });
      this.$emit("expandNode", val);
    },
    cancelSelect(name) {
      this.$emit("cancelSelect", name);

      const legendList = this.CetChart_energyConsume.options.legend.data.filter(
        item => item !== name
      );
      const seriesList = this.CetChart_energyConsume.options.series.filter(
        item => item.name !== name
      );
      this.CetChart_energyConsume.options.legend.data = legendList;
      this.CetChart_energyConsume.options.series = seriesList;
    },
    handlerChangeModel(val) {
      this.currentModel = val;
      // 切换时将所有隐藏的数据在图表中显示
      this.CetChart_energyConsume.options.legend.data.forEach(name => {
        this.$refs.cetChart.chart.dispatchAction({
          type: "legendSelect",
          name: name
        });
      });
    },
    getCyclePreLevel(iLevel) {
      if (iLevel === 7) {
        return 12;
      } else if (iLevel === 12) {
        return 14;
      } else if ([13, 14, 17].includes(iLevel)) {
        return 17;
      }
      return 0;
    },
    getAxixs(date, type) {
      if (type === 7) {
        return this.$moment(date).format("HH");
      } else if (type === 12) {
        return this.$moment(date).format("DD");
      } else if (type === 13) {
        return this.$moment(date).format($T("YYYY年第ww周"));
      } else if (type === 14) {
        return this.$moment(date).format("YYYY/MM");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY");
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.legend-model {
  position: absolute;
  top: 0;
  right: 40px;
  z-index: 1001;
}
</style>
