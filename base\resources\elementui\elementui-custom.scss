// 适配子应用状态下，element-ui 弹框定位问题
body {
  position: relative !important;
}

.el-popper,
.el-tooltip__popper {
  position: absolute !important;
}

.el-dialog {
  border-radius: 12px;

  .el-dialog__title {
    font-weight: 600;
    line-height: 22px;
    font-size: var(--H3);
  }

  .el-dialog__body {
    background-color: var(--BG);
    padding: 8px;
    line-height: normal;

    .dialog-content {
      border-radius: 8px;
      background-color: var(--BG1);
      padding: var(--J2);
    }
  }

  .el-dialog__header {
    padding: var(--J2) var(--J3);
  }
}

.el-drawer__header {
  span {
    // font-weight: 600;
    line-height: 22px;
    font-size: var(--H3);
    color: var(--T1);
  }

  padding: var(--J2);
  margin-bottom: 0;
  // font-weight: 600;
  // line-height: 22px;
  // font-size: var(--H3);
}

.el-drawer__body {
  background-color: var(--BG);
  padding: 8px;
  line-height: normal;

  .dialog-content {
    border-radius: 8px;
    background-color: var(--BG1);
    padding: var(--J2);
  }
}

// 输入框靠左
.el-input-number .el-input__inner {
  text-align: left !important;
}

// 表单单位
.form-item-unit {
  line-height: 30px;
  position: absolute;
  background-color: var(--BG);
  padding: 0 var(--J1);
  right: 1px;
  text-align: center;
  top: 1px;
  border-radius: 0 var(--Ra) var(--Ra) 0;
}

.flex-auto {
  flex: 1 1 0;
  min-height: 0;
  min-width: 0;
}

// 弹框尺寸
.min > .el-dialog {
  width: 320px !important;
}
.small > .el-dialog {
  width: 600px !important;
}
.medium > .el-dialog {
  width: 960px !important;
}
.max > .el-dialog {
  width: 1440px !important;
}

// 节点树横向滚动
.el-tree {
  & > .el-tree-node {
    display: inline-block;
    min-width: 100%;
  }
}

// 节点树头部筛选去掉上间距
.gianttree .device-search {
  margin-top: 0px !important;
}

// 表格底部间距加靠右对齐
.table-container .el-footer {
  margin-top: var(--J3);
  text-align: right;
}
