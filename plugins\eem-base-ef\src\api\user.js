import fetch from "eem-base/utils/fetch";
import { decryptAttributes } from "eem-base/utils/crypto.js";

function parseAttribute(data) {
  if (typeof data === "string") {
    data = JSON.parse(data);
  }
  let response = _.get(data, "data");
  response = decryptAttributes(response, [
    "name",
    "nicName",
    "email",
    "mobilePhone"
  ]);
  return {
    ...data,
    data: response
  };
}

// 根据用户id查用户信息
export function queryUserInfoById(id) {
  return fetch({
    url: `/eem-service/v2/user/queryUserById`,
    method: "GET",
    transformResponse: [parseAttribute],
    params: {
      id
    }
  });
}
