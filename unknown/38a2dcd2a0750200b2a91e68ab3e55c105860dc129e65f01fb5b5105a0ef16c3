<template>
  <CetDialog class="min" v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
    >
      <el-form-item :label="$T('名称')" prop="deviceName">
        <ElInput
          disabled
          v-model="CetForm_1.data.deviceName"
          v-bind="ElInput_name"
          v-on="ElInput_name.event"
        ></ElInput>
      </el-form-item>
      <el-form-item :label="$T('线损率')" prop="value">
        <ElInputNumber
          v-model="CetForm_1.data.value"
          v-bind="ElInputNumber_1"
          v-on="ElInputNumber_1.event"
        ></ElInputNumber>
        <span class="form-item-unit">%</span>
      </el-form-item>
    </CetForm>
  </CetDialog>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
export default {
  name: "energyLossConfigName",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  data() {
    return {
      CetDialog_1: {
        title: $T("编辑损耗阈值"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          value: [
            {
              required: true,
              trigger: ["blur", "change"],
              validator: (rule, value, callback) => {
                if (value === 0) {
                  callback(new Error($T("线损率不能为0")));
                } else if (!value) {
                  callback(new Error($T("线损率不能为空")));
                } else {
                  callback();
                }
              }
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_name: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        max: 100,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.init();
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      let value = this._.get(this.inputData_in, "value");
      if (value || value === 0) {
        value = Number((value * 100).toFixed2(2));
      } else {
        value = undefined;
      }
      this.CetForm_1.data = {
        deviceName: this.inputData_in?.deviceName,
        value: value
      };
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      let saveData = {
        id: this.inputData_in.id,
        modelLabel: this.inputData_in.modelLabel,
        value: Number((val.value / 100).toFixed2(4))
      };
      customApi.powerManageConfigNode(saveData).then(response => {
        if (response.code === 0) {
          this.$message.success($T("保存成功"));
          this.$emit("save_out");
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    }
  }
};
</script>
