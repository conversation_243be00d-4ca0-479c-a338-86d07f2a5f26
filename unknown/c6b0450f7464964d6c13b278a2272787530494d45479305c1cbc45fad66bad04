<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <div class="flex-col flex">
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item :label="$T('操作人')" prop="nicName">
              <ElInput
                disabled
                v-model="CetForm_1.data.nicName"
                v-bind="ElInput_2"
                v-on="ElInput_2.event"
              ></ElInput>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$T('处理结果')" prop="remark">
          <ElInput
            v-model.trim="CetForm_1.data.remark"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
        </el-form-item>
      </CetForm>
    </div>
  </CetDialog>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
export default {
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  data() {
    return {
      CetDialog_1: {
        title: $T("事件处理"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          remark: [
            {
              required: true,
              message: $T("处理结果不能为空"),
              trigger: ["blur", "change"]
            },
            common.pattern_name,
            common.check_stringLessThan140
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 8,
        type: "textarea",
        // maxlength: 255,
        // showWordLimit: true,
        // required: true,
        placeholder: $T("请填写原因分析、改进措施、处理结果"),
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.init();
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      this.CetForm_1.data = {
        nicName: `${this.userInfo.nicName || ""}(${this.userInfo.name})`
      };
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      let saveData = {
        eventId: this.inputData_in.id,
        remark: val.remark,
        status: 3,
        operatorId: this.userInfo.id,
        operatorName: this.userInfo.nicName
      };
      customApi.powerManageUpdateeventdesc(saveData).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功"));
          this.$emit("save_out");
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    }
  }
};
</script>
