<template>
  <div class="page">
    <div
      style="display: block; overflow-x: hidden; overflow-y: auto"
      :style="{ height: containerHeight, minHeight: '100%' }"
    >
      <CetChart v-bind="CetChart_1" @click="clickChart"></CetChart>
    </div>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
export default {
  props: {
    unit_in: {
      type: String,
      default: () => {
        return "";
      }
    },
    inputData_in: {
      type: Object
    }
  },
  data(vm) {
    return {
      containerHeight: 0,
      CetChart_1: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item",
            position: "bottom",
            confine: true,
            triggerOn: "mousemove",
            formatter: params => {
              if (params.data.source) {
                return `${params.data.sourceLabel} > ${
                  params.data.targetLabel
                }: ${common.formatNum(
                  common.roundNumber(params.data.value, 2, "--")
                )} ${vm.unit_in}`;
              } else {
                let str = `${params.data.label}`;
                const changeNodeData = params.data.changeNodeData;

                str += `<br />${
                  changeNodeData?.length ? $T("总能耗") : $T("能耗值")
                }：${common.formatNum(
                  common.roundNumber(params.data.value, 2, "--")
                )} ${vm.unit_in}`;

                // 展示合并前各个节点的能耗值
                if (changeNodeData?.length) {
                  changeNodeData.forEach(item => {
                    str += `<br />${item.name}：${common.formatNum(
                      common.roundNumber(item.value, 2, "--")
                    )}${this.unit_in}`;
                  });
                }

                str += `<br />${$T("与下级之间损耗量")}：${common.formatNum(
                  common.roundNumber(params.data.loss, 2, "--")
                )} ${vm.unit_in}`;

                str += `<br />${$T(
                  "与下级之间损耗率"
                )}：<span style="color:#F76771!important;">${
                  params.data.lossPercent || params.data.lossPercent === 0
                    ? Number(params.data.lossPercent * 100).toFixed(2) + "%"
                    : "--"
                }</span>`;

                return str;
              }
            }
          },
          series: {
            type: "sankey",
            top: 20,
            layout: "none",
            draggable: false,
            layoutIterations: 0,
            emphasis: {
              focus: "adjacency"
            },
            nodeGap: 14,
            nodeAlign: "right",
            label: {
              formatter: params => {
                const data = params.data || {};
                let label = data.label;
                const maxlength = 10;
                const result = this.isExpand.find(
                  item => item.id === data.name
                );
                let tip = "";
                if (data.expend) {
                  tip = `(${$T("可展开")})`;
                }
                if (result && result.expanded) {
                  tip = "";
                }
                if ((label && label.length) > maxlength) {
                  label = label.substring(0, maxlength - 1) + "...";
                }
                return label + tip;
              }
            },
            lineStyle: {
              opacity: 0.4
            },
            data: [],
            links: []
          }
        }
      },
      isExpand: [],
      initInputData: {}, //初始化数据存放
      handInputData: {}, //过滤使用数据存放
      nameObj: {} //节点名称存储
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    }
  },
  methods: {
    init() {
      this.nameObj = {};
      this.CetChart_1.options.series.data = [];
      this.CetChart_1.options.series.links = [];
      if (
        !this.inputData_in ||
        !this.inputData_in.lossDataVoList ||
        !this.inputData_in.linkNode
      ) {
        return;
      }
      if (Array.isArray(this.inputData_in.lossDataVoList)) {
        this.inputData_in.lossDataVoList.forEach((item, index) => {
          item.iindex = index;
        });
      }
      this.initInputData = this._.cloneDeep(this.inputData_in);
      this.handInputData = this.filterInitData(this.initInputData);
      this.setChartData(this.handInputData);
    },
    setChartData(val) {
      this.isExpand = [];
      if (!val || !val.lossDataVoList) return;
      let depth = 0;
      // 先默认所有节点都可展开
      val.lossDataVoList.forEach(item => {
        item.expend = true;
        if (item.depth > depth) {
          depth = item.depth;
        }
      });
      //如果是最后一层子节点，不需要显示/可展示
      val.lossDataVoList.forEach(item => {
        let expanded = false;
        if (item.depth < depth) {
          expanded = true;
        } else {
          let getIsChild = this.initInputData.linkNode.filter(ii => {
            return item.name === ii.source;
          });
          expanded = getIsChild.length === 0;
        }
        this.isExpand.push({ id: item.name, expanded: expanded });
      });
      const chartData = [];
      // 桑基图data
      val.lossDataVoList.forEach((item, index) => {
        this.nameObj[item.name] = item.deviceName;
        let node = val.linkNode.find(i => i.target === item.name);
        if (node) {
          node.value = item.value || 0;
        }
        chartData.push({
          expend: true,
          iindex: item.iindex,
          depth: item.depth,
          name: item.name,
          value: item.value || 0,
          label: item.deviceName,
          loss: item.loss,
          lossPercent: item.lossPercent,
          itemStyle:
            item.lossPercent && item.lossPercent > item.threshold
              ? {
                  color: "#F65D68"
                }
              : {
                  color: "#3E77FC"
                },
          changeNodeData: item.changeNodeData
        });
      });
      // 桑基图link
      val.linkNode = val.linkNode.map(item => {
        return {
          sourceLabel: this.nameObj[item.source],
          targetLabel: this.nameObj[item.target],
          ...item
        };
      });
      this.calcHeight(chartData);
      this.CetChart_1.options.series.data = chartData;
      this.CetChart_1.options.series.links = val.linkNode;
      //links 里的值如果都是0，则展示 暂无损耗图
      let test = this.CetChart_1.options.series.links.filter(
        item => item.value !== 0
      );
      if (test.length === 0) {
        this.$emit("noSankey", false);
      }
    },
    // 点击节点展开
    clickChart(val) {
      if (val.dataType === "edge") return;

      const vm = this;
      const data = this._.cloneDeep(val.data);
      let index = 0;
      const result = this.isExpand.find((item, idx) => {
        index = idx;
        return item.id === data.name;
      });
      //如果是点击最后一层子节点，直接return退出处理
      let getIsChild = this.initInputData.linkNode.filter(ii => {
        return data.name === ii.source;
      });
      if (getIsChild.length === 0) {
        return;
      }

      //判断是否可展开,点击能源类型时只显示当前的能源流向
      if (result) {
        // 收缩，根节点不能收缩
        if (result.expanded && ![0, 1].includes(data.depth)) {
          this.isExpand[index].expanded = false;
          const nodeData = this._.cloneDeep(
            this.CetChart_1.options.series.data
          );
          const linkData = this._.cloneDeep(
            this.CetChart_1.options.series.links
          );
          this.handleNode(linkData, data, nodeData);
          vm.calcHeight(nodeData);
          this.CetChart_1.options.series.data = nodeData;
          this.CetChart_1.options.series.links = linkData;
          // 展开
        } else if (!result.expanded) {
          this.isExpand[index].expanded = true;
          const childNodeData = this.getChildNodeData(data);

          const chartData = [];
          // 桑基图data
          const lossDataVoList = childNodeData.lossDataVoList.filter(item => {
            return item.name !== data.name;
          });
          const expand = lossDataVoList;
          // 先默认所有节点都可展开
          expand.forEach(item => {
            item.expend = true;
          });
          //如果是最后一层子节点，不需要显示/可展示
          expand.forEach(item => {
            let expanded = false;
            let getIsChild = this.initInputData.linkNode.filter(ii => {
              return item.name === ii.source;
            });
            expanded = getIsChild.length === 0;
            vm.isExpand.push({ id: item.name, expanded: expanded });
          });
          let linkNode = vm._.cloneDeep(childNodeData.linkNode);
          lossDataVoList.forEach((item, index) => {
            this.nameObj[item.name] = item.deviceName;
            let node = linkNode.find(i => i.target === item.name);
            if (node) {
              node.value = item.value || 0;
            }
            chartData.push({
              expend: true,
              iindex: item.iindex,
              depth: item.depth,
              name: item.name,
              value: item.value || 0,
              label: item.deviceName,
              loss: item.loss,
              lossPercent: item.lossPercent,
              itemStyle:
                item.lossPercent && item.lossPercent > item.threshold
                  ? {
                      color: "#F65D68"
                    }
                  : {
                      color: "#3E77FC"
                    },
              changeNodeData: item.changeNodeData
            });
          });
          // 桑基图link
          linkNode = linkNode.map(item => {
            return {
              sourceLabel: this.nameObj[item.source],
              targetLabel: this.nameObj[item.target],
              ...item
            };
          });
          let nodes = vm.CetChart_1.options.series.data.concat(chartData);
          const links = vm.CetChart_1.options.series.links.concat(linkNode);
          nodes = this.sortDatafilter(nodes);
          vm.calcHeight(nodes);
          vm.CetChart_1.options.series.data = nodes;
          vm.CetChart_1.options.series.links = links;
        }
      }
    },
    //点击缩收删除选中节点下子节点
    handleNode(linkData, data, nodeData) {
      const toDelete = [];
      // linkData中找到所有source为当前点击的节点
      const deleteArr = linkData.filter(item => {
        return data && item.source === data.name;
      });
      if (deleteArr.length) {
        // linkData中删除deleteArr数据
        deleteArr.forEach(item => {
          this.deleteFun(linkData, item, toDelete);
        });
      }

      if (toDelete.length) {
        // linkData中被删除的数据继续调用handleNode
        toDelete.forEach(item => {
          const deleteData = nodeData.find(it => {
            return it.name === item.target;
          });
          this.handleNode(linkData, deleteData, nodeData);
        });
      }
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      deleteArr.forEach(item => {
        const idx = nodeData.findIndex(it => {
          return item.target === it.name;
        });
        const it = this.isExpand.findIndex(i => {
          return item.target === i.id;
        });
        if (idx >= 0) {
          nodeData.splice(idx, 1);
        }
        if (it >= 0) {
          this.isExpand.splice(it, 1);
        }
      });
    },
    deleteFun(linkData, item, toDelete) {
      const idx = linkData.findIndex(it => {
        return this._.isEqual(it, item);
      });
      if (idx >= 0) {
        toDelete.push(linkData.splice(idx, 1)[0]);
      }
    },
    calcHeight(data) {
      let height = 0;
      height = data.length * 20;
      this.containerHeight = height + "px";
    },
    //过滤初始化返回数据，只需要显示三层数据结构;depth ===0和depth === 1归类为父节点
    filterInitData(val) {
      let lossDataVoList = val.lossDataVoList || [],
        linkNode = val.linkNode || [],
        toDelete = [];
      let filLossDataVoList = lossDataVoList.filter(item => item.depth <= 3);
      toDelete = lossDataVoList.filter(item => item.depth > 3);
      let filLinkNode = linkNode.filter(item => {
        let isOk = true;
        for (let i = 0, len = toDelete.length; i < len; i++) {
          if (item.target === toDelete[i].name) {
            isOk = false;
            break;
          }
        }
        return isOk;
      });
      return {
        lossDataVoList: filLossDataVoList,
        linkNode: filLinkNode
      };
    },
    //获取点击选择节点下一子层级
    getChildNodeData(data) {
      let lossDataVoList = this.initInputData.lossDataVoList || [],
        linkNode = this.initInputData.linkNode || [];
      // linkData中找到所有source为当前点击的节点
      let childLinkData = linkNode.filter(item => {
        return data && item.source === data.name;
      });
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      let childData = lossDataVoList.filter(item => {
        let isOk = false;
        for (let i = 0, len = childLinkData.length; i < len; i++) {
          if (item.name === childLinkData[i].target) {
            isOk = true;
            break;
          }
        }
        return isOk;
      });
      return {
        lossDataVoList: childData,
        linkNode: childLinkData
      };
    },
    //对桑基图节点列表重新排序顺序
    sortDatafilter(data) {
      data = data || [];
      let obj = {},
        newArr = [];
      data.forEach(item => {
        obj[item.iindex] = item;
      });
      for (let i in obj) {
        newArr.push(obj[i]);
      }
      return newArr;
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 500px;
  position: relative;
  overflow: auto;
}
</style>
