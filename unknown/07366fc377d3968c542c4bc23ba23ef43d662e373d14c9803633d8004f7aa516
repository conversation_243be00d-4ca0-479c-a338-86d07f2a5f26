<template>
  <div class="fullfilled detail flex-col flex">
    <div class="Toback handel">
      <i class="el-icon-arrow-left icon" @click="back"></i>
    </div>
    <div class="clearfix searchBox mb-J3">
      <CetButton
        class="fl mr-J3 backBtn"
        v-bind="CetButton_back"
        v-on="CetButton_back.event"
      ></CetButton>
      <ElInput
        class="fl mr-J3"
        v-model.trim="ElInput_search.value"
        v-bind="ElInput_search"
        v-on="ElInput_search.event"
      >
        <i slot="suffix" class="el-input__icon el-icon-search"></i>
      </ElInput>
      <customElSelect
        class="fl mr-J3"
        v-model="ElSelect_energytype.value"
        v-bind="ElSelect_energytype"
        v-on="ElSelect_energytype.event"
        :prefix_in="$T('能源类型')"
      >
        <ElOption
          v-for="item in ElOption_energytype.options_in"
          :key="item[ElOption_energytype.key]"
          :label="item[ElOption_energytype.label]"
          :value="item[ElOption_energytype.value]"
          :disabled="item[ElOption_energytype.disabled]"
        ></ElOption>
      </customElSelect>
      <customElSelect
        class="fl mr-J3"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
        :prefix_in="$T('监测对象')"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>

      <el-radio-group
        class="fl mr-J3"
        v-model="ElSelect_3.value"
        v-on="ElSelect_3.event"
      >
        <el-radio-button
          v-for="item in ElOption_3.options_in"
          :label="item[ElOption_3.value]"
          :key="item[ElOption_3.value]"
        >
          {{ item[ElOption_3.label] }}
        </el-radio-button>
      </el-radio-group>
      <div class="clearfix fl">
        <CetButton
          class="fl"
          v-bind="CetButton_prv"
          v-on="CetButton_prv.event"
        ></CetButton>
        <CustomElDatePicker
          class="fl ml-J0 datePicker"
          :prefix_in="cycleName[ElSelect_3.value]"
          v-bind="CetDatePicker_time.config"
          v-model="CetDatePicker_time.val"
          @change="getData"
        />
        <CetButton
          class="fl ml-J0"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
      </div>
    </div>
    <DataCarousel v-bind="dataCarousel" :symbol_in="symbol" />
    <div class="p-J4 rounded-Ra mt-J3 bg-BG1 pt-0">
      <div class="dataCardWrap" :class="{ retract: retractCard }">
        <div class="dataCardBox">
          <div class="clearfix dataCard" ref="dataCard">
            <div
              class="flex-row flex p-J3 mr-J3 fl mt-J3 rounded-Ra"
              v-for="(item, index) in statisticsNodes"
              :key="index"
            >
              <div class="label">
                {{ $T("{0}级损耗异常数量(个)", item.depth + 1) }}
              </div>
              <el-tooltip effect="light" :content="formatCount(item.count)">
                <div class="flex-auto value text-ellipsis ml-J1">
                  {{ formatCount(item.count) }}
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div
          v-if="showRetractCard"
          class="text-center handle fullwidth text-ZS mt-J3"
          @click="retractCard = !retractCard"
        >
          {{ retractCard ? $T("展开") : $T("收起") }}
          <i class="el-icon-d-arrow-left handleIcon"></i>
        </div>
      </div>
    </div>
    <div class="flex-auto mt-J3 flex-col flex bg-BG1 p-J4 rounded-Ra">
      <div class="clearfix tableTitle">
        <!-- <div class="fl">
          共
          <span class="text-ZS">{{ totalCount }}</span>
          条
        </div> -->
        <CetButton
          class="fr"
          :disable_in="totalCount ? false : true"
          v-bind="CetButton_exprot"
          v-on="CetButton_exprot.event"
        ></CetButton>
      </div>
      <CetTable
        class="flex-auto mt-J3 CetTable"
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
      >
        <template v-for="item in Columns_1">
          <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
        </template>
        <ElTableColumn v-bind="ElTableColumn_subordinate">
          <template slot-scope="scope">
            <span class="text-ZS handle" @click="toSubordinate(scope)">
              {{
                common.formatNumberWithPrecision(
                  scope.row[ElTableColumn_subordinate.prop],
                  0
                )
              }}
            </span>
          </template>
        </ElTableColumn>
        <template v-for="item in Columns_2">
          <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
        </template>
        <ElTableColumn v-bind="ElTableColumn_status">
          <template slot-scope="scope">
            <span
              :class="
                formatStatus(
                  scope.row,
                  ElTableColumn_status,
                  scope.row[ElTableColumn_status.prop]
                ).styleClass
              "
            >
              {{
                formatStatus(
                  scope.row,
                  ElTableColumn_status,
                  scope.row[ElTableColumn_status.prop]
                ).text
              }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          :label="$T('操作')"
          width="100"
          header-align="left"
          align="left"
          fixed="right"
        >
          <template slot-scope="scope">
            <span class="handle text-ZS" @click.stop="toDetail(scope)">
              {{ $T("详情") }}
            </span>
          </template>
        </ElTableColumn>
      </CetTable>
      <div class="text-right mt-J2 mr-J2">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentPageChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :total="totalCount"
          layout="total,sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </div>
    <Detail v-bind="detail" />
    <Subordinate v-bind="subordinate" :symbol_in="symbol" />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import { CustomElDatePicker } from "eem-base/components";
import DataCarousel from "./dataCarousel.vue";
import common from "eem-base/utils/common";
import Detail from "./dialog/detail.vue";
import Subordinate from "./dialog/subordinate.vue";
import omegaI18n from "@omega/i18n";
const language = omegaI18n.locale === "en";

export default {
  name: "energyLossOverviewDetail",
  components: {
    CustomElDatePicker,
    DataCarousel,
    Detail,
    Subordinate
  },
  props: {
    initTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    searchData_in: {
      type: Object
    },
    energytypePptions_in: {
      type: Array
    },
    objectOptions_in: {
      type: Array
    },
    date_in: {
      type: Object
    },
    energytype_in: {
      type: Number
    }
  },
  data(vm) {
    return {
      showRetractCard: false,
      retractCard: true,
      symbol: "--",
      statisticsNodes: [],
      common: common,
      CetButton_back: {
        visible_in: true,
        disable_in: false,
        title: $T("返回"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.back
        }
      },
      ElInput_search: {
        value: "",
        placeholder: $T("请输入设备名称"),
        style: {
          width: "300px"
        },
        event: {
          change: this.searchChange
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: "240px"
        },
        event: {
          change: this.getData
        }
      },
      ElOption_1: {
        options_in: [],
        key: "tree_id",
        value: "tree_id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_energytype: {
        value: null,
        disabled: true, // 此处切换能源类型时获取的监测对象有误，所以暂禁止切换能源类型
        style: {
          width: "240px"
        },
        event: {
          change: this.energytypeChange
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 12,
        style: {
          width: "180px"
        },
        event: {
          change: this.ElSelect_3_change
        }
      },
      cycleName: {
        12: $T("选择日期"),
        14: $T("选择月份"),
        17: $T("选择年份")
      },
      cycle: {
        12: "d",
        14: "month",
        17: "year"
      },
      ElOption_3: {
        options_in: [
          { id: 12, text: $T("日") },
          { id: 14, text: $T("月") },
          { id: 17, text: $T("年") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_time: {
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          clearable: false
        }
      },
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_exprot: {
        visible_in: true,
        // disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_exprot_statusTrigger_out
        }
      },
      pageSize: 10,
      currentPage: 1,
      totalCount: 0,
      fliterNameTableData: [],
      originalTableData: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "70" //绝对宽度
        },
        {
          prop: "depth", // 支持path a[0].b
          label: $T("损耗级别"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            return $T("{0}级损耗", cellValue + 1);
          }
        },
        {
          prop: "roomName", // 支持path a[0].b
          label: $T("房间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "180", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "180", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "value", // 支持path a[0].b
          label: $T("设备能耗") + "(--)", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "300" : "150", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(2)
        }
      ],
      ElTableColumn_subordinate: {
        prop: "endLoopCount", // 支持path a[0].b
        label: $T("下级设备数量(个)"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "350" : "150", //该宽度会自适应
        formatter: this.formatNumberColWithPercision(0)
      },
      Columns_2: [
        {
          prop: "endDevInfos", // 支持path a[0].b
          label: $T("下级设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "300" : "200", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (cellValue && cellValue.length) {
              if (cellValue.length === 1) {
                return cellValue[0].endDevName;
              }
              return `${cellValue[0].endDevName} ${$T("等")}`;
            }
            return "--";
          }
        },
        {
          prop: "endLoopValue", // 支持path a[0].b
          label: $T("下级设备能耗之和") + "(--)", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "460" : "190", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(2)
        },
        {
          prop: "loss", // 支持path a[0].b
          label: $T("损耗量") + "(--)", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(2)
        },
        {
          prop: "lossRate", // 支持path a[0].b
          label: $T("损耗率") + "(%)", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: language ? "150" : "100", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (cellValue || cellValue === 0) {
              return `${common.formatNum((cellValue * 100).toFixed2(2))}`;
            }
            return "--";
          }
        }
      ],
      ElTableColumn_status: {
        prop: "evaluate$text", // 支持path a[0].b
        label: $T("损耗评估"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "150" : "90" //该宽度会自适应
      },
      detail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        energytype_in: null,
        query_in: null
      },
      subordinate: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      dataCarousel: {
        initTrigger_in: new Date().getTime(),
        inputData_in: []
      }
    };
  },
  watch: {
    initTrigger_in() {
      this.init();
    },
    "ElSelect_energytype.value": {
      handler: function (val) {
        let obj = this.ElOption_energytype.options_in.find(
          item => item.energytype === val
        );
        this.symbol = obj?.symbol || "--";
        this.Columns_1.find(i => i.prop === "value").label =
          $T("设备能耗") + `(${this.symbol})`;
        this.Columns_2.find(i => i.prop === "endLoopValue").label =
          $T("下级设备能耗之和") + `(${this.symbol})`;

        this.Columns_2.find(i => i.prop === "loss").label =
          $T("损耗量") + `(${this.symbol})`;
      },
      immediate: true
    },
    statisticsNodes: {
      handler: function () {
        this.checkHeight();
      },
      deep: true
    }
  },
  methods: {
    init() {
      this.retractCard = true;
      this.pageSize = 10;
      this.currentPage = 1;
      this.totalCount = 0;
      this.fliterNameTableData = [];
      this.originalTableData = [];
      this.ElInput_search.value = "";
      this.ElSelect_3.value = this._.get(this.date_in, "timeType") || 12;
      this.CetDatePicker_time.val =
        this._.get(this.date_in, "time") ||
        this.$moment().add(0, "d").valueOf();
      let cycle = {
        12: "date",
        14: "month",
        17: "year"
      };
      this.CetDatePicker_time.config.type = cycle[this.ElSelect_3.value];
      this.ElOption_1.options_in = this.objectOptions_in || [];
      let id = this._.get(this.inputData_in, "id"),
        modelLabel = this._.get(this.inputData_in, "modelLabel");
      this.ElSelect_1.value = `${modelLabel}_${id}`;

      this.ElOption_energytype.options_in = this.energytypePptions_in || [];
      this.ElSelect_energytype.value =
        this.energytype_in ||
        this._.get(this.energytypePptions_in, "[0].energytype");
      this.dataCarousel.inputData_in = [];
      this.getData();
      this.dataCarousel.initTrigger_in = new Date().getTime();
    },
    back() {
      this.$emit("back_out");
    },
    energytypeChange() {
      this.queryEnergyLossStartNodes();
    },
    ElSelect_3_change() {
      let cycle = {
        12: "date",
        14: "month",
        17: "year"
      };
      this.CetDatePicker_time.config.type = cycle[this.ElSelect_3.value];
      this.getData();
    },
    CetButton_prv_statusTrigger_out() {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date
        .subtract(1, this.cycle[this.ElSelect_3.value])
        .valueOf();
      this.getData();
    },
    CetButton_next_statusTrigger_out() {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date
        .add(1, this.cycle[this.ElSelect_3.value])
        .valueOf();
      this.getData();
    },
    // 更新监测对象
    queryEnergyLossStartNodes() {
      let energyType = this.ElSelect_energytype.value;
      if (!energyType) {
        return;
      }
      const params = {
        energyType: energyType
      };
      customApi.queryEnergyLossStartNodes(params).then(response => {
        if (response.code === 0) {
          let options = this._.get(response, "data", []) || [];
          let objectOptions = options.map(item => {
            return {
              name: item.name,
              id: item.id,
              modelLabel: item.modelLabel,
              tree_id: `${item.modelLabel}_${item.id}`
            };
          });
          this.ElOption_1.options_in = objectOptions;

          let id = this._.get(objectOptions, "[0].id"),
            modelLabel = this._.get(objectOptions, "[0].modelLabel");
          if (id) {
            this.ElSelect_1.value = `${modelLabel}_${id}`;
          } else {
            this.ElSelect_1.value = null;
          }
          this.getData();
        }
      });
    },
    getData() {
      const vm = this;
      if (!vm.ElSelect_1.value || !vm.ElSelect_energytype.value) {
        vm.CetTable_1.data = [];
        vm.totalCount = 0;
        vm.statisticsNodes = [];
        vm.dataCarousel.inputData_in = [];
        vm.dataCarousel.initTrigger_in = new Date().getTime();
        return;
      }
      let id = Number(vm.ElSelect_1.value.split("_")[1]),
        modelLabel = vm.ElSelect_1.value.split("_")[0];
      let date = vm.$moment(vm.CetDatePicker_time.val);
      let queryData = {
        modelLabel: modelLabel,
        id: id,
        energyType: vm.ElSelect_energytype.value,
        startTime: date.startOf(vm.cycle[vm.ElSelect_3.value]).valueOf(),
        endTime: date.endOf(vm.cycle[vm.ElSelect_3.value]).valueOf() + 1,
        aggregationCycle: vm.ElSelect_3.value
      };
      customApi
        .lossConfigDeviceLossAnalysis(queryData, { keepTransformer: false })
        .then(res => {
          if (res.code === 0) {
            let tableData = vm._.get(res, "data.nodeList", []) || [];
            tableData.forEach(item => {
              item.evaluate$text = item.evaluate
                ? item.lossRate
                  ? this.$T("正常")
                  : "--"
                : this.$T("异常");
            });
            vm.fliterNameTableData = vm._.cloneDeep(tableData);
            vm.originalTableData = vm._.cloneDeep(tableData);
            vm.totalCount = tableData.length;
            vm.searchChange(vm.ElInput_search.value);
            let statisticsNodes =
              vm._.get(res, "data.statisticsNodes", []) || [];
            vm.statisticsNodes = statisticsNodes;
            let measureNodes = vm._.get(res, "data.measureNodes", []) || [];
            vm.powerManageBetBill(measureNodes);
          }
        });
    },
    // 获取供电局账单
    async powerManageBetBill(measureNodes) {
      const vm = this;
      let date = vm.$moment(vm.CetDatePicker_time.val),
        id = Number(vm.ElSelect_1.value.split("_")[1]),
        modelLabel = vm.ElSelect_1.value.split("_")[0];
      let queryData = {
        aggregationCycle: vm.ElSelect_3.value,
        startTime: date.startOf(vm.cycle[vm.ElSelect_3.value]).valueOf(),
        endTime: date.endOf(vm.cycle[vm.ElSelect_3.value]).valueOf() + 1,
        energyType: vm.ElSelect_energytype.value,
        objectId: id,
        objectLabel: modelLabel
      };
      const res = await customApi.powerManageQuerylinebill(queryData);
      if (res.code !== 0) return;

      const data = res.data || [];
      let usage = vm._.get(res, "data[0].usage");
      if (usage != null) {
        measureNodes.unshift({
          depth: -1,
          measure: usage,
          multiple: data?.length > 1,
          multipleData: data
        });
      }
      vm.dataCarousel.inputData_in = measureNodes;
      vm.dataCarousel.initTrigger_in = new Date().getTime();
    },
    // 关键字过滤
    searchChange(val) {
      let tableData = this.fliterNameTableData.filter(
        item => item.name.toUpperCase().indexOf(val.toUpperCase()) !== -1
      );
      this.originalTableData = this._.cloneDeep(tableData);
      this.totalCount = tableData.length;
      this.handleCurrentPageChange(1);
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentPageChange(1);
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.CetTable_1.data = this._.cloneDeep(
        this.originalTableData.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
      );
    },
    CetButton_exprot_statusTrigger_out() {
      let tableData = this._.cloneDeep(this.CetTable_1.data);
      this.CetTable_1.data = this._.cloneDeep(this.originalTableData);
      this.$nextTick(() => {
        let obj = this.ElOption_1.options_in.find(
          i => i.tree_id === this.ElSelect_1.value
        );
        this.CetTable_1.exportFileName = `${obj.name} ${$T("损耗详情")}`;
        this.CetTable_1.exportTrigger_in = new Date().getTime();
        this.CetTable_1.data = this._.cloneDeep(tableData);
      });
    },
    toDetail(scope) {
      let energytype = this.ElOption_energytype.options_in.find(
        i => i.energytype === this.ElSelect_energytype.value
      );
      this.detail.energytype_in = energytype;
      let date = this.$moment(this.CetDatePicker_time.val);
      this.detail.query_in = {
        startTime: date.startOf(this.cycle[this.ElSelect_3.value]).valueOf(),
        endTime: date.endOf(this.cycle[this.ElSelect_3.value]).valueOf() + 1,
        aggregationCycle: this.ElSelect_3.value
      };
      this.detail.inputData_in = this._.cloneDeep(scope.row);
      this.detail.visibleTrigger_in = new Date().getTime();
    },
    toSubordinate(scope) {
      this.subordinate.inputData_in = this._.cloneDeep(scope.row);
      this.subordinate.visibleTrigger_in = new Date().getTime();
    },
    formatCount(val) {
      if (val || val === 0) {
        return common.formatNum(val.toFixed(0));
      }
      return "--";
    },
    formatNumberColWithPercision(precision) {
      return function (row, column, cellValue) {
        return common.formatNum(
          common.formatNumberWithPrecision(cellValue, precision)
        );
      };
    },
    checkHeight() {
      this.$nextTick(() => {
        this.showRetractCard = this.$refs.dataCard.clientHeight > 104;
      });
    },
    formatStatus(row) {
      let styleClass = "",
        text = "",
        cellValue = row.evaluate;
      if (cellValue) {
        text = $T("正常");
        styleClass = "text-Sta1";
        if (!row.lossRate) {
          text = "--";
          styleClass = "text-Sta3";
        }
      } else {
        text = $T("异常");
        styleClass = "text-Sta3";
      }
      return {
        styleClass,
        text
      };
    }
  },

  mounted() {
    window.onresize = () => {
      this.checkHeight();
    };
  }
};
</script>

<style lang="scss" scoped>
.detail {
  .Toback {
    height: 14px;
    position: fixed;
    top: 60px;
    left: 262px;
    z-index: 1;
    @include background_color(BG1);
    display: none;
    .icon {
      @include font_size("Aa");
      color: #9197a5;
      line-height: 14px;
      cursor: pointer;
    }
  }
}
.frame-vertical-collapse .Toback {
  left: 104px;
}
.VLayoutSZW .Toback {
  display: block;
}
.VLayoutSZW .backBtn {
  display: none;
}

.dataCardWrap {
  .dataCard {
    & > div {
      width: 218px;
      @include background_color(BG, !important);
      .label {
        @include font_color(T3);
        @include font_size(Ab);
        @include line_height(H);
      }
      .value {
        font-weight: bold;
        @include font_size(H);
        @include line_height(H);
      }
    }
  }
  .handleIcon {
    transform: rotate(90deg);
  }
  &.retract {
    .dataCardBox {
      box-sizing: border-box;
      height: 104px;
      overflow: hidden;
    }
    .handleIcon {
      transform: rotate(-90deg);
    }
  }
}
.CetTable {
  :deep(.el-footer) {
    text-align: right;
  }
}
.tableTitle {
  @include line_height(Hm);
}
.handle {
  cursor: pointer;
}
.datePicker :deep(.el-date-editor.el-input) {
  width: 220px !important;
}
</style>
