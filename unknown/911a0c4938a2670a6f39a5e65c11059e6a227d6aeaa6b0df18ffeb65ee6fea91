<template>
  <div class="fullfilled flex-col flex diagram">
    <div>
      <customElSelect
        class="fl"
        v-model="ElSelect_type.value"
        v-bind="ElSelect_type"
        v-on="ElSelect_type.event"
        :prefix_in="$T('展示方式')"
      >
        <ElOption
          v-for="item in ElOption_type.options_in"
          :key="item[ElOption_type.key]"
          :label="item[ElOption_type.label]"
          :value="item[ElOption_type.value]"
          :disabled="item[ElOption_type.disabled]"
        ></ElOption>
      </customElSelect>
    </div>
    <div class="flex-auto mt-J3">
      <LossAnalysis
        :unit_in="unit_in"
        v-if="showLossAnalysis && ElSelect_type.value === 2"
        v-bind="LossAnalysisConfig"
      />
      <Sankey
        :unit_in="unit_in"
        v-if="showLossAnalysis && ElSelect_type.value === 1 && isShowImage"
        v-bind="Sankey"
        @noSankey="changeShowLossAnalysis"
      />
      <div
        v-if="!showLossAnalysis || (!isShowImage && ElSelect_type.value === 1)"
        class="fullfilled"
      >
        <div class="noTopology">
          <div class="fl text">
            <div>{{ $T("无损耗数据不展示能流图") }}</div>
          </div>
          <img class="fr img" :src="emptyImg" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import LossAnalysis from "./lossAnalysis";
import Sankey from "./sankey";
import omegaTheme from "@omega/theme";

export default {
  name: "energyLossOverviewDetail",
  components: {
    LossAnalysis,
    Sankey
  },
  props: {
    initTrigger_in: {
      type: Number
    },
    unit_in: {
      type: String,
      default: () => {
        return "";
      }
    },
    query_in: {
      type: Object
    }
  },
  computed: {
    emptyImg() {
      return omegaTheme.theme === "light"
        ? require("../../energyLossManagement/assets/empty_light.png")
        : require("../../energyLossManagement/assets/empty_dark.png");
    }
  },
  data() {
    return {
      showLossAnalysis: false,
      isShowImage: true,
      ElSelect_type: {
        value: 2,
        style: {
          width: "300px"
        },
        size: "small",
        event: {
          // change: this.ElSelect_type_change
        }
      },
      ElOption_type: {
        options_in: [
          {
            id: 2,
            name: $T("拓扑图")
          },
          {
            id: 1,
            name: $T("能流图")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      LossAnalysisConfig: {
        inputData_in: null
      },
      Sankey: {
        inputData_in: null
      }
    };
  },
  watch: {
    initTrigger_in() {
      this.init();
    }
  },
  methods: {
    init() {
      this.ElSelect_type.value = 2;
      this.isShowImage = true;
      this.getEnergyLoss();
    },
    // 损耗查询
    getEnergyLoss() {
      if (!this.query_in) {
        console.error("拓扑图入参不全");
        return;
      }
      const params = {
        ...this.query_in
      };
      customApi.getLossAnalysisDepth(params).then(res => {
        if (res.code === 0) {
          this.showLossAnalysis = Boolean(
            res.data.linkNode?.length && res.data.lossDataVoList?.length
          );
          this.LossAnalysisConfig.inputData_in =
            this._.cloneDeep(res.data) || {};
          this.Sankey.inputData_in = this._.cloneDeep(res.data) || {};
        } else {
          this.showLossAnalysis = false;
        }
      });
    },
    changeShowLossAnalysis(val) {
      this.isShowImage = val;
      // this.showLossAnalysis = val;
    }
  }
};
</script>
<style lang="scss" scoped>
.diagram {
  box-sizing: border-box;
  min-height: 580px;
  position: relative;
}
.noTopology {
  width: 725px;
  height: 315px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  .img {
    height: 315px;
  }
  .text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    & > div:nth-child(1) {
      @include font_color(ZS);
      @include font_size(H);
      font-weight: bold;
    }
  }
}
</style>
