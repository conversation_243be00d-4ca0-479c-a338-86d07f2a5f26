<template>
  <div class="h-full relative">
    <CetAside class="cet-aside h-full">
      <template #aside>
        <div class="fullheight flex-col flex">
          <customElSelect
            class="mb-J3"
            v-model="ElSelect_energytype.value"
            v-bind="ElSelect_energytype"
            v-on="ElSelect_energytype.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_energytype.options_in"
              :key="item[ElOption_energytype.key]"
              :label="item[ElOption_energytype.label]"
              :value="item[ElOption_energytype.value]"
              :disabled="item[ElOption_energytype.disabled]"
            ></ElOption>
          </customElSelect>
          <CetGiantTree
            class="flex-auto"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </div>
      </template>
      <template #container>
        <div class="flex-auto h-full flex-col flex">
          <div class="text-ellipsis mb-J3">
            <el-tooltip
              effect="light"
              :content="currentNode ? currentNode.name : '--'"
              placement="top-start"
            >
              <span class="text-H2">
                {{ currentNode ? currentNode.name : "--" }}
              </span>
            </el-tooltip>
          </div>
          <div class="flex-row flex items-center">
            <div class="flex-auto flex-row flex items-center">
              <customElSelect
                class="mr-J3"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
                :prefix_in="$T('损耗级别')"
                @change="getTableData"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </customElSelect>
              <customElSelect
                class="mr-J3"
                v-model="ElSelect_2.value"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
                :prefix_in="$T('报警类型')"
                @change="getTableData"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </customElSelect>
              <CetButton
                v-bind="CetButton_prv"
                v-on="CetButton_prv.event"
              ></CetButton>
              <CustomElDatePicker
                class="ml-J0 datePicker"
                :prefix_in="$T('选择时段')"
                v-bind="CetDatePicker_time.config"
                v-model="CetDatePicker_time.val"
                @change="getTableData"
                @blur="datePickerBlur"
                :picker-options="pickerOptions"
              />
              <CetButton
                class="ml-J0"
                v-bind="CetButton_next"
                v-on="CetButton_next.event"
              ></CetButton>
              <customElSelect
                class="ml-J3"
                v-model="ElSelect_4.value"
                v-bind="ElSelect_4"
                v-on="ElSelect_4.event"
                :prefix_in="$T('处理状态')"
                @change="getTableData"
              >
                <ElOption
                  v-for="item in ElOption_4.options_in"
                  :key="item[ElOption_4.key]"
                  :label="item[ElOption_4.label]"
                  :value="item[ElOption_4.value]"
                  :disabled="item[ElOption_4.disabled]"
                ></ElOption>
              </customElSelect>
            </div>
            <CetButton
              class="ml-J1"
              :disable_in="CetTable_1.totalCount ? false : true"
              v-bind="CetButton_exprot"
              v-on="CetButton_exprot.event"
            ></CetButton>
          </div>
          <div class="flex-auto tableBox mt-J3">
            <CetTable
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <template v-for="item in Columns_1">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
              <ElTableColumn v-bind="ElTableColumn_status">
                <template slot-scope="scope">
                  <span
                    :class="
                      scope.row[ElTableColumn_status.prop] === 3
                        ? 'text-Sta1'
                        : 'text-Sta2'
                    "
                  >
                    {{
                      scope.row[ElTableColumn_status.prop] === 3
                        ? $T("已处理")
                        : $T("待处理")
                    }}
                  </span>
                </template>
              </ElTableColumn>
              <ElTableColumn
                :label="$T('操作')"
                width="130"
                header-align="left"
                align="left"
                fixed="right"
              >
                <template slot-scope="scope">
                  <span class="handle" @click.stop="toDetail(scope)">
                    {{ $T("详情") }}
                  </span>
                  <span
                    class="handle ml-J1"
                    v-if="scope.row[ElTableColumn_status.prop] !== 3"
                    @click.stop="handle(scope)"
                  >
                    {{ $T("处理") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
        </div>
        <EventHandle v-bind="eventHandle" @save_out="getTableData" />
      </template>
    </CetAside>

    <Detail
      v-bind="detail"
      @back="detail.visible = fasle"
      class="h-full absolute top-0 left-0 right-0 bottom-0 bg-BG1 rounded-Ra z-10 p-J4 box-border"
      v-if="detail.visible"
    />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import { CustomElDatePicker } from "eem-base/components";
import Detail from "./dialogs/detail.vue";
import EventHandle from "./dialogs/eventHandle.vue";
import omegaI18n from "@omega/i18n";

export default {
  name: "energyLossWarning",
  components: {
    CustomElDatePicker,
    Detail,
    EventHandle
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    alarmType() {
      const list = this.$store.state.systemCfg?.alarmAggregationType;
      const defaultAlarmType = [
        { id: 7, text: $T("小时") },
        { id: 12, text: $T("日") }
      ];

      if (!list?.length) {
        return defaultAlarmType;
      }

      return defaultAlarmType.filter(({ id }) => list.includes(id));
    },
    standardEnergyType() {
      return this.$store.state.standardEnergyType;
    }
  },
  data(vm) {
    const language = omegaI18n.locale === "en";
    return {
      datePickerStart: 0,
      datePickerEnd: 0,
      pickerOptions: {
        disabledDate(time) {
          if (vm.datePickerEnd && vm.datePickerStart) {
            return (
              time.getTime() > vm.datePickerEnd ||
              time.getTime() < vm.datePickerStart
            );
          }
        },
        onPick({ maxDate, minDate }) {
          if (!maxDate && minDate) {
            // 第一次选择前后取3个月
            let startTime = vm.$moment(minDate).add(-3, "month").valueOf(),
              endTime = vm.$moment(minDate).add(3, "month").valueOf();

            vm.datePickerEnd = endTime;
            vm.datePickerStart = startTime;
          } else if (maxDate && minDate) {
            vm.datePickerEnd = 0;
            vm.datePickerStart = 0;
          }
        },
        shortcuts: [
          {
            text: $T("今天"),
            onClick(picker) {
              const end = vm.$moment().endOf("d").valueOf() + 1;
              const start = vm.$moment().startOf("d").valueOf();
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: $T("昨天"),
            onClick(picker) {
              const end = vm.$moment().startOf("d").valueOf();
              const start = vm.$moment().startOf("d").add(-1, "d").valueOf();
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: $T("最近3天"),
            onClick(picker) {
              const end = vm.$moment().startOf("d").add(1, "d").valueOf();
              const start = vm.$moment().startOf("d").add(-2, "d").valueOf();
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: $T("最近一周"),
            onClick(picker) {
              const end = vm.$moment().startOf("d").add(1, "d").valueOf();
              const start = vm.$moment().startOf("d").add(-6, "d").valueOf();
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: $T("最近一个月"),
            onClick(picker) {
              const end = vm.$moment().startOf("d").add(1, "d").valueOf();
              const start = vm.$moment().startOf("d").add(-1, "M").valueOf();
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      lossLevel: [],
      currentNode: null,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      ElSelect_energytype: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_energytype_change
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_1: {
        value: null,
        style: {
          width: language ? "240px" : "200px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "level",
        value: "level",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 7,
        style: {
          width: "160px"
        },
        event: {}
      },
      ElOption_2: {
        // 改写为支持默认值
        options_in: [
          // { id: 7, text: $T("小时") },
          // { id: 12, text: $T("日") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_4: {
        value: 1,
        style: {
          width: language ? "240px" : "160px"
        },
        event: {}
      },
      ElOption_4: {
        options_in: [
          { id: 1, text: $T("待处理") },
          { id: 3, text: $T("已处理") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false
        }
      },
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_exprot: {
        visible_in: true,
        // disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_exprot_statusTrigger_out
        }
      },
      CetTable_1: {
        totalCount: 0,
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "lossConfigSystemevents",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [{ name: "queryData", operator: "LIKE", prop: "queryData" }], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          queryData: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20,
          layout: "total, sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          totalNum_out: this.CetTable_1_totalCount_out
        }
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          headerAlign: "left",
          align: "left",
          fixed: "left",
          showOverflowTooltip: true,
          width: "70" //绝对宽度
        },
        {
          prop: "logTime", // 支持path a[0].b
          label: $T("时间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "180", //该宽度会自适应
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          prop: "str", // 支持path a[0].b
          label: $T("能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: () => {
            let obj = vm.ElOption_energytype.options_in.find(
              i => i.energytype === vm.ElSelect_energytype.value
            );
            return obj ? obj.name : "--";
          }
        },
        {
          prop: "str", // 支持path a[0].b
          label: $T("报警类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: () => {
            let obj = vm.ElOption_2.options_in.find(
              i => i.id === vm.ElSelect_2.value
            );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "lossLevel", // 支持path a[0].b
          label: $T("损耗级别"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            return `${cellValue + 1} ` + $T("级损耗");
          }
        },
        {
          prop: "roomName", // 支持path a[0].b
          label: $T("房间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "130", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "objectName", // 支持path a[0].b
          label: $T("设备"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "130", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "description", // 支持path a[0].b
          label: $T("描述"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        }
      ],
      ElTableColumn_status: {
        prop: "status", // 支持path a[0].b
        label: $T("状态"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "110" //该宽度会自适应
      },
      detail: {
        visible: false,
        inputData_in: null,
        energytype_in: null
      },
      eventHandle: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  methods: {
    async init() {
      this.lossLevel = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.selectNode = {};
      this.ElSelect_1.value = null;
      this.ElSelect_2.value = this.ElOption_2.options_in[0]?.id;
      this.ElSelect_4.value = 1;
      this.ElSelect_energytype.value = null;
      this.CetDatePicker_time.val = [
        this.$moment().startOf("date").valueOf(),
        this.$moment().endOf("date").valueOf()
      ];
      await this.getProjectEnergy();
      this.getTreeData();
    },
    datePickerBlur() {
      this.datePickerEnd = 0;
      this.datePickerStart = 0;
    },
    async getLossLevel() {
      if (!this.currentNode) {
        this.ElOption_1.options_in = [];
        this.ElSelect_1.value = null;
      } else {
        let curDepth = this.currentNode.curDepth - 2,
          maxDepth = this.currentNode.maxDepth - 2,
          options = [];
        if (curDepth < 0) {
          curDepth = 0;
        }
        if (maxDepth < 0) {
          maxDepth = 0;
        }
        for (let index = curDepth; index <= maxDepth; index++) {
          options.push({
            level: index,
            name: `${index + 1}` + $T("级损耗")
          });
        }
        options.unshift({
          level: -1,
          name: $T("全部")
        });
        this.ElOption_1.options_in = options;
        this.ElSelect_1.value = options[0].level;
      }
    },
    async getProjectEnergy() {
      const response = await customApi.getProjectEnergy();
      if (response.code !== 0) {
        return;
      }

      const data = response.data || [];
      this.ElOption_energytype.options_in = data.filter(
        item => this.standardEnergyType.indexOf(item.energytype) === -1
      );
      this.ElSelect_energytype.value =
        this.ElOption_energytype.options_in?.[0].energytype;
    },
    initTable() {
      this.CetTable_1.data = [];
      this.CetTable_1.totalCount = 0;
    },
    ElSelect_energytype_change() {
      this.currentNode = null;
      this.totalCount = 0;
      this.CetTable_1.data = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.getTreeData();
    },
    getTreeData() {
      if (!this.ElSelect_energytype.value) {
        return;
      }
      let data = {
        energyType: this.ElSelect_energytype.value
      };
      customApi
        .lossConfigProjectTree(data, { keepTransformer: false })
        .then(response => {
          if (response.code === 0) {
            let data = this._.get(response, "data");
            this.CetGiantTree_1.inputData_in = data;
            this.CetGiantTree_1.selectNode = this._.get(data, "[0]");
          }
        });
    },
    CetGiantTree_1_currentNode_out(val) {
      let level = val.level;
      let lossLevels = this.lossLevel.filter(item => item.level >= level - 1);
      this.ElOption_1.options_in = this._.cloneDeep(lossLevels);
      this.ElSelect_1.value = this._.get(
        this.ElOption_1.options_in,
        "[0].level"
      );
      this.currentNode = this._.cloneDeep(val);
      this.getLossLevel();
      this.getTableData();
    },
    CetButton_prv_statusTrigger_out() {
      let date = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = [
        this.$moment(date[0]).subtract(1, "d").valueOf(),
        this.$moment(date[1]).subtract(1, "d").valueOf()
      ];
      this.getTableData();
    },
    CetButton_next_statusTrigger_out() {
      let date = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = [
        this.$moment(date[0]).add(1, "d").valueOf(),
        this.$moment(date[1]).add(1, "d").valueOf()
      ];
      this.getTableData();
    },
    getParams() {
      const vm = this;
      let date = vm.CetDatePicker_time.val;
      let queryData = {
        endTime: vm.$moment(date[1]).endOf("d").valueOf() + 1,
        startTime: vm.$moment(date[0]).startOf("d").valueOf(),
        energyType: this.ElSelect_energytype.value,
        eventType: 706,
        cycle: this.ElSelect_2.value,
        status: this.ElSelect_4.value,
        nodes: [
          {
            id: vm.currentNode.id,
            modelLabel: vm.currentNode.modelLabel
          }
        ]
      };
      if (this.ElSelect_1.value !== -1) {
        queryData.lossLevel = this.ElSelect_1.value;
      }
      return queryData;
    },
    getTableData() {
      const vm = this;
      if (
        !vm.currentNode ||
        !vm.ElSelect_energytype.value ||
        vm.ElSelect_1.value === null
      ) {
        vm.initTable();
        return;
      }
      let queryData = this.getParams();
      this.CetTable_1.dynamicInput.queryData = queryData;
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    CetButton_exprot_statusTrigger_out() {
      let queryData = this.getParams();
      common.downExcel(
        `/eembaseloss/eem-base/loss/v1/config/export/event`,
        queryData
      );
    },
    CetTable_1_totalCount_out(val) {
      this.CetTable_1.totalCount = val;
    },
    toDetail(scope) {
      const vm = this;
      let item = this._.cloneDeep(scope.row);
      let energytypeObj = vm.ElOption_energytype.options_in.find(
        i => i.energytype === vm.ElSelect_energytype.value
      );
      item.$energytype = energytypeObj ? energytypeObj.name : "--";
      let typeObj = vm.ElOption_2.options_in.find(
        i => i.id === vm.ElSelect_2.value
      );
      item.$type = typeObj ? typeObj.text : "--";
      vm.detail.inputData_in = item;
      vm.detail.energytype_in = vm.ElSelect_energytype.value;
      vm.detail.visible = true;
    },
    handle(scope) {
      this.eventHandle.inputData_in = scope.row;
      this.eventHandle.visibleTrigger_in = new Date().getTime();
    }
  },
  mounted() {
    // 过滤转为不需要过滤
    // let alarmType = this.alarmType?.filter(i => !i.text?.includes("小时"));
    this.ElOption_2.options_in = this.alarmType;
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
.tableBox {
  :deep(.el-footer) {
    text-align: right;
  }
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
}
</style>
