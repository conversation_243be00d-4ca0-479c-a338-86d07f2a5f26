<template>
  <ElDrawer
    :title="$T('详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="1440px"
  >
    <Diagram v-bind="diagram" />
  </ElDrawer>
</template>
<script>
import common from "eem-base/utils/common";
import Diagram from "../../../components/diagram.vue";
import customApi from "@/api/custom";
export default {
  name: "energyLossOverviewDetailDialog",
  components: {
    Diagram
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    energytype_in: {
      type: Object
    },
    query_in: {
      type: Object
    }
  },
  computed: {},
  data() {
    return {
      unit: "--",
      openDrawer: false,
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      diagram: {
        initTrigger_in: new Date().getTime(),
        unit_in: "",
        query_in: null
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in() {
      this.init();
      this.openDrawer = true;
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },

  methods: {
    init() {
      this.getUnit();
    },

    async getUnit() {
      // 查询基本单位
      const params = {
        projectUnitClassify: 1
      };
      const data = [this.energytype_in?.energytype];
      await this.$nextTick();
      const res = await customApi.getDefaultUnitSetting(data, params);
      if (res.code !== 0) {
        return;
      }
      const uniten = res.data?.[0]?.uniten || "--";
      this.unit = uniten;
      this.diagram.unit_in = this.unit;
      let query_in = {
        energyType: this._.get(this.energytype_in, "energytype"),
        id: this._.get(this.inputData_in, "id"),
        modelLabel: this._.get(this.inputData_in, "modelLabel")
      };
      if (this.query_in) {
        Object.assign(query_in, this.query_in);
      } else {
        console.error("拓扑图时间入参不全");
      }
      this.diagram.query_in = query_in;
      this.diagram.initTrigger_in = new Date().getTime();
    }
  }
};
</script>
