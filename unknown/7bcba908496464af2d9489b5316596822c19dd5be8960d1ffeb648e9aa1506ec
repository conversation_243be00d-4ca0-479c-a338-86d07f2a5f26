//能管基础
import commonApi from "eem-base/api";
import * as energy from "./energy";
import * as lossAnalysis from "./lossAnalysis";
import * as unitTransition from "./unitTransition";
import * as energyLossManagement from "./energyLossManagement";
import * as config from "./config";
import * as tree from "./tree";
import * as networkEnergy from "./networkEnergy";

export default {
  ...commonApi,
  ...energy,
  ...lossAnalysis,
  ...unitTransition,
  ...energyLossManagement,
  ...config,
  ...tree,
  ...networkEnergy
};
