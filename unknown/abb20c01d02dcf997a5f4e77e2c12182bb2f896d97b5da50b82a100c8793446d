<template>
  <div class="page" :style="{ height: containerHeight }">
    <CetChart v-bind="CetChart_1" @click="clickChart"></CetChart>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  name: "LossAnalysisSankey",
  props: {
    unit_in: {
      type: String,
      default: ""
    },
    inputData_in: Object,
    params: Object
  },
  data(vm) {
    return {
      deep: 2,
      containerHeight: 0,
      CetChart_1: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item",
            position: "bottom",
            confine: true,
            triggerOn: "mousemove",
            transitionDuration: 0,
            formatter: params => {
              if (
                ["switchcabinet", "arraycabinet"].includes(
                  params.data.modelLabel
                )
              ) {
                return "";
              }
              if (params.data.source) {
                return `${params.data.sourceLabel} > ${
                  params.data.targetLabel
                }: ${common.formatNum(
                  common.roundNumber(params.data.value, 2, "--")
                )} ${vm.unit_in}`;
              } else {
                let isLast = vm.inputData_in.linkNode.find(
                  item => item.source === params.data.name
                );
                let color =
                  params.data.lossPercent != null &&
                  params.data.threshold != null &&
                  Math.abs(params.data.lossPercent) >= params.data.threshold
                    ? "color:#F76771!important;"
                    : "";
                let str = `${params.data.label}`;
                const changeNodeData = params.data.changeNodeData;

                str += `<br />${
                  changeNodeData?.length ? $T("总能耗") : $T("能耗值")
                }：${common.formatNum(
                  common.roundNumber(params.data.value, 2, "--")
                )} ${vm.unit_in}`;

                // 展示合并前各个节点的能耗值
                if (changeNodeData?.length) {
                  changeNodeData.forEach(item => {
                    str += `<br />${item.name}：${common.formatNum(
                      common.roundNumber(item.value, 2, "--")
                    )}${this.unit_in}`;
                  });
                }

                if (isLast && params.data.depth < this.deep) {
                  const totalVal = `${common.formatNum(
                    common.roundNumber(params.data.nextLevelEnergy, 2, "--")
                  )}${this.unit_in}`;
                  str += `<br />${$T("下级能耗之和")}：${totalVal}<br />${$T(
                    "损耗值"
                  )}：${common.formatNum(
                    common.roundNumber(params.data.loss, 2, "--")
                  )}${this.unit_in}<br />${$T(
                    "损耗率"
                  )}：<span style=${color}>${
                    params.data.lossPercent || params.data.lossPercent === 0
                      ? Number(params.data.lossPercent * 100).toFixed(2) + "%"
                      : "--"
                  }</span>`;
                }
                return str;
              }
            }
          },
          series: {
            type: "sankey",
            layout: "none",
            top: 20,
            left: 130,
            draggable: false,
            layoutIterations: 0,
            emphasis: {
              focus: "adjacency"
            },
            nodeGap: 14,
            nodeAlign: "right",
            label: {
              formatter: params => {
                const { label = "", expand, name } = params.data || {};
                const result = this.isExpand.find(item => item.id === name);
                let tip = "";
                if (expand && !result?.expanded) {
                  tip = `(${$T("可展开")})`;
                }
                const MAX_LENGTH = 10;
                if (label.length > MAX_LENGTH) {
                  return `${label.slice(0, MAX_LENGTH)}...`;
                }
                return `${label}${tip}`;
              }
            },
            lineStyle: {
              opacity: 0.4
            },
            data: [],
            links: []
          }
        }
      },
      isExpand: [],
      initInputData: {}, //初始化数据存放
      handInputData: {}, //过滤使用数据存放
      nameObj: {}, //节点名称存储
      childrenData: {} //子节点数据
    };
  },
  watch: {
    inputData_in: {
      async handler(val) {
        this.deep = 2;
        await this.$nextTick();

        this.init();
      },
      deep: true
    }
  },
  methods: {
    init() {
      this.nameObj = {};
      this.CetChart_1.options.series.data = [];
      this.CetChart_1.options.series.links = [];
      if (
        !this.inputData_in ||
        !this.inputData_in.lossDataVoList ||
        !this.inputData_in.linkNode
      ) {
        return;
      }
      if (Array.isArray(this.inputData_in.lossDataVoList)) {
        this.inputData_in.lossDataVoList.forEach((item, index) => {
          item.iindex = index;
        });
      }
      this.initInputData = this._.cloneDeep(this.inputData_in);
      this.handInputData = this.filterInitData(this.initInputData);
      this.setChartData(this.handInputData);
    },
    setChartData(val) {
      this.isExpand = [];
      if (!val || !val.lossDataVoList) return;
      let depth = 0;
      // 先默认所有节点都可展开
      val.lossDataVoList.forEach(item => {
        item.expend = true;
        if (item.depth > depth) {
          depth = item.depth;
        }
      });
      //如果是最后一层子节点，不需要显示/可展示
      val.lossDataVoList.forEach(item => {
        let expanded = false;
        if (item.depth < depth) {
          expanded = true;
        } else {
          let getIsChild = this.initInputData.linkNode.filter(ii => {
            return item.name === ii.source;
          });
          expanded = getIsChild.length === 0;
        }
        this.isExpand.push({ id: item.name, expanded: expanded });
      });
      const chartData = [];
      // 桑基图data
      val.lossDataVoList.forEach((item, index) => {
        this.nameObj[item.name] = item.deviceName;
        let node = val.linkNode.find(i => i.target === item.name);
        if (node) {
          //赋值
          node.value = item.value || 0;
        }
        const hasNextLevelEnergy = Object.prototype.hasOwnProperty.call(
          item,
          "nextLevelEnergy"
        );
        chartData.push({
          expend: true,
          iindex: item.iindex,
          depth: item.depth,
          name: item.name,
          value: item.value || 0,
          label: item.deviceName,
          loss: item.loss,
          lossPercent: item.lossPercent,
          threshold: item.threshold,
          modelLabel: item.modelLabel,
          itemStyle:
            item.lossPercent != null &&
            item.threshold != null &&
            Math.abs(item.lossPercent) >= item.threshold
              ? {
                  color: "#F65D68"
                }
              : {
                  color: "#3E77FC"
                },
          changeNodeData: item.changeNodeData,
          nextLevelEnergy: hasNextLevelEnergy
            ? item.nextLevelEnergy
            : item.value - item.loss
        });
      });
      // 桑基图link,里的值如果都是0，则展示 暂无损耗图
      let hideSankey = true;
      val.linkNode = val.linkNode.map(item => {
        if (item.value && item.value !== 0) {
          hideSankey = false;
        }
        return {
          sourceLabel: this.nameObj[item.source],
          targetLabel: this.nameObj[item.target],
          ...item
        };
      });
      if (hideSankey) {
        this.$emit("noSankey", false);
        return;
      }

      this.calcHeight(chartData);
      this.CetChart_1.options.series.data = chartData;
      this.CetChart_1.options.series.links = val.linkNode;
    },
    // 点击节点展开
    async clickChart(val) {
      if (val.dataType === "edge") return;

      const vm = this;
      const data = this._.cloneDeep(val.data);
      let index = 0;
      const result = this.isExpand.find((item, idx) => {
        index = idx;
        return item.id === data.name;
      });
      //如果是点击最后一层子节点，直接return退出处理
      let getIsChild = this.initInputData.linkNode.filter(ii => {
        return data.name === ii.source;
      });
      if (getIsChild.length === 0) {
        return;
      }

      //判断是否可展开,点击能源类型时只显示当前的能源流向
      if (result) {
        // 收缩，根节点不能收缩
        if (result.expanded && data.depth !== 0) {
          this.isExpand[index].expanded = false;
          const nodeData = this._.cloneDeep(
            this.CetChart_1.options.series.data
          );
          const linkData = this._.cloneDeep(
            this.CetChart_1.options.series.links
          );
          let numList = nodeData.filter(item => {
            return item.depth === this.deep;
          });
          if (val.data.depth === this.deep - 1 && numList.length === 1) {
            this.deep--;
          }
          this.handleNode(linkData, data, nodeData);
          vm.calcHeight(nodeData);
          this.CetChart_1.options.series.data = nodeData;
          this.CetChart_1.options.series.links = linkData;
          // 展开
        } else if (!result.expanded) {
          this.isExpand[index].expanded = true;
          await this.getChildNodeData(data);
          const childNodeData = this.childrenData;

          const chartData = [];
          // 桑基图data
          const lossDataVoList = childNodeData.lossDataVoList;
          const expand = lossDataVoList;
          // 先默认所有节点都可展开
          expand.forEach(item => {
            item.expend = true;
          });
          //如果是最后一层子节点，不需要显示/可展示
          expand.forEach(item => {
            let expanded = false;
            let getIsChild = this.initInputData.linkNode.filter(ii => {
              return item.name === ii.source;
            });
            expanded = getIsChild.length === 0;
            vm.isExpand.push({ id: item.name, expanded: expanded });
          });
          let linkNode = vm._.cloneDeep(childNodeData.linkNode);
          lossDataVoList.forEach((item, index) => {
            this.nameObj[item.name] = item.deviceName;
            let node = linkNode.find(i => i.target === item.name);
            if (node) {
              node.value = item.value || 0;
            }
            const hasNextLevelEnergy = Object.prototype.hasOwnProperty.call(
              item,
              "nextLevelEnergy"
            );
            chartData.push({
              expend: true,
              iindex: item.iindex,
              depth: item.depth,
              name: item.name,
              value: item.value || 0,
              label: item.deviceName,
              loss: item.loss,
              lossPercent: item.lossPercent,
              threshold: item.threshold,
              modelLabel: item.modelLabel,
              itemStyle:
                item.lossPercent != null &&
                item.threshold != null &&
                Math.abs(item.lossPercent) >= item.threshold
                  ? {
                      color: "#F65D68"
                    }
                  : {
                      color: "#3E77FC"
                    },
              changeNodeData: item.changeNodeData,
              nextLevelEnergy: hasNextLevelEnergy
                ? item.nextLevelEnergy
                : item.value - item.loss
            });
          });
          // 桑基图link
          linkNode = linkNode.map(item => {
            return {
              sourceLabel: this.nameObj[item.source],
              targetLabel: this.nameObj[item.target],
              ...item
            };
          });
          let nodes = this._.cloneDeep(chartData);
          const links = this._.cloneDeep(linkNode);
          vm.calcHeight(nodes);
          vm.CetChart_1.options.series.data = nodes;
          vm.CetChart_1.options.series.links = links;
          if (val.data.depth === this.deep) {
            this.deep++;
          }
        }
      }
    },
    //点击缩收删除选中节点下子节点
    handleNode(linkData, data, nodeData) {
      const toDelete = [];
      // linkData中找到所有source为当前点击的节点
      const deleteArr = linkData.filter(item => {
        return data && item.source === data.name;
      });
      if (deleteArr.length) {
        // linkData中删除deleteArr数据
        deleteArr.forEach(item => {
          this.deleteFun(linkData, item, toDelete);
        });
      }

      if (toDelete.length) {
        // linkData中被删除的数据继续调用handleNode
        toDelete.forEach(item => {
          const deleteData = nodeData.find(it => {
            return it.name === item.target;
          });
          this.handleNode(linkData, deleteData, nodeData);
        });
      }
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      deleteArr.forEach(item => {
        const idx = nodeData.findIndex(it => {
          return item.target === it.name;
        });
        const it = this.isExpand.findIndex(i => {
          return item.target === i.id;
        });
        if (idx >= 0) {
          nodeData.splice(idx, 1);
        }
        if (it >= 0) {
          this.isExpand.splice(it, 1);
        }
      });
    },
    deleteFun(linkData, item, toDelete) {
      const idx = linkData.findIndex(it => {
        return this._.isEqual(it, item);
      });
      if (idx >= 0) {
        toDelete.push(linkData.splice(idx, 1)[0]);
      }
    },
    calcHeight(data) {
      const height = data.length * 20;
      this.containerHeight = height + "px";
    },
    //过滤初始化返回数据，只需要显示三层数据结构;depth ===0和depth === 1归类为父节点
    filterInitData(val) {
      let lossDataVoList = val.lossDataVoList || [],
        linkNode = val.linkNode || [],
        toDelete = [];
      let filLossDataVoList = lossDataVoList.filter(item => item.depth <= 2);
      toDelete = lossDataVoList.filter(item => item.depth > 2);
      let filLinkNode = linkNode.filter(item => {
        const result = toDelete.find(
          toDeleteItem => toDeleteItem.name === item.target
        );
        return !result;
      });
      return {
        lossDataVoList: filLossDataVoList,
        linkNode: filLinkNode
      };
    },
    //获取点击选择节点下一子层级
    async getChildNodeData(data) {
      let params = this.params;
      params.depth = data.depth + 1;
      let loss = [];
      let link = [];
      const res = await customApi.getLossAnalysisDepth(params);
      if (res.code === 0) {
        loss = res.data.lossDataVoList || [];
        link = res.data.linkNode || [];
      }
      this.childrenData = {
        lossDataVoList: loss,
        linkNode: link
      };
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: auto;
  min-height: 100%;
}
</style>
