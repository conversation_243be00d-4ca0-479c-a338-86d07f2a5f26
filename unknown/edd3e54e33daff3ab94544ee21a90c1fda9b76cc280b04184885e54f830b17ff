<template>
  <ElDrawer
    :title="$T('下级表计详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div class="h-full flex flex-col">
      <ElInput
        v-model.trim="ElInput_search.value"
        v-bind="ElInput_search"
        v-on="ElInput_search.event"
      >
        <i slot="suffix" class="el-input__icon el-icon-search"></i>
      </ElInput>
      <div class="tableBox flex-col flex mt-J3 flex-auto">
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
        </CetTable>
        <div class="mt-J3 mr-J3 text-right">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentPageChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :total="totalCount"
            layout="total,sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
          />
        </div>
      </div>
    </div>
  </ElDrawer>
</template>
<script>
import common from "eem-base/utils/common";
export default {
  name: "energyLossOverviewSubordinate",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    symbol_in: {
      type: String,
      default: "--"
    }
  },
  computed: {},
  data(vm) {
    return {
      openDrawer: false,
      ElInput_search: {
        value: "",
        placeholder: $T("请输入回路名称"),
        style: {
          width: "264px"
        },
        event: {
          change: this.searchChange
        }
      },
      pageSize: 10,
      currentPage: 1,
      totalCount: 0,
      fliterNameTableData: [],
      originalTableData: [],
      CetTable_1: {
        totalCount: 0,
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "70" //绝对宽度
        },
        {
          prop: "roomName", // 支持path a[0].b
          label: $T("房间名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "150", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "endDevName", // 支持path a[0].b
          label: $T("回路名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "150", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "usage", // 支持path a[0].b
          "render-header": h => {
            return h("span", `${$T("用电量")}(${vm.symbol_in})`);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "150", //该宽度会自适应
          formatter: this.formatNumberColWithPercision(2)
        }
      ]
    };
  },
  watch: {
    visibleTrigger_in() {
      this.init();
      this.openDrawer = true;
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },

  methods: {
    formatNumberColWithPercision(precision) {
      return function (row, column, cellValue) {
        return common.formatNum(
          common.formatNumberWithPrecision(cellValue, precision)
        );
      };
    },
    init() {
      this.pageSize = 10;
      this.currentPage = 1;
      this.totalCount = 0;
      this.fliterNameTableData = [];
      this.originalTableData = [];
      this.ElInput_search.value = "";
      this.getTableData();
    },
    getTableData() {
      const vm = this;
      let tableData = vm._.get(vm.inputData_in, "endDevInfos", []) || [];
      vm.fliterNameTableData = vm._.cloneDeep(tableData);
      vm.originalTableData = vm._.cloneDeep(tableData);
      vm.totalCount = tableData.length;
      vm.handleCurrentPageChange(1);
    },
    // 关键字过滤
    searchChange(val) {
      let tableData = this.fliterNameTableData.filter(
        item => item.endDevName.toUpperCase().indexOf(val.toUpperCase()) !== -1
      );
      this.originalTableData = this._.cloneDeep(tableData);
      this.totalCount = tableData.length;
      this.handleCurrentPageChange(1);
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentPageChange(1);
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.CetTable_1.data = this._.cloneDeep(
        this.originalTableData.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
      );
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.tableBox {
  height: 500px;
  .tableTitle {
    @include line_height(Hm);
  }
  :deep(.el-footer) {
    text-align: right;
  }
}
</style>
