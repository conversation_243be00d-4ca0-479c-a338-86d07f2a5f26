<template>
  <el-tabs
    v-model="activeName"
    class="fullfilled el-tabs bg-BG1 rounded-Ra flex flex-col"
  >
    <el-tab-pane name="threshold" :label="$T('损耗阈值配置')" class="h-full">
      <Threshold
        v-if="activeName === 'threshold'"
        :energytypes_in="energytypes"
      />
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import customApi from "@/api/custom";
import Threshold from "./threshold.vue";
export default {
  name: "energyLossConfig",
  components: {
    Threshold
  },
  computed: {
    standardEnergyType() {
      return this.$store.state.standardEnergyType;
    }
  },

  data() {
    return {
      activeName: "",
      energytypes: []
    };
  },
  methods: {
    async getProjectEnergy() {
      const response = await customApi.getProjectEnergy();
      if (response.code !== 0) {
        return;
      }

      const data = response.data || [];

      this.energytypes = data.filter(item => {
        return this.standardEnergyType.indexOf(item.energytype) === -1;
      });
    }
  },
  async mounted() {
    await this.getProjectEnergy();
    this.activeName = "threshold";
  }
};
</script>

<style lang="scss" scoped>
.el-tabs {
  :deep() {
    .el-tabs__header {
      margin-bottom: 0;
      line-height: 46px;
    }
    .el-tabs__nav-scroll {
      padding-left: var(--J4);
    }
    .el-tabs__content {
      flex: 1;
      min-height: 0;
    }
  }
}
</style>
