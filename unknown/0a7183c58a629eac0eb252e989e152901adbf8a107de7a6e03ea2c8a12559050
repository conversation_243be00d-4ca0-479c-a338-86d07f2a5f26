<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full flex flex-col">
        <CustomElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          :prefix_in="$T('能源类型')"
          class="mb-J1"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
          ></ElOption>
        </CustomElSelect>
        <CetGiantTree
          class="flex-auto"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </div>
    </template>
    <template #container>
      <div class="fullfilled flex flex-col">
        <div class="flex flex-row">
          <TimeTool @date_out="handleDateChange"></TimeTool>
          <CustomElSelect
            class="ml-J3"
            v-model="ElSelect_type.value"
            v-bind="ElSelect_type"
            v-on="ElSelect_type.event"
            :prefix_in="$T('展示方式')"
          >
            <ElOption
              v-for="item in ElOption_type.options_in"
              :key="item[ElOption_type.key]"
              :label="item[ElOption_type.label]"
              :value="item[ElOption_type.value]"
              :disabled="item[ElOption_type.disabled]"
            ></ElOption>
          </CustomElSelect>
        </div>
        <div class="relative flex-auto mt-J3">
          <LossAnalysis
            :unit_in="unit_in"
            v-if="showLossAnalysis && ElSelect_type.value === 2"
            v-bind="lossAnalysisConfig"
          />
          <div v-show="showSankey" class="fullheight fullwidth">
            <Sankey
              :unit_in="unit_in"
              v-bind="sankey"
              @noSankey="changeShowLossAnalysis"
            />
          </div>
          <div
            v-if="
              !showLossAnalysis || (!isShowImage && ElSelect_type.value === 1)
            "
            class="fullfilled"
          >
            <div class="noTopology">
              <div class="fl text">
                <div>{{ $T("无损耗数据不展示能流图") }}</div>
              </div>
              <img class="fr img" :src="emptyImg" alt="" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </CetAside>
</template>
<script>
import customApi from "@/api/custom";
import LossAnalysis from "./components/LossAnalysis";
import Sankey from "./components/sankey";
import TimeTool from "./components/TimeTool.vue";
import omegaTheme from "@omega/theme";
import { CustomElSelect } from "eem-base/components";

export default {
  name: "lossAnalysis",
  components: { LossAnalysis, Sankey, TimeTool, CustomElSelect },
  computed: {
    showSankey() {
      return (
        this.showLossAnalysis &&
        this.ElSelect_type.value === 1 &&
        this.isShowImage
      );
    },
    emptyImg() {
      return omegaTheme.theme === "light"
        ? require("./assets/empty_light.png")
        : require("./assets/empty_dark.png");
    },
    standardEnergyType() {
      return this.$store.state.standardEnergyType;
    }
  },
  data() {
    return {
      unit_in: "",
      currentNode: null,
      lossAnalysisConfig: {
        inputData_in: null,
        params: null
      },
      sankey: {
        inputData_in: null,
        params: null
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      ElSelect_type: {
        value: 2,
        style: {
          width: "300px"
        },
        size: "small",
        event: {
          // change: this.ElSelect_type_change
        }
      },
      ElOption_type: {
        options_in: [
          {
            id: 2,
            name: $T("拓扑图")
          },
          {
            id: 1,
            name: $T("能流图")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      showLossAnalysis: false,
      isShowImage: true,
      time: [],
      cycle: 12,
      oldSelectNode: null
    };
  },
  methods: {
    async getEnergy() {
      const res = await customApi.getProjectEnergy();
      if (res.code !== 0) {
        return;
      }

      let list = res.data || [];
      this.ElOption_1.options_in = list.filter(
        item => this.standardEnergyType.indexOf(item.energytype) === -1
      );
      this.ElSelect_1.value = list[0]?.energytype;

      await this.getUnit();
      this.getTree();
    },
    async getTree() {
      let params = {
        energyType: this.ElSelect_1.value
      };
      const res = await customApi.lossConfigProjectTree(params, {
        keepTransformer: true
      });
      if (res.code !== 0) {
        return;
      }

      let tree = res.data || [];
      this.CetGiantTree_1.inputData_in = tree;
      this.CetGiantTree_1.selectNode = tree[0];

      if (
        this.oldSelectNode?.tree_id === this.CetGiantTree_1.selectNode.tree_id
      ) {
        this.getEnergyLoss();
      }
    },
    async ElSelect_1_change_out() {
      await this.getUnit();
      this.getTree();
    },
    async getUnit() {
      const params = {
        projectUnitClassify: 1
      };
      const data = [this.ElSelect_1.value];
      const res = await customApi.getDefaultUnitSetting(data, params);
      this.unit_in = res.data?.[0]?.uniten ?? "--";
    },
    CetGiantTree_1_currentNode_out(val) {
      this.oldSelectNode = _.cloneDeep(val);
      this.currentNode = _.cloneDeep(val);
      this.isShowImage = true;
      this.getEnergyLoss();
    },
    async getEnergyLoss() {
      if (_.isEmpty(this.currentNode)) {
        return;
      }

      const params = {
        startTime: this.time[0],
        endTime: this.time[1],
        aggregationCycle: this.cycle,
        id: this.currentNode?.id,
        modelLabel: this.currentNode?.modelLabel,
        energyType: this.ElSelect_1.value,
        depth: 2
      };
      const res = await customApi.getLossAnalysisDepth(params);
      if (res.code !== 0) {
        this.showLossAnalysis = false;
        return;
      }

      this.showLossAnalysis = Boolean(
        res.data.linkNode?.length && res.data.lossDataVoList?.length
      );
      this.lossAnalysisConfig.inputData_in = res.data || {};
      this.lossAnalysisConfig.params = params;
      if (!this.showSankey) {
        return;
      }
      this.sankey.inputData_in = res.data || {};
      this.sankey.params = params;
    },
    changeShowLossAnalysis(val) {
      this.isShowImage = val;
    },
    handleDateChange(time, type) {
      this.time = time;
      this.cycle = type;
      this.isShowImage = true;
      this.getEnergyLoss();
    }
  },
  mounted() {
    this.getEnergy();
  }
};
</script>
<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
.main-block {
  @include background_color(BG1);
  border-radius: 4px;
  padding: 24px;
}
.noTopology {
  width: 725px;
  height: 315px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  .img {
    height: 315px;
  }
  .text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    & > div:nth-child(1) {
      @include font_color(ZS);
      @include font_size(H);
      font-weight: bold;
    }
  }
}
</style>
