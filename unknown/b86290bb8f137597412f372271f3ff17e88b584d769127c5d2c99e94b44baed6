import omegaTheme from "@omega/theme";
import fetch from "eem-base/utils/fetch";
let eventTextColor = "#FFFFFF";
const eventTypeColor = {
    light: {
      1: "#FF3F3F", // 事故事件
      2: "#FF842B", // 报警事件
      3: "#28B061", // 一般事件
      4: "#FFC531", // 预警事件
      5: "#0D86FF" // 其他事件
    },
    dark: {
      1: "#F95E5A", // 事故事件
      2: "#FF9D09", // 报警事件
      3: "#28B061", // 一般事件
      4: "#FFD12F", // 预警事件
      5: "#0D86FF" // 其他事件
    }
  },
  eventGradeColor = {
    light: {
      1: "#FF3F3F", // 一级
      2: "#FF842B", // 二级
      3: "#FFC531", // 三级
      4: "#0D86FF" // 其他
    },
    dark: {
      1: "#F95E5A", // 一级
      2: "#FF9D09", // 二级
      3: "#FFD12F", // 三级
      4: "#0D86FF" // 其他
    }
  };

// 获取事件类型颜色
export const getEventTypeColor = value => {
  const themeName = omegaTheme.theme === "dark" ? "dark" : "light";
  const color = eventTextColor;
  return { bgColor: eventTypeColor[themeName][value], color: color };
};
// 获取事件等级颜色
export const getEventGradeColor = value => {
  const themeName = omegaTheme.theme === "dark" ? "dark" : "light";
  const color = eventTextColor;
  return { bgColor: eventGradeColor[themeName][value], color: color };
};

export async function init() {
  const res = await fetch({
    url: `/eem-service/v1/common/eventColor`,
    method: "GET"
  });
  const gradeColor = res?.data?.eventGradeColor;
  const typeColor = res?.data?.eventTypeColor;
  const fontColor = res?.data?.fontColor;

  if (typeColor) {
    eventTypeColor.light = typeColor;
    eventTypeColor.dark = typeColor;
  }
  if (gradeColor) {
    eventGradeColor.light = gradeColor;
    eventGradeColor.dark = gradeColor;
  }
  if (fontColor) {
    eventTextColor = fontColor;
  }
}
