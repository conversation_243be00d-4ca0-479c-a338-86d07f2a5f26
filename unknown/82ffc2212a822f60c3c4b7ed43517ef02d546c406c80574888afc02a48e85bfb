<template>
  <div class="fullwidth dataCarousel rounded-Ra bg-BG1" ref="dataCarousel">
    <div class="listBox clearfix" ref="listBox">
      <div
        class="wrapBox clearfix"
        :style="`width:${wrapWidth}px;left:${wrapLfet}px`"
      >
        <template v-for="(item, index) in list">
          <div
            class="node bg-BG2 rounded-Ra p-J3 fl flex flex-col"
            :key="`node${index}`"
            :style="`width:${nodeWidth}px`"
          >
            <el-tooltip effect="light" :content="item.label">
              <div class="label text-ellipsis">
                {{ item.label }}({{ symbol_in }})
              </div>
            </el-tooltip>
            <div class="flex-auto overflow-y-auto" v-if="isMutiBus(item)">
              <el-tooltip
                effect="light"
                :content="`${multipleItem.objectName}：${valueFormat(
                  multipleItem.usage
                )}`"
                v-for="(multipleItem, multipleIndex) in item.multipleData"
                :key="multipleIndex"
              >
                <div class="value text-ellipsis mtJ multiple">
                  {{
                    `${multipleItem.objectName}：${valueFormat(
                      multipleItem.usage
                    )}`
                  }}
                </div>
              </el-tooltip>
            </div>
            <template v-else>
              <el-tooltip effect="light" :content="valueFormat(item.value)">
                <div class="value text-ellipsis mt-J1">
                  {{ valueFormat(item.value) }}
                </div>
              </el-tooltip>
            </template>
          </div>
          <div
            v-if="index !== list.length - 1"
            class="lineBox fl"
            :key="`line${index}`"
            :style="`width:${lineWidth}px`"
          >
            <div class="line"></div>
            <el-tooltip
              effect="light"
              :content="
                $T('{0}级损耗量', item.index + 1) +
                `：${valueFormatUnit(item.lossNum)}`
              "
            >
              <div v-if="item.index !== -1" class="text-ellipsis lineText">
                <span>
                  {{ $T("{0}级损耗量", item.index + 1) }}：{{
                    valueFormatUnit(item.lossNum)
                  }}
                </span>
              </div>
            </el-tooltip>
            <el-tooltip
              effect="light"
              :content="
                $T('{0}级损耗率', item.index + 1) +
                `：${rateFormat(item.lossRate)}`
              "
            >
              <div
                v-if="item.index !== -1"
                class="text-ellipsis lineText"
                :title="
                  $T('{0}级损耗率', item.index + 1) +
                  `：${rateFormat(item.lossRate)}`
                "
              >
                <span>{{ $T("{0}级损耗率", item.index + 1) }}：</span>
                <span>
                  {{ rateFormat(item.lossRate) }}
                </span>
              </div>
            </el-tooltip>
            <CetButton
              v-if="item.index > 0"
              class="queryBtn"
              v-bind="CetButton_query"
              v-on="CetButton_query.event"
              @click="query(item)"
            ></CetButton>
          </div>
        </template>
      </div>
    </div>
    <CetButton
      class="prv"
      :disable_in="index <= 0"
      v-bind="CetButton_prv"
      v-on="CetButton_prv.event"
    ></CetButton>
    <CetButton
      class="next"
      :disable_in="index >= list.length - (elLanguage ? 4 : 5)"
      v-bind="CetButton_next"
      v-on="CetButton_next.event"
    ></CetButton>
    <LossRate v-bind="lossRate" />
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import LossRate from "./dialog/lossRate.vue";

export default {
  components: {
    LossRate
  },
  props: {
    initTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    },
    symbol_in: {
      type: String,
      default: "--"
    }
  },
  computed: {
    elLanguage() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      common,
      clientWidth: null,
      nodeWidth: null,
      lineWidth: null,
      wrapWidth: null,
      wrapLfet: 0,
      index: 0,
      list: [],
      CetButton_prv: {
        visible_in: true,
        // disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        // disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_query: {
        visible_in: true,
        disable_in: false,
        title: $T("查看排名"),
        plain: true,
        event: {}
      },
      lossRate: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  watch: {
    initTrigger_in() {
      this.init();
    },
    index(val) {
      this.wrapLfet = -(val * (this.nodeWidth + this.lineWidth));
    }
  },
  methods: {
    init() {
      this.list = [];
      if (this.inputData_in && this.inputData_in.length) {
        this.list = this.inputData_in.map(item => {
          let label = $T("{0}级计量", item.depth + 1);
          if (item.depth === -1) {
            label = $T("账单值");
          }
          return {
            label: label,
            value: item.measure,
            index: item.depth,
            lossNum: item.loss,
            lossRate: item.lossRate,
            rankings: item.rankings,
            multiple: item.multiple,
            multipleData: item.multipleData
          };
        });
      }
      this.index = 0;
      this.calculateWidth();
    },
    // 计算每个节点、连线的宽度
    calculateWidth() {
      this.clientWidth = this.$refs.listBox.clientWidth;
      if (this.elLanguage) {
        // 英文平均四等分
        this.nodeWidth = this.clientWidth * 0.15;
        this.lineWidth = this.clientWidth * 0.1333;
      } else {
        // 平均五等分
        this.nodeWidth = this.clientWidth * 0.13;
        this.lineWidth = this.clientWidth * 0.0875;
      }
      let nodeNum = this.list.length,
        lineNum = this.list.length > 0 ? this.list.length - 1 : 0;
      this.wrapWidth = nodeNum * this.nodeWidth + lineNum * this.lineWidth;
    },
    CetButton_prv_statusTrigger_out() {
      this.index -= 1;
    },
    CetButton_next_statusTrigger_out() {
      this.index += 1;
    },
    query(item) {
      this.lossRate.inputData_in = this._.cloneDeep(item);
      this.lossRate.visibleTrigger_in = new Date().getTime();
    },
    valueFormat(val) {
      if (val || val === 0) {
        return common.formatNum(val.toFixed2(2));
      } else {
        return "--";
      }
    },
    valueFormatUnit(val) {
      if (val || val === 0) {
        return common.formatNum(val.toFixed2(2)) + this.symbol_in;
      } else {
        return "--";
      }
    },
    rateFormat(val) {
      if (typeof val === "number") {
        return common.formatNum((val * 100).toFixed2(2)) + "%";
      } else {
        return "--";
      }
    },
    isMutiBus(val) {
      return val.multiple && val.multipleData?.length > 1;
    }
  }
};
</script>
<style lang="scss" scoped>
.dataCarousel {
  padding: 24px;
  height: 144px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  .prv,
  .next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    :deep(.el-button) {
      width: 16px;
      height: 64px;
      padding: 0;
      box-shadow: 0px 0px 10px 1px rgba(88, 90, 102, 0.25);
    }
  }
  .prv {
    left: 16px;
  }
  .next {
    right: 16px;
  }
  .listBox {
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: relative;
    .wrapBox {
      height: 100%;
      position: absolute;
      top: 0;
      transition: all 0.4s ease-in-out;
    }
    .node {
      height: 100%;
      width: 13%;
      box-sizing: border-box;
      .label {
        @include font_color(T3);
      }
      .value {
        font-weight: bold;
        @include font_size(H);
        @include line_height(H);
        &.multiple {
          font-size: 14px;
          line-height: 16px;
        }
      }
    }
    .lineBox {
      height: 100%;
      width: 8.75%;
      position: relative;
      padding: 8px;
      box-sizing: border-box;
      .line {
        height: 2px;
        width: 100%;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        @include background_color(BG);
      }
      .lineText {
        span {
          @include font_color(Sta4);
          font-size: 12px;
        }
      }
      .queryBtn {
        position: absolute;
        top: calc(50% + 8px);
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
