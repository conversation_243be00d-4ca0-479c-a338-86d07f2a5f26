<template>
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog small"
  >
    <div class=" ">
      <el-checkbox class="mb-J3" v-model="checked" @change="checkedChange">
        {{ $T("默认选中子节点") }}
      </el-checkbox>
      <CetGiantTree
        class="giantTree"
        ref="giantTree1"
        v-show="!checked"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <CetGiantTree
        class="giantTree"
        ref="giantTree2"
        v-show="checked"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import customApi from "@/api/custom";

export default {
  name: "NodeSelect",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    rootNode_in: Object
  },

  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      currentNode: null,
      CetDialog_1: {
        title: $T("关联节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      checked: false,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.checked = false;
      this.getTreeData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    checkedChange() {
      const vm = this;
      let checkNodes = vm._.cloneDeep(vm.checkNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree1.$el).find(".ztree").scrollLeft(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollLeft(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree2.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree1.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    },
    // 获取节点树
    async getTreeData() {
      const queryData = {
        nodeTreeGroupId: 2,
        energyType: this.inputData_in.energytype,
        rootNode: {
          id: this.rootNode_in.id,
          modelLabel: this.rootNode_in.modelLabel
        }
      };
      const res = await customApi.getNodeTree(queryData);
      if (res.code !== 0) {
        return;
      }
      // 过滤掉项目下的开关柜或一段线
      const data = res.data || [];
      this.getNodeByTSScheme(data);
    },
    // 获取关联节点
    async getNodeByTSScheme(treeData) {
      this.checkNodes = [];
      const queryData = {
        schemeId: this.inputData_in.id
      };
      const res = await customApi.nodeByTSScheme(queryData);
      if (res.code !== 0 || !res.data?.length) {
        this.CetGiantTree_1.checkedNodes = [];
        this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        this.CetGiantTree_2.checkedNodes = [];
        this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
        this.CetGiantTree_1.inputData_in = treeData;
        this.CetGiantTree_2.inputData_in = treeData;
        return;
      }
      let checkedNodes = [];
      res.data.forEach(item => {
        checkedNodes.push({
          id: item.objectid,
          modelLabel: item.objectlabel,
          tree_id: item.objectlabel + "_" + item.objectid
        });
      });
      this.CetGiantTree_1.checkedNodes = this._.cloneDeep(checkedNodes);
      this.checkNodes = checkedNodes;
      setTimeout(() => {
        this.expandNode(
          checkedNodes,
          "tree_id",
          this.$refs.giantTree1.ztreeObj
        );
        this.expandNode(
          checkedNodes,
          "tree_id",
          this.$refs.giantTree2.ztreeObj
        );
      }, 0);
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree1.$el).find(".ztree").scrollLeft(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollLeft(0);
      }, 0);
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    async CetButton_confirm_statusTrigger_out() {
      const saveData = this.checkNodes.map(item => ({
        id: item.id,
        modelLabel: item.modelLabel,
        name: item.name
      }));
      const params = {
        schemeId: this.inputData_in.id
      };
      const res = await customApi.timeShareRelationship(saveData, params);
      if (res.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  .giantTree {
    height: 400px;
  }
}
</style>
