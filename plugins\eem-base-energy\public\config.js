__registeAppConfig.setAppconfig({
  version: "1.0.0",
  i18n: {
    en: {
      能耗: "Energy",
      能耗报警配置: "Energy Alarm Configuration",
      非自然周期配置: "Non-natural cycle configuration",
      分时方案: "Time-based solution",
      公共分摊方案配置: "Common share scheme configuration",
      损耗分摊配置: "Loss share configuration"
    }
  },
  navmenu: [
    {
      label: "能耗",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "能耗报警配置",
          category: "project",
          type: "menuItem",
          location: "/energyAlarmConfig",
          permission: "energyAlarmConfig"
        },
        {
          label: "非自然周期配置",
          category: "project",
          type: "menuItem",
          location: "/cycleconfig",
          permission: "cycleconfig"
        },
        {
          label: "分时方案",
          category: "project",
          type: "menuItem",
          location: "/timesharingConfig",
          permission: "timesharingConfig"
        },
        {
          label: "公共分摊方案配置",
          category: "project",
          type: "menuItem",
          location: "/publicSchemeConfig",
          permission: "publicSchemeConfig"
        },
        {
          label: "损耗分摊配置",
          category: "project",
          type: "menuItem",
          location: "/lossSchemeConfig",
          permission: "lossSchemeConfig"
        },
        {
          label: "维度配置",
          category: "project",
          type: "menuItem",
          location: "/dimensionConfiguration",
          permission: "dimensionConfiguration"
        }
      ]
    }
  ],
  newGuideSteps: []
});
