{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  //cet-simpleSelect的代码片段
  "cet-simpleselect-template": {
    "prefix": "cet-simpleselect-template",
    "body": [
      "<!-- ${1:设置组件唯一识别字段}组件 -->  ",
      "<CetSimpleSelect",
      "v-model=\"CetSimpleSelect_$1.value\" ",
      "v-bind=\"CetSimpleSelect_$1\" ",
      "v-on=\"CetSimpleSelect_$1.event\"",
      "></CetSimpleSelect>"
    ],
    "description": ""
  },
  "cet-simpleselect-data": {
    "prefix": "cet-simpleselect-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件       ",
      "CetSimpleSelect_$1: {",
      "value:\"\",",
      "option: {",
      "key: \"id\",",
      "value: \"id\",",
      " label: \"text\",",
      "disabled: \"disabled\"",
      " },",
      "interface: {",
      "queryMode: \"diff\",",
      "data: [], ",
      "dataConfig: {",
      " queryFunc: \"queryEnum\",",
      "modelLabel: \"mu_chemicalindustry\",",
      "dataIndex: [],",
      "modelList: [],",
      " filters: [],",
      "orders: [],",
      "treeReturnEnable: false,",
      " hasQueryNode: false,",
      " hasQueryId: false",
      " },",
      " queryNode_in: null,",
      " queryId_in: -1,",
      " queryTrigger_in: Date.now(),",
      " dynamicInput: {}",
      " },",
      "event: {",
      " change: this.CetSimpleSelect_$1_change_out",
      " }",
      " }, "
    ],
    "description": ""
  },
  "cet-simpleselect-method": {
    "prefix": "cet-simpleselect-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出",
      "   CetSimpleSelect_$1_change_out(val) { ",
      "    },                                   "
    ],
    "description": ""
  }
}
