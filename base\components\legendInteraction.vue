<template>
  <el-radio-group v-model="currentModel" @change="handlerChange">
    <el-radio-button
      v-for="item in radioList"
      :label="item.label"
      :key="item.label"
    >
      <el-tooltip :content="item.content">
        <omega-icon :symbolId="item.icon" />
      </el-tooltip>
    </el-radio-button>
  </el-radio-group>
</template>
<script>
export default {
  data() {
    return {
      currentModel: 1,
      radioList: [
        {
          label: 1,
          icon: "visibleOrHidden",
          content: $T("点击图例展示/隐藏数据")
        },
        {
          label: 2,
          icon: "position",
          content: $T("点击图例快速找寻该节点")
        },
        {
          label: 3,
          icon: "clearSelection",
          content: $T("点击图例取消节点选中")
        }
      ]
    };
  },
  methods: {
    handlerChange(val) {
      this.$emit("changeModel", val);
    }
  }
};
</script>
