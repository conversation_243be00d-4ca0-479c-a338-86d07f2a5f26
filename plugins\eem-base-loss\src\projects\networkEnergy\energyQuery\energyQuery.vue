<template>
  <div class="h-full flex flex-col">
    <div class="flex-row flex items-center justify-between">
      <div class="flex-row flex items-center">
        <customElSelect
          v-model="ElSelect_type.value"
          v-bind="ElSelect_type"
          v-on="ElSelect_type.event"
          class="mr-J3"
          :prefix_in="$T('分析类型')"
        >
          <ElOption
            v-for="item in ElOption_type.options_in"
            :key="item[ElOption_type.key]"
            :label="item[ElOption_type.label]"
            :value="item[ElOption_type.value]"
            :disabled="item[ElOption_type.disabled]"
          ></ElOption>
        </customElSelect>
        <ElCheckboxGroup
          v-show="ElSelect_type.value === 1"
          class="check-box mr-J3"
          v-model="ElCheckboxGroup_1.value"
          v-bind="ElCheckboxGroup_1"
          v-on="ElCheckboxGroup_1.event"
        >
          <ElCheckbox
            v-for="item in ElCheckboxList_1.options_in"
            :key="item[ElCheckboxList_1.key]"
            :label="item[ElCheckboxList_1.label]"
            :disabled="item[ElCheckboxList_1.disabled]"
          >
            {{ item[ElCheckboxList_1.text] }}
          </ElCheckbox>
        </ElCheckboxGroup>
        <CustomDatePicker
          ref="CustomDatePicker"
          @change="CustomDatePicker_1_change"
          :val="CustomDatePicker_1.queryTime"
          :dataConfig="CustomDatePicker_1.dataConfig"
          :showShortcuts="true"
        ></CustomDatePicker>
      </div>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_export"
        v-on="CetButton_export.event"
        :disable_in="!nodeExist"
      ></CetButton>
    </div>
    <div class="flex-auto mt-J3">
      <div style="height: 100%" v-show="ElSelect_type.value === 1">
        <Tbhb
          v-bind="tbhb"
          :thbSelectList="thbSelectList"
          :CustomDatePicker_1="CustomDatePicker_1"
          :energy_in="energy"
        />
      </div>
      <div style="height: 100%" v-if="ElSelect_type.value === 3">
        <Compare
          v-bind="compare"
          :CustomDatePicker_1="CustomDatePicker_1"
          :clickNode_in="clickNode"
          :checkedNodes_in="checkedNodes"
          :energy_in="energy"
          @updateTree="handlerUpdateTree"
        />
      </div>
    </div>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import CustomDatePicker from "./CustomDatePicker.vue";
import Tbhb from "./tbhb.vue";
import Compare from "./compare.vue";
import omegaI18n from "@omega/i18n";
export default {
  components: {
    CustomDatePicker,
    Tbhb,
    Compare
  },
  props: {
    clickNode: Object,
    checkedNodes: Array,
    energy: Object
  },

  computed: {
    thbSelectList() {
      return this.ElCheckboxList_1.options_in.filter(
        item => !item.disabled && this.ElCheckboxGroup_1.value.includes(item.id)
      );
    },
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    en() {
      return omegaI18n.locale === "en";
    },
    nodeExist() {
      if (this.ElSelect_type.value === 3) {
        return !!this.checkedNodes.length;
      } else {
        return !!this.clickNode?.id;
      }
    }
  },

  data() {
    const en = omegaI18n.locale === "en";
    return {
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            disabled: false,
            text: $T("同比")
          },
          {
            id: 2,
            disabled: false,
            text: $T("环比")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },

      // type组件
      ElSelect_type: {
        value: 1,
        style: {
          width: en ? "260px" : "200px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      // type组件
      ElOption_type: {
        options_in: [
          {
            id: 1,
            text: $T("同比环比")
          },
          {
            id: 3,
            text: $T("节点对比")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CustomDatePicker_1: {
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 14 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 3,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: $T("日"),
              type: "date"
            },
            {
              id: 3,
              text: $T("月"),
              type: "month"
            },
            {
              id: 5,
              text: $T("年"),
              type: "year"
            },
            {
              id: 11,
              text: $T("自定义"),
              type: "date"
            }
          ]
        }
      },
      CetButton_export: {
        visible_in: true,
        // disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      unit: "",
      tbhb: {
        queryBody_in: null,
        queryTrigger_in: new Date().getTime()
      },
      compare: {
        queryBody_in: null,
        queryTrigger_in: new Date().getTime()
      }
    };
  },
  watch: {},

  methods: {
    handlerUpdateTree(type, name) {
      this.$emit("updateTree", type, name);
    },
    nodeClick_out(val) {
      if (val) {
        if (this.ElSelect_type.value === 1) {
          this.getChartData1();
        }
      }
    },
    ElCheckboxGroup_1_change_out() {
      if (this.ElSelect_type.value === 1) {
        this.getChartData1();
      }
      if (this.ElSelect_type.value === 3) {
        this.getChartData3();
      }
    },

    ElSelect_type_change_out(val) {
      if (!val) {
        return;
      }
      this.$emit("changeCheckbox", this.ElSelect_type.value === 3);
      this.CustomDatePicker_1.dataConfig.cycle = 3;
      this.CustomDatePicker_1.dataConfig.type = [
        {
          id: 1,
          text: $T("日"),
          type: "date"
        },
        {
          id: 3,
          text: $T("月"),
          type: "month"
        },
        {
          id: 5,
          text: $T("年"),
          type: "year"
        },
        {
          id: 11,
          text: $T("自定义"),
          type: "date"
        }
      ];
      this.updateTHBGroup();
      if (val === 1) {
        // this.getChartData1();
        this.$refs.CustomDatePicker.init();
        if (this.clickNode) {
          setTimeout(() => {
            this.$emit("setTree1Node", this._.cloneDeep(this.clickNode));
          });
        }
      } else if (val === 3) {
        if (this.clickNode) {
          let obj = this._.cloneDeep(this.clickNode);
          this.$refs.CustomDatePicker.init();
          setTimeout(() => {
            this.$emit("setTree2Node", obj);
          });
        }
      }
    },
    //导出
    CetButton_export_statusTrigger_out() {
      var urlStr = "",
        params = {};
      if (this.ElSelect_type.value === 1) {
        urlStr =
          "/eembaseloss/eem-base/loss/v1/pipe/energy/export?analysisType=1";
        params = this.getParams(1);
      } else if (this.ElSelect_type.value === 3) {
        urlStr =
          "/eembaseloss/eem-base/loss/v1/pipe/energy/export?analysisType=3";
        params = this.getParams(3);
      }
      if (!params) {
        return;
      }
      common.downExcel(urlStr, params);
    },
    //同比环比
    getChartData1() {
      var queryBody = this.getParams(1);
      if (!queryBody || !queryBody.energyType) {
        this.tbhb.queryBody_in = null;
        this.tbhb.queryTrigger_in = new Date().getTime();
        return;
      }

      this.tbhb.queryBody_in = queryBody;
      this.tbhb.queryTrigger_in = new Date().getTime();
    },
    //节点对比
    getChartData3() {
      var queryBody = this.getParams(3);
      if (!queryBody || queryBody.node.length == 0 || !queryBody.energyType) {
        this.compare.queryBody_in = null;
        this.compare.queryTrigger_in = new Date().getTime();
        return;
      }
      this.compare.queryBody_in = queryBody;
      this.compare.queryTrigger_in = new Date().getTime();
    },
    //获取图表参数
    getParams(type = 1) {
      if (!this.clickNode) {
        return null;
      }
      let params = {};
      var queryTime = this.CustomDatePicker_1.queryTime,
        startTime = queryTime.startTime,
        endTime = queryTime.endTime,
        cycle = queryTime.cycle;
      var energyType = this.energy.id;
      params.cycle = cycle;
      params.endTime = endTime;
      params.energyType = energyType;
      params.startTime = startTime;
      if (type === 1) {
        var CheckedArray = this.ElCheckboxGroup_1.value || [],
          len = CheckedArray.length,
          queryType;
        if (len === 0) {
          queryType = 0;
        } else if (len === 1) {
          queryType = CheckedArray[0];
        } else if (len === 2) {
          queryType = 3;
        }
        if (cycle === 17) {
          queryType = 1;
        } else if (cycle === 20) {
          queryType = 0;
        }
        params.queryType = queryType;
        params.node = [
          {
            id: this.clickNode.id,
            modelLabel: this.clickNode.modelLabel,
            name: this.clickNode.name
          }
        ];
        return params;
      } else if (type === 3) {
        params.node = this.getSelectNode();
        return params;
      }
      return {};
    },
    //节点对比中获取选中节点，进行分类
    getSelectNode() {
      var list = this.checkedNodes || [];
      return list.map(i => {
        return {
          id: i.id,
          modelLabel: i.modelLabel,
          name: i.name
        };
      });
    },

    //时间组件变化
    CustomDatePicker_1_change(val) {
      if (
        this.CustomDatePicker_1.queryTime.cycle !== val.cycle &&
        val.cycle === 20
      ) {
        this.ElCheckboxGroup_1.value = [];
      }
      this.CustomDatePicker_1.queryTime = val;

      this.updateTHBGroup();

      if (this.ElSelect_type.value === 1) {
        this.getChartData1();
      } else if (this.ElSelect_type.value === 3) {
        this.getChartData3();
      }
    },

    updateTHBGroup() {
      let cycle = this.CustomDatePicker_1.queryTime.cycle;

      if (this.ElSelect_type.value === 1) {
        this.ElCheckboxList_1.options_in = [
          {
            id: 1,
            disabled: cycle === 20,
            text: $T("同比")
          },
          {
            id: 2,
            disabled: cycle === 17 || cycle === 20,
            text: $T("环比")
          }
        ];
      }
    }
  },
  activated() {
    this.ElSelect_type.value = 1;
  }
};
</script>
<style lang="scss" scoped>
.header {
  @include margin_top(J2);
  @include margin_bottom(J2);
}
.head-label {
  @include line_height(H2);
  @include font_size(H2);
  font-weight: bold;
}
.check-box :deep(.el-checkbox) {
  margin-right: var(--J1);
  &:last-of-type {
    margin-right: 0;
  }
}
</style>
