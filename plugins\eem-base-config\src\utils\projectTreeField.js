import common from "eem-base/utils/common";
import Vue from "vue";
const vue = new Vue();
/*
{
  name: $T("节点类型"),表单标题
  nameTooltip: $T("节点类型"),标题浮动提示
  propertyLabel: "modelLabel",字段名称
  relatedLabel: ["sectionupperlimit"],与字段相关的其他字段，可能不在表单里面，区间选择:如开始绑定在propertyLabel，结束就放在此处，区间选择只支持配置一个关联字段;
  type: "nodeType", 字段类型string,numberInt,numberFloat，datePicker，enums，pic，rangeSelection，boolean   可以自己再加类型，需要在新增和详情中对新增类型做兼容
  detailMinName: $T("中载区间下限"),type为rangeSelection时，详情需要将最大最小分开展示，需要配置其名称
  detailMaxName: $T("中载区间上限"),type为rangeSelection时，详情需要将最大最小分开展示，需要配置其名称
  pickerOptions: common.pickerOptions_laterThanYesterd,配置了是时间类型对应的时间选择器配置
  unit: "",单位
  defaultValue: 111,新建时默认回填数据
  saveDefaultValue: 0.04,如果保存时未填写数据则以此数据进行保存
  noEdit: true,字段是否允许在编辑状态下不可编辑
  rules: [
    {
      required: true,
      message: $T("请选择节点类型"),
      trigger: ["blur", "change"]
    }
  ],表单检验规则
  validator: formData => {
    return [
      {
        required: false,
        validator: checkBusbarSeg.bind(formData),
        trigger: ["blur", "change"]
      }
    ];
  } // 自定义校验
}
*/

/*
自定义枚举配置示例
  customEnums支持异步或者数组
  hvdcList = {
    name: $T("供电关联设备"),
    propertyLabel: "supplytoid",
    type: "customEnums",
    customEnums: async () => {
      const res = await customApi.projectManageQuerydata({
        modelLabel: "hvdc"
      });
      const data = res?.data ?? [];
      const list = data.map(item => {
        return {
          id: item.id,
          text: item.name
        };
      });
      return list;
    }
  },

*/

const checkBusbarSeg = function (rule, value, callback) {
  if (
    this.busbarsegi &&
    this.busbarsegii &&
    this.busbarsegi === this.busbarsegii
  ) {
    return callback(new Error($T("母线不能重复")));
  } else {
    callback();
  }
};
const checkMaterial = function (rule, value, callback) {
  if (this.pipetype !== 5 && !this.energytype) {
    return callback(new Error($T("请选择物料类型")));
  }
  callback();
};
const nodeType = {
    name: $T("节点类型"),
    propertyLabel: "modelLabel",
    type: "nodeType",
    rules: [
      {
        required: true,
        message: $T("请选择节点类型"),
        trigger: ["blur", "change"]
      }
    ]
  },
  name = {
    name: $T("节点名称"),
    propertyLabel: "name",
    type: "string",
    rules: [
      {
        required: true,
        message: $T("请输入名称"),
        trigger: ["blur", "change"]
      },
      common.check_name,
      common.pattern_name
    ]
  },
  code = {
    name: $T("编号"),
    propertyLabel: "code",
    type: "string",
    rules: [common.check_name, common.pattern_name]
  },
  area = {
    name: $T("面积"),
    propertyLabel: "area",
    type: "numberFloat",
    unit: "m²"
  },
  floorarea = {
    name: $T("面积"),
    propertyLabel: "floorarea",
    type: "numberFloat",
    unit: "m²"
  },
  population = {
    name: $T("人数"),
    propertyLabel: "population",
    type: "numberInt",
    unit: $T("人")
  },
  pic = {
    name: $T("主图"),
    propertyLabel: "pic",
    type: "pic"
  },
  latitude = {
    name: $T("经度"),
    propertyLabel: "latitude",
    type: "latitude"
  },
  longitude = {
    name: $T("纬度"),
    propertyLabel: "longitude",
    type: "longitude"
  },
  address = {
    name: $T("地址"),
    propertyLabel: "address",
    type: "string",
    rules: [common.check_stringLessThan255]
  },
  cooperativedeadline = {
    name: $T("合作截止时间"),
    propertyLabel: "cooperativedeadline",
    type: "datePicker",
    pickerOptions: common.pickerOptions_laterThanYesterd
  },
  commissiondate = {
    name: $T("投用日期"),
    propertyLabel: "commissiondate",
    type: "datePicker"
  },
  voltagelevel = {
    name: $T("电压等级"),
    propertyLabel: "voltagelevel",
    type: "enums",
    enumLabel: "voltagelevel"
  },
  powerroomtype = {
    name: $T("配电室类型"),
    propertyLabel: "powerroomtype",
    type: "enums",
    enumLabel: "powerroomtype"
  },
  brand = {
    name: $T("品牌"),
    propertyLabel: "brand",
    type: "string",
    rules: [common.check_name, common.pattern_name]
  },
  locationField = {
    name: $T("安装位置"),
    propertyLabel: "location",
    type: "string",
    rules: [common.check_name, common.check_stringLessThan255]
  },
  model = {
    name: $T("型号"),
    propertyLabel: "model",
    type: "string",
    rules: [common.check_name, common.pattern_name]
  },
  manufacturedate = {
    name: $T("出厂日期"),
    propertyLabel: "manufacturedate",
    type: "datePicker"
  },
  manufactor = {
    name: $T("生产厂家"),
    propertyLabel: "manufactor",
    type: "string",
    rules: [common.check_name, common.pattern_name]
  },
  documentField = {
    name: $T("相关文档"),
    propertyLabel: "document",
    type: "document"
  },
  energytype = {
    name: $T("能源类型"),
    propertyLabel: "energytype",
    type: "energytype",
    rules: [
      {
        required: true,
        message: $T("请选择能源类型"),
        trigger: ["blur", "change"]
      }
    ]
  },
  maintenanceperiod = {
    name: $T("检修周期"),
    propertyLabel: "maintenanceperiod",
    type: "numberInt",
    unit: $T("天")
  },
  lastoverhauldate = {
    name: $T("上次检修日期"),
    propertyLabel: "lastoverhauldate",
    type: "datePicker",
    pickerOptions: common.pickerOptions_earlierThanYesterd11
  },
  lastpretestdate = {
    name: $T("上次预检日期"),
    propertyLabel: "lastpretestdate",
    type: "datePicker",
    pickerOptions: common.pickerOptions_earlierThanYesterd11
  },
  nextoverhauldate = {
    name: $T("下次检修日期"),
    propertyLabel: "nextoverhauldate",
    type: "datePicker",
    pickerOptions: common.pickerOptions_laterThanYesterd11
  },
  ownship = {
    name: $T("资产归属"),
    propertyLabel: "ownship",
    type: "string"
  },
  deviceclassification = {
    name: $T("设备归类"),
    propertyLabel: "deviceclassification",
    type: "deviceclassification"
  },
  pipefunctiontype = {
    name: $T("管道功能类型"),
    propertyLabel: "pipefunctiontype",
    type: "enums",
    enumLabel: "pipefunctiontype"
  },
  pipepositiontype = {
    name: $T("管道位置类型"),
    propertyLabel: "pipepositiontype",
    type: "enums",
    enumLabel: "pipepositiontype"
  },
  ratedcapacity = {
    name: $T("额定容量"),
    propertyLabel: "ratedcapacity",
    type: "numberFloat",
    unit: "kVA"
  },
  linefunctiontype = {
    name: $T("线功能"),
    propertyLabel: "linefunctiontype",
    type: "enums",
    enumLabel: "linefunctiontype",
    filterElOption: options => {
      const ordinarymeasureline = options.find(i => i.id === 7);
      const ingoingline = options.find(i => i.id === 4);
      const list = [];
      ordinarymeasureline && list.push(ordinarymeasureline);
      ingoingline && list.push(ingoingline);
      list.push(...options.filter(i => ![7, 4].includes(i.id)));
      return list;
    }
  },
  coiltype = {
    name: $T("圈变电压类型"),
    propertyLabel: "coiltype",
    type: "enums",
    enumLabel: "coiltype"
  },
  noloadcurrent = {
    name: $T("空载电流百分比"),
    propertyLabel: "noloadcurrent",
    type: "numberFloat"
  },
  noloadloss = {
    name: $T("空载损耗"),
    propertyLabel: "noloadloss",
    type: "numberFloat",
    unit: "kW"
  },
  phasetype = {
    name: $T("相别类型"),
    propertyLabel: "phasetype",
    type: "enums",
    enumLabel: "phasetype"
  },
  ptratio = {
    name: $T("PT变比"),
    propertyLabel: "ptratio",
    type: "string"
  },
  ratedcurrent = {
    name: $T("额定电流"),
    propertyLabel: "ratedcurrent",
    type: "numberFloat",
    unit: "A"
  },
  ratedvoltage = {
    name: $T("额定电压"),
    propertyLabel: "ratedvoltage",
    type: "numberFloat",
    unit: "kV"
  },
  shortcircuitimpedance = {
    name: $T("短路阻抗"),
    propertyLabel: "shortcircuitimpedance",
    type: "numberFloat",
    unit: "Ω"
  },
  shortcircuitloss = {
    name: $T("短路损耗"),
    propertyLabel: "shortcircuitloss",
    type: "numberFloat",
    unit: "kW"
  },
  shortcircuitvoltage = {
    name: $T("短路电压百分比"),
    propertyLabel: "shortcircuitvoltage",
    type: "numberFloat"
  },
  status = {
    name: $T("设备启动状态"),
    propertyLabel: "status",
    type: "enums",
    enumLabel: "deviceenablestatus"
  },
  warrantydate = {
    name: $T("质保期限"),
    propertyLabel: "warrantydate",
    type: "datePicker"
  },
  transformerlevel = {
    name: $T("变压器等级"),
    propertyLabel: "transformerlevel",
    type: "enums",
    enumLabel: "transformerlevel"
  },
  reactiveeconomicequivalent = {
    name: $T("无功经济当量"),
    nameTooltip: $T(
      "根据变压器在电网中的位置取值，一般35kV配电变压器取值范围为0.02~0.05,10kV配电变压器的取值范围为0.05~0.10。当变压器负载侧的功率因数已补偿至0.9及以上时取0.02~0.04。"
    ),
    propertyLabel: "reactiveeconomicequivalent",
    type: "numberFloat",
    saveDefaultValue: 0.04
  },
  sectionlowlimit = {
    name: $T("中载区间上下限"),
    propertyLabel: "sectionlowlimit",
    relatedLabel: ["sectionupperlimit"],
    type: "rangeSelection",
    detailMinName: $T("中载区间下限"),
    detailMaxName: $T("中载区间上限")
  },
  loadclass = {
    name: $T("供电负载类型"),
    propertyLabel: "loadclass",
    type: "enums",
    enumLabel: "loadclass"
  },
  ctpolarity = {
    name: $T("CT极性"),
    propertyLabel: "ctpolarity",
    type: "enums",
    enumLabel: "ctpolarity"
  },
  linesegmentwithswitch_id = {
    name: $T("所属低压馈电柜ID"),
    propertyLabel: "linesegmentwithswitch_id",
    type: "numberInt"
  },
  busbarmarktype = {
    name: $T("母线标记类型"),
    propertyLabel: "busbarmarktype",
    type: "enums",
    enumLabel: "busbarmarktype"
  },
  materialtype = {
    name: $T("材质"),
    propertyLabel: "materialtype",
    type: "enums",
    enumLabel: "materialtype"
  },
  busbarsegi = {
    name: $T("I段母线"),
    propertyLabel: "busbarsegi",
    type: "busbarseg",
    validator: formData => {
      return [
        {
          required: false,
          validator: checkBusbarSeg.bind(formData),
          trigger: ["blur", "change"]
        }
      ];
    }
  },
  busbarsegii = {
    name: $T("II段母线"),
    propertyLabel: "busbarsegii",
    type: "busbarseg",
    validator: formData => {
      return [
        {
          required: false,
          validator: checkBusbarSeg.bind(formData),
          trigger: ["blur", "change"]
        }
      ];
    }
  },
  asset = {
    name: $T("资产编码"),
    propertyLabel: "asset",
    type: "string"
  },
  operationstatus = {
    name: $T("设备运行状态"),
    propertyLabel: "operationstatus",
    type: "enums",
    enumLabel: "deviceoperationstatus"
  },
  conversiontime = {
    name: $T("转换时间"),
    propertyLabel: "conversiontime",
    type: "datePicker"
  },
  inputvoltage = {
    name: $T("输入电压"),
    propertyLabel: "inputvoltage",
    type: "numberFloat",
    unit: "V"
  },
  inverterefficiency = {
    name: $T("逆变器效率"),
    propertyLabel: "inverterefficiency",
    type: "numberFloat"
  },
  machineefficiency = {
    name: $T("整机效率"),
    propertyLabel: "machineefficiency",
    type: "numberFloat"
  },
  outputvoltage = {
    name: $T("输出电压"),
    propertyLabel: "outputvoltage",
    type: "numberFloat",
    unit: "V"
  },
  upsstructure = {
    name: $T("逆变器结构"),
    propertyLabel: "upsstructure",
    type: "string"
  },
  devicetype = {
    name: $T("设备类型"),
    propertyLabel: "devicetype",
    type: "enums",
    enumLabel: "devicetypeenum"
  },
  powerdiscabinettype = {
    name: $T("配电柜类型"),
    propertyLabel: "powerdiscabinettype",
    type: "enums",
    enumLabel: "powerdiscabinettype"
  },
  prodstandard = {
    name: $T("生产标准"),
    propertyLabel: "prodstandard",
    type: "string"
  },
  protlevel = {
    name: $T("防护等级"),
    propertyLabel: "protlevel",
    type: "string"
  },
  switchfunctiontype = {
    name: $T("开关柜的功能"),
    propertyLabel: "switchfunctiontype",
    type: "enums",
    enumLabel: "switchfunctiontype"
  },
  powerproperty = {
    name: $T("电源性质"),
    propertyLabel: "powerproperty",
    type: "enums",
    enumLabel: "powerproperty"
  },
  distributionpurpose = {
    name: $T("配电用途"),
    propertyLabel: "distributionpurpose",
    type: "enums",
    enumLabel: "distributionpurpose"
  },
  resistance = {
    name: $T("电阻"),
    propertyLabel: "resistance",
    type: "numberFloat"
  },
  airconditionsystemtype = {
    name: $T("空调系统类型"),
    propertyLabel: "airconditionsystemtype",
    type: "enums",
    enumLabel: "airconditionsystemtype"
  },
  devicesize = {
    name: $T("机组尺寸"),
    propertyLabel: "devicesize",
    type: "string"
  },
  deviceweight = {
    name: $T("机组重量"),
    propertyLabel: "deviceweight",
    type: "numberFloat",
    unit: $T("吨")
  },
  coolingmethod = {
    name: $T("制冷方式"),
    propertyLabel: "coolingmethod",
    type: "enums",
    enumLabel: "coolingmethod"
  },
  engineattr = {
    name: $T("机组属性"),
    propertyLabel: "engineattr",
    type: "enums",
    enumLabel: "coldwaterengineattr"
  },
  coolingcyclewater = {
    name: $T("冷却循环水量"),
    propertyLabel: "coolingcyclewater",
    type: "numberFloat",
    unit: "m³/h"
  },
  minconstantoutwatertemp = {
    name: $T("恒定出水温度（℃）"),
    propertyLabel: "minconstantoutwatertemp",
    relatedLabel: ["maxconstantoutwatertemp"],
    type: "rangeSelection",
    detailMinName: $T("最小恒定出水温度（℃）"),
    detailMaxName: $T("最大恒定出水温度（℃）")
  },
  mincoolingcycletemp = {
    name: $T("冷却循环水温度（℃）"),
    propertyLabel: "mincoolingcycletemp",
    relatedLabel: ["maxcoolingcycletemp"],
    type: "rangeSelection",
    detailMinName: $T("冷却循环水最小温度（℃）"),
    detailMaxName: $T("冷却循环水最大温度（℃）")
  },
  minrefrigeratingcycletemp = {
    name: $T("冷冻循环水温度（℃）"),
    propertyLabel: "minrefrigeratingcycletemp",
    relatedLabel: ["maxrefrigeratingcycletemp"],
    type: "rangeSelection",
    detailMinName: $T("冷冻循环水最小温度（℃）"),
    detailMaxName: $T("冷冻循环水最大温度（℃）")
  },
  ratedmotorpower = {
    name: $T("额定功率"),
    propertyLabel: "ratedmotorpower",
    type: "numberFloat",
    unit: "kW"
  },
  ratedrefrigeration = {
    name: $T("额定制冷量"),
    propertyLabel: "ratedrefrigeration",
    type: "numberFloat",
    unit: "kW"
  },
  refrigeratingcyclewater = {
    name: $T("冷冻循环水量"),
    propertyLabel: "refrigeratingcyclewater",
    type: "numberFloat",
    unit: "m³/h"
  },
  refrigerationmediumtype = {
    name: $T("制冷介质"),
    propertyLabel: "refrigerationmediumtype",
    type: "enums",
    enumLabel: "refrigerationmediumtype"
  },
  computername = {
    name: $T("计算机名称"),
    propertyLabel: "computername",
    type: "string"
  },
  cpuload = {
    name: $T("cpu负载"),
    propertyLabel: "cpuload",
    type: "numberFloat"
  },
  diskload = {
    name: $T("磁盘负载"),
    propertyLabel: "diskload",
    type: "numberFloat"
  },
  diskremainingcapacity = {
    name: $T("磁盘剩余空间"),
    propertyLabel: "diskremainingcapacity",
    type: "numberFloat"
  },
  ip = {
    name: $T("ip"),
    propertyLabel: "ip",
    type: "string"
  },
  ip2 = {
    name: $T("ip2"),
    propertyLabel: "ip2",
    type: "string"
  },
  isbackgroudserver = {
    name: $T("是否后台服务器"),
    propertyLabel: "isbackgroudserver",
    type: "boolean"
  },
  isconfigserver = {
    name: $T("是否是配置服务"),
    propertyLabel: "isconfigserver",
    type: "boolean"
  },
  isfrontserver = {
    name: $T("是否前台服务器"),
    propertyLabel: "isfrontserver",
    type: "boolean"
  },
  standbymode = {
    name: $T("主备模式"),
    propertyLabel: "standbymode",
    type: "enums",
    enumLabel: "standmode"
  },
  standbyserverid = {
    name: $T("备用服务器的id"),
    propertyLabel: "standbyserverid",
    type: "numberInt"
  },
  istimeserver = {
    name: $T("是否时间服务器"),
    propertyLabel: "istimeserver",
    type: "boolean"
  },
  memoryload = {
    name: $T("内存负载"),
    propertyLabel: "memoryload",
    type: "numberFloat"
  },
  timezone = {
    name: $T("时区"),
    propertyLabel: "timezone",
    type: "enums",
    enumLabel: "timezone"
  },
  attribute1 = {
    name: $T("attribute1"),
    propertyLabel: "attribute1",
    type: "numberInt"
  },
  attribute2 = {
    name: $T("attribute2"),
    propertyLabel: "attribute2",
    type: "numberInt"
  },
  attribute3 = {
    name: $T("attribute3"),
    propertyLabel: "attribute3",
    type: "numberInt"
  },
  e = {
    name: $T("e"),
    propertyLabel: "e",
    type: "numberInt"
  },
  description = {
    name: $T("描述"),
    propertyLabel: "description",
    type: "textarea",
    span: 24
  },
  successrate = {
    name: $T("通信成功率"),
    propertyLabel: "successrate",
    type: "numberFloat"
  },
  communicationstatus = {
    name: $T("通信状态"),
    propertyLabel: "communicationstatus",
    type: "enums",
    enumLabel: "communicationstatus"
  },
  gatewaymodel = {
    name: $T("通信管理机型号"),
    propertyLabel: "gatewaymodel",
    type: "enums",
    enumLabel: "gatewaymodel"
  },
  networkaddress1 = {
    name: $T("地址1"),
    propertyLabel: "networkaddress1",
    type: "string"
  },
  networkaddress2 = {
    name: $T("地址2"),
    propertyLabel: "networkaddress2",
    type: "string"
  },
  networkaddress3 = {
    name: $T("地址3"),
    propertyLabel: "networkaddress3",
    type: "string"
  },
  networkaddress4 = {
    name: $T("地址4"),
    propertyLabel: "networkaddress4",
    type: "string"
  },
  standbygatewayid = {
    name: $T("备用管理机的id"),
    propertyLabel: "standbygatewayid",
    type: "numberInt"
  },
  communicationmode = {
    name: $T("通信方式"),
    propertyLabel: "communicationmode",
    type: "enums",
    enumLabel: "communicationmode"
  },
  boudrate = {
    name: $T("波特率"),
    propertyLabel: "boudrate",
    type: "numberInt"
  },
  circuitid = {
    name: $T("回路ID"),
    propertyLabel: "circuitid",
    type: "numberInt"
  },
  circuitname = {
    name: $T("回路名称"),
    propertyLabel: "circuitname",
    type: "string"
  },
  com = {
    name: $T("串口号"),
    propertyLabel: "com",
    type: "string"
  },
  commid = {
    name: $T("通信id"),
    propertyLabel: "commid",
    type: "numberInt"
  },
  databits = {
    name: $T("数据位"),
    propertyLabel: "databits",
    type: "numberInt"
  },
  deviceid = {
    name: $T("表计ID"),
    propertyLabel: "deviceid",
    type: "numberInt"
  },
  measuretype = {
    name: $T("测量类型"),
    propertyLabel: "measuretype",
    type: "string"
  },
  metermodel = {
    name: $T("表计型号"),
    propertyLabel: "metermodel",
    type: "string"
  },
  metertype = {
    name: $T("表计类型"),
    propertyLabel: "metertype",
    type: "enums",
    enumLabel: "metertype"
  },
  networkaddress = {
    name: $T("网络地址"),
    propertyLabel: "networkaddress",
    type: "string"
  },
  paritycheck = {
    name: $T("奇偶校验"),
    propertyLabel: "paritycheck",
    type: "enums",
    enumLabel: "paritycheck"
  },
  protocalname = {
    name: $T("规约类型名称"),
    propertyLabel: "protocalname",
    type: "string"
  },
  protocaltype = {
    name: $T("通信规约"),
    propertyLabel: "protocaltype",
    type: "enums",
    enumLabel: "protocaltype"
  },
  protocalversion = {
    name: $T("规约版本"),
    propertyLabel: "protocalversion",
    type: "string"
  },
  ratio = {
    name: $T("变比"),
    propertyLabel: "ratio",
    type: "string"
  },
  stopbit = {
    name: $T("停止位"),
    propertyLabel: "stopbit",
    type: "numberInt"
  },
  rollover = {
    name: $T("翻转量"),
    propertyLabel: "rollover",
    type: "numberFloat"
  },
  bandwidth = {
    name: $T("背板带宽"),
    propertyLabel: "bandwidth",
    type: "numberFloat"
  },
  netport = {
    name: $T("网口数量"),
    propertyLabel: "netport",
    type: "numberInt"
  },
  opticalport = {
    name: $T("光口数量"),
    propertyLabel: "opticalport",
    type: "numberInt"
  },
  transmissionrate = {
    name: $T("传输速率"),
    propertyLabel: "transmissionrate",
    type: "enums",
    enumLabel: "transmissionrate"
  },
  interchangertype = {
    name: $T("交换机类型"),
    propertyLabel: "interchangertype",
    type: "enums",
    enumLabel: "interchangertype"
  },
  producttype = {
    name: $T("产品类型"),
    propertyLabel: "producttype",
    type: "string"
  },
  elecport = {
    name: $T("电口数量"),
    propertyLabel: "elecport",
    type: "numberInt"
  },
  frequencytype = {
    name: $T("是否变频"),
    propertyLabel: "frequencytype",
    type: "enums",
    enumLabel: "frequencytype"
  },
  minworkingfrequency = {
    name: $T("工作频率（Hz）"),
    propertyLabel: "minworkingfrequency",
    relatedLabel: ["maxworkingfrequency"],
    type: "rangeSelection",
    detailMinName: $T("最小工作频率（Hz）"),
    detailMaxName: $T("最大工作频率（Hz）")
  },
  ratedheatexchange = {
    name: $T("额定热换量"),
    propertyLabel: "ratedheatexchange",
    type: "numberFloat"
  },
  valvetype = {
    name: $T("阀门类型"),
    propertyLabel: "valvetype",
    type: "enums",
    enumLabel: "valvetype"
  },
  changedate = {
    name: $T("更换日期"),
    propertyLabel: "changedate",
    type: "datePicker"
  },
  functiontype = {
    name: $T("功能类型"),
    propertyLabel: "functiontype",
    type: "enums",
    enumLabel: "pumpfunctiontype"
  },
  ratedblankingtimes = {
    name: $T("额定冲次"),
    propertyLabel: "ratedblankingtimes",
    type: "numberInt",
    unit: $T("次/min")
  },
  ratedstroke = {
    name: $T("额定冲程"),
    propertyLabel: "ratedstroke",
    type: "numberFloat",
    unit: "mm"
  },
  physicaltype = {
    name: $T("泵类型"),
    propertyLabel: "physicaltype",
    type: "enums",
    enumLabel: "pumptype"
  },
  electricalmachinerymanufacturer = {
    name: $T("电机厂家"),
    propertyLabel: "electricalmachinerymanufacturer",
    type: "string"
  },
  operationmediumtype = {
    name: $T("工作介质类型"),
    propertyLabel: "operationmediumtype",
    type: "enums",
    enumLabel: "pumpoperationmediumtype"
  },
  motortype = {
    name: $T("电机型号"),
    propertyLabel: "motortype",
    type: "string"
  },
  ratedmotorcurrent = {
    name: $T("电机额定电流"),
    propertyLabel: "ratedmotorcurrent",
    type: "numberFloat",
    unit: "mm"
  },
  ratedmotorpowerfactor = {
    name: $T("电机额定功率因数"),
    propertyLabel: "ratedmotorpowerfactor",
    type: "numberFloat"
  },
  ratedmotorefficiency = {
    name: $T("电机额定效率"),
    propertyLabel: "ratedmotorefficiency",
    type: "numberFloat",
    unit: "%"
  },
  ratedlift = {
    name: $T("额定扬程"),
    propertyLabel: "ratedlift",
    type: "numberFloat",
    unit: "m"
  },
  coolingmode = {
    name: $T("冷却方式"),
    propertyLabel: "coolingmode",
    type: "string"
  },
  rateddischarge = {
    name: $T("额定流量"),
    propertyLabel: "rateddischarge",
    type: "numberFloat",
    unit: "m³/h"
  },
  lubricationmode = {
    name: $T("润滑方式"),
    propertyLabel: "lubricationmode",
    type: "string"
  },
  ratedspeed = {
    name: $T("额定转速"),
    propertyLabel: "ratedspeed",
    type: "numberFloat",
    unit: "r/min"
  },
  usagestate = {
    name: $T("使用状态"),
    propertyLabel: "usagestate",
    type: "enums",
    enumLabel: "usagestate"
  },
  ratedworkingpressure = {
    name: $T("额定工作压力"),
    propertyLabel: "ratedworkingpressure",
    type: "numberFloat",
    unit: "Mpa"
  },
  isscrap = {
    name: $T("是否报废"),
    propertyLabel: "isscrap",
    type: "boolean"
  },
  effevaluation = {
    name: $T("效率节能评价值"),
    propertyLabel: "effevaluation",
    type: "numberFloat"
  },
  efflimit = {
    name: $T("效率限定值"),
    propertyLabel: "efflimit",
    type: "numberFloat"
  },
  pumpcycletype = {
    name: $T("一次泵或二次泵"),
    propertyLabel: "pumpcycletype",
    type: "enums",
    enumLabel: "pumpcycletype"
  },
  scrapdate = {
    name: $T("报废日期"),
    propertyLabel: "scrapdate",
    type: "datePicker"
  },
  aircompressorattr = {
    name: $T("空压机组属性"),
    propertyLabel: "aircompressorattr",
    type: "enums",
    enumLabel: "aircompressorattr"
  },
  colddrymachineattr = {
    name: $T("冷干机属性"),
    propertyLabel: "colddrymachineattr",
    type: "enums",
    enumLabel: "aircompressorattr"
  },
  dryingagent = {
    name: $T("干燥剂"),
    propertyLabel: "dryingagent",
    type: "string"
  },
  dryingmachineattr = {
    name: $T("干燥机属性"),
    propertyLabel: "dryingmachineattr",
    type: "enums",
    enumLabel: "aircompressorattr"
  },
  boilertype = {
    name: $T("锅炉类型"),
    propertyLabel: "boilertype",
    type: "enums",
    enumLabel: "boilertype"
  },
  fueltype = {
    name: $T("燃料类型"),
    propertyLabel: "fueltype",
    type: "energytype"
  },
  multiplyingpower = {
    name: $T("倍率"),
    propertyLabel: "multiplyingpower",
    type: "numberFloat",
    unit: ""
  },
  takttime = {
    name: $T("生产节拍"),
    propertyLabel: "takttime",
    type: "numberFloat",
    unit: ""
  },
  allowtime = {
    name: $T("允许时间"),
    propertyLabel: "allowtime",
    type: "datePicker",
    unit: ""
  },
  linebodytype = {
    name: $T("空耗线体类型"),
    propertyLabel: "linebodytype",
    type: "enums",
    enumLabel: "linebodytype",
    unit: ""
  },
  pipetype = {
    name: $T("管道类型"),
    propertyLabel: "pipetype",
    type: "enums",
    enumLabel: "pipetype",
    noEdit: true,
    rules: [
      {
        required: true,
        message: $T("请选择管道类型"),
        trigger: ["blur", "change"]
      }
    ]
  },
  rawmaterialtype = {
    name: $T("物料类型"),
    propertyLabel: "energytype",
    type: "enums",
    enumLabel: "rawmaterialtype",
    validator: formData => {
      return [
        {
          required: true,
          validator: checkMaterial.bind(formData),
          trigger: ["blur", "change"]
        }
      ];
    }
  },
  burnproducemattertype = {
    name: $T("物料类型"),
    propertyLabel: "energytype",
    type: "enums",
    enumLabel: "burnproducemattertype",
    validator: formData => {
      return [
        {
          required: true,
          validator: checkMaterial.bind(formData),
          trigger: ["blur", "change"]
        }
      ];
    }
  },
  product = {
    name: $T("物料类型"),
    propertyLabel: "energytype",
    type: "product",
    validator: formData => {
      return [
        {
          required: true,
          validator: checkMaterial.bind(formData),
          trigger: ["blur", "change"]
        }
      ];
    }
  },
  ratedpower = {
    name: $T("额定功率"),
    propertyLabel: "ratedpower",
    type: "numberFloat",
    unit: ""
  },
  maxflow = {
    name: $T("最大流量"),
    propertyLabel: "maxflow",
    type: "numberFloat",
    unit: ""
  };
// IT机房
const room2 = [
  {
    modelLabel: "computer",
    node_fields: [
      nodeType,
      name,
      computername,
      cpuload,
      diskload,
      ip,
      ip2,
      memoryload,
      timezone,
      model,
      isbackgroudserver,
      isconfigserver,
      isfrontserver,
      diskremainingcapacity,
      standbymode,
      standbyserverid,
      istimeserver,
      attribute1,
      attribute2,
      attribute3,
      e,
      description,
      pic
    ]
  },
  {
    modelLabel: "gateway",
    node_fields: [
      nodeType,
      name,
      standbymode,
      successrate,
      model,
      communicationstatus,
      gatewaymodel,
      networkaddress1,
      networkaddress2,
      networkaddress3,
      networkaddress4,
      standbygatewayid,
      description,
      pic
    ]
  },
  {
    modelLabel: "meter",
    node_fields: [
      nodeType,
      name,
      circuitid,
      circuitname,
      com,
      commid,
      energytype,
      deviceid,
      protocaltype,
      networkaddress,
      metertype,
      communicationmode,
      metermodel,
      boudrate,
      paritycheck,
      databits,
      measuretype,
      protocalname,
      protocalversion,
      ratio,
      stopbit,
      successrate,
      rollover,
      description,
      pic
    ]
  },
  {
    modelLabel: "interchanger",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      interchangertype,
      { ...ratedvoltage, unit: "V" },
      ratedcurrent,
      bandwidth,
      transmissionrate,
      netport,
      opticalport,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      voltagelevel,
      code,
      brand,
      operationstatus,
      { ...ratedcapacity, name: $T("额定功率"), unit: "kW" },
      asset,
      warrantydate,
      producttype,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "photoeleconverter",
    node_fields: [
      nodeType,
      name,
      devicetype,
      commissiondate,
      elecport,
      maintenanceperiod,
      manufacturedate,
      model,
      opticalport,
      { ...ratedcapacity, name: $T("额定功率"), unit: "kW" },
      voltagelevel,
      manufactor,
      ratedcurrent,
      { ...ratedvoltage, unit: "V" },
      brand,
      code,
      asset,
      operationstatus,
      warrantydate,
      documentField,
      pic
    ]
  }
];

// 空调机房
const room3 = [
  {
    modelLabel: "coldwatermainengine",
    node_fields: [
      nodeType,
      name,
      model,
      airconditionsystemtype,
      devicesize,
      deviceweight,
      coolingmethod,
      engineattr,
      voltagelevel,
      coolingcyclewater,
      minconstantoutwatertemp,
      mincoolingcycletemp,
      minrefrigeratingcycletemp,
      ratedmotorpower,
      ratedrefrigeration,
      refrigeratingcyclewater,
      refrigerationmediumtype,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "windset",
    node_fields: [
      nodeType,
      name,
      model,
      airconditionsystemtype,
      devicesize,
      deviceweight,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "pump",
    node_fields: [
      nodeType,
      name,
      locationField,
      changedate,
      functiontype,
      ratedblankingtimes,
      ratedstroke,
      physicaltype,
      operationmediumtype,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      ratedmotorpower,
      ratedmotorcurrent,
      { ...asset, type: "numberInt" },
      code,
      electricalmachinerymanufacturer,
      motortype,
      model,
      ownship,
      deviceclassification,
      manufactor,
      ratedmotorpowerfactor,
      commissiondate,
      ratedmotorefficiency,
      ratedlift,
      coolingmode,
      rateddischarge,
      lubricationmode,
      ratedspeed,
      usagestate,
      ratedworkingpressure,
      isscrap,
      effevaluation,
      efflimit,
      airconditionsystemtype,
      frequencytype,
      pumpcycletype,
      scrapdate,
      minworkingfrequency,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "plateheatexchanger",
    node_fields: [
      nodeType,
      name,
      ratedheatexchange,
      valvetype,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "plc",
    node_fields: [nodeType, name, brand, documentField, pic]
  }
];

// 空压机房
const room4 = [
  {
    modelLabel: "aircompressor",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      model,
      manufactor,
      ownship,
      deviceclassification,
      devicesize,
      deviceweight,
      aircompressorattr,
      voltagelevel,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "colddryingmachine",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      model,
      manufactor,
      ownship,
      deviceclassification,
      devicesize,
      deviceweight,
      colddrymachineattr,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "dryingmachine",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      model,
      manufactor,
      ownship,
      deviceclassification,
      devicesize,
      deviceweight,
      dryingagent,
      dryingmachineattr,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "coolingtower",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      model,
      manufactor,
      ownship,
      deviceclassification,
      airconditionsystemtype,
      devicesize,
      deviceweight,
      frequencytype,
      minworkingfrequency,
      ratedmotorpower,
      documentField,
      pic
    ]
  }
];

// 锅炉房
const room5 = [
  {
    modelLabel: "boiler",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      model,
      manufactor,
      ownship,
      deviceclassification,
      boilertype,
      devicesize,
      deviceweight,
      fueltype,
      documentField,
      pic
    ]
  }
];
const Fields = [
  {
    modelLabel: "sectionarea",
    node_fields: [nodeType, name, code, area, population, pic]
  },
  {
    modelLabel: "building",
    node_fields: [
      nodeType,
      name,
      code,
      address,
      floorarea,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "room",
    roomType: 1,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      voltagelevel,
      powerroomtype,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "room",
    roomType: 2,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "room",
    roomType: 3,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "room",
    roomType: 4,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "room",
    roomType: 5,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "room",
    roomType: 6,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      longitude,
      latitude,
      population,
      cooperativedeadline,
      commissiondate,
      pic
    ]
  },
  {
    modelLabel: "civicpipe",
    node_fields: [nodeType, name]
  },
  {
    modelLabel: "floor",
    node_fields: [nodeType, name, code, address, area, population, pic]
  },
  {
    modelLabel: "manuequipment",
    node_fields: [
      nodeType,
      name,
      code,
      brand,
      locationField,
      model,
      longitude,
      latitude,
      manufacturedate,
      commissiondate,
      manufactor,
      voltagelevel,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "meteorologicalmonitor",
    node_fields: [nodeType, name]
  },
  {
    modelLabel: "airconditioner",
    node_fields: [nodeType, name]
  },
  {
    modelLabel: "room",
    roomType: 13,
    node_fields: [
      nodeType,
      name,
      code,
      address,
      area,
      population,
      longitude,
      latitude,
      commissiondate,
      cooperativedeadline,
      voltagelevel,
      pic
    ]
  },
  {
    modelLabel: "pipeline",
    node_fields: [
      nodeType,
      name,
      {
        ...pipetype,
        change: tableData => {
          vue.$set(tableData, "energytype", null);
        }
      },
      {
        ...energytype,
        noEdit: true,
        name: $T("物料类型"),
        validator: formData => {
          return [
            {
              required: true,
              validator: checkMaterial.bind(formData),
              trigger: ["blur", "change"]
            }
          ];
        },
        filterElOption: options => {
          return options.filter(item => {
            return ![2, 13, 18, 22].includes(item.energytype);
          });
        },
        show: tableData => {
          return tableData.pipetype === 1;
        }
      },
      {
        ...rawmaterialtype,
        noEdit: true,
        show: tableData => {
          return tableData.pipetype === 2;
        }
      },
      {
        ...product,
        noEdit: true,
        show: tableData => {
          return tableData.pipetype === 3;
        }
      },
      {
        ...burnproducemattertype,
        noEdit: true,
        show: tableData => {
          return tableData.pipetype === 4;
        }
      },
      locationField,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      manufacturedate,
      model,
      manufactor,
      code,
      deviceclassification,
      pipefunctiontype,
      pipepositiontype,
      ownship,
      maxflow,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "powertransformer",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcapacity,
      coiltype,
      phasetype,
      ratedcurrent,
      ratedvoltage,
      shortcircuitimpedance,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      deviceclassification,
      locationField,
      code,
      brand,
      longitude,
      latitude,
      lastoverhauldate,
      nextoverhauldate,
      noloadcurrent,
      noloadloss,
      ptratio,
      shortcircuitloss,
      shortcircuitvoltage,
      status,
      warrantydate,
      ownship,
      transformerlevel,
      reactiveeconomicequivalent,
      sectionlowlimit,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "linesegmentwithswitch",
    node_fields: [
      nodeType,
      name,
      code,
      longitude,
      latitude,
      commissiondate,
      voltagelevel,
      ratedcurrent,
      {
        ...linefunctiontype,
        noEdit: true,
        rules: [
          {
            required: true,
            message: $T("请选择"),
            trigger: ["blur", "change"]
          }
        ]
      },
      loadclass,
      lastoverhauldate,
      nextoverhauldate,
      maintenanceperiod,
      deviceclassification,
      linesegmentwithswitch_id,
      brand,
      locationField,
      model,
      manufacturedate,
      manufactor,
      ctpolarity,
      ownship,
      ratedpower,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "busbarsection",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcurrent,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      busbarmarktype,
      deviceclassification,
      longitude,
      latitude,
      code,
      brand,
      locationField,
      materialtype,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "busbarconnector",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcapacity,
      busbarsegi,
      busbarsegii,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      {
        ...ctpolarity,
        nameTooltip: $T("选择正向时，母线一段流向母线二段")
      },
      deviceclassification,
      code,
      brand,
      locationField,
      longitude,
      latitude,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "generator",
    node_fields: [
      nodeType,
      name,
      code,
      brand,
      locationField,
      model,
      manufacturedate,
      commissiondate,
      manufactor,
      voltagelevel,
      lastoverhauldate,
      nextoverhauldate,
      maintenanceperiod,
      deviceclassification,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "hvdc",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcapacity,
      ratedvoltage,
      ratedcurrent,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      warrantydate,
      deviceclassification,
      asset,
      code,
      brand,
      locationField,
      longitude,
      latitude,
      lastpretestdate,
      operationstatus,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "ups",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      ratedcapacity,
      inputvoltage,
      outputvoltage,
      inverterefficiency,
      machineefficiency,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      deviceclassification,
      code,
      brand,
      locationField,
      warrantydate,
      conversiontime,
      upsstructure,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "battery",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      deviceclassification,
      locationField,
      brand,
      ownship,
      warrantydate,
      code,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "ptcabinet",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      deviceclassification,
      locationField,
      longitude,
      latitude,
      brand,
      code,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "capacitor",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      deviceclassification,
      locationField,
      longitude,
      latitude,
      brand,
      ownship,
      code,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "meteringcabinet",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      deviceclassification,
      locationField,
      longitude,
      latitude,
      brand,
      code,
      ownship,
      ratedpower,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "arraycabinet",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcapacity,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      ratedcurrent,
      ratedvoltage,
      devicetype,
      deviceclassification,
      asset,
      code,
      brand,
      locationField,
      longitude,
      latitude,
      lastpretestdate,
      warrantydate,
      operationstatus,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "powerdiscabinet",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      powerdiscabinettype,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      locationField,
      longitude,
      latitude,
      deviceclassification,
      asset,
      code,
      brand,
      lastpretestdate,
      warrantydate,
      operationstatus,
      devicetype,
      prodstandard,
      protlevel,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "switchcabinet",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcapacity,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      locationField,
      distributionpurpose,
      ctpolarity,
      deviceclassification,
      asset,
      code,
      brand,
      longitude,
      latitude,
      loadclass,
      lastpretestdate,
      ratedcurrent,
      ratedvoltage,
      warrantydate,
      operationstatus,
      switchfunctiontype,
      powerproperty,
      resistance,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "avc",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      model,
      manufacturedate,
      commissiondate,
      manufactor,
      voltagelevel,
      ratedcapacity,
      lastoverhauldate,
      nextoverhauldate,
      maintenanceperiod,
      deviceclassification,
      materialtype,
      devicetype,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "ats",
    node_fields: [
      nodeType,
      name,
      asset,
      code,
      brand,
      locationField,
      model,
      longitude,
      latitude,
      manufacturedate,
      commissiondate,
      manufactor,
      voltagelevel,
      ratedcapacity,
      lastoverhauldate,
      nextoverhauldate,
      maintenanceperiod,
      ratedcurrent,
      ratedvoltage,
      warrantydate,
      deviceclassification,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "dcpanel",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcapacity,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      nextoverhauldate,
      locationField,
      longitude,
      latitude,
      deviceclassification,
      asset,
      code,
      brand,
      lastpretestdate,
      ratedcurrent,
      ratedvoltage,
      warrantydate,
      operationstatus,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "itcabinet",
    node_fields: [
      nodeType,
      name,
      code,
      locationField,
      model,
      manufacturedate,
      commissiondate,
      manufactor,
      ratedcapacity,
      lastoverhauldate,
      nextoverhauldate,
      maintenanceperiod,
      deviceclassification,
      ownship,
      documentField,
      pic
    ]
  },
  {
    modelLabel: "linesegment",
    node_fields: [
      nodeType,
      name,
      manufactor,
      model,
      voltagelevel,
      ratedcurrent,
      manufacturedate,
      commissiondate,
      maintenanceperiod,
      lastoverhauldate,
      loadclass,
      locationField,
      linefunctiontype,
      ctpolarity,
      deviceclassification,
      asset,
      code,
      brand,
      longitude,
      latitude,
      lastpretestdate,
      resistance,
      warrantydate,
      distributionpurpose,
      operationstatus,
      powerproperty,
      ownship,
      ratedpower,
      documentField,
      pic
    ]
  },
  ...room2,
  ...room3,
  ...room4,
  ...room5,

  {
    modelLabel: "cataphoresis",
    node_fields: [
      nodeType,
      name,
      multiplyingpower,
      takttime,
      allowtime,
      linebodytype
    ]
  },
  {
    modelLabel: "virtualbuildingnode",
    node_fields: [nodeType, name]
  },
  {
    modelLabel: "virtualdevicenode",
    node_fields: [nodeType, name]
  }
];

export const addFields = customFields => {
  Fields.push(...customFields);
};
export const getFields = () => {
  return Fields;
};
