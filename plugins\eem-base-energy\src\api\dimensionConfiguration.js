import fetch from "eem-base/utils/fetch";
const version = "v1";
const service = "eem-service";

// 查询维度配置的内容
export function queryAttributedimensionConfig(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/config/query`,
    method: "POST",
    data
  });
}

// 新增维度属性
export function insertAttributedimensionConfig(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/config/insert`,
    method: "POST",
    data
  });
}

// 编辑配置列表
export function editAttributedimensionConfig(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/config/edit`,
    method: "POST",
    data
  });
}

// 删除配置列表
export function deleteAttributedimensionConfig(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/config/delete`,
    method: "DELETE",
    data
  });
}

// 查询维度标签和赋值的列表
export function attributedimensionNodeConfigTag(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/nodeconfig/tag`,
    method: "POST",
    data
  });
}

// 根据节点查询维度标签赋值
export function attributedimensionNodeConfigQuery(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/nodeconfig/query`,
    method: "POST",
    data
  });
}

// 节点维度标签赋值
export function attributedimensionNodeConfigEdit(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/nodeconfig/edit`,
    method: "POST",
    data
  });
}

// 节点维度标签导入
export function attributedimensionNodeConfigImport(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/nodeconfig/import`,
    method: "POST",
    data
  });
}

// 查询编辑记录
export function attributedimensionNodeConfigRecord(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/nodeconfig/record`,
    method: "POST",
    data
  });
}

// 查询编辑记录
export function attributedimensionNodeConfigColumnName(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/nodeconfig/column-name`,
    method: "POST",
    data
  });
}

// 标签排序
export function attributedimensionConfigSort(data) {
  return fetch({
    url: `/${service}/${version}/attributedimension/config/sort`,
    method: "POST",
    data
  });
}

// 查询多维度节点树的列表
export function queryAttributedimensionTreeList(data) {
  return fetch({
    url: `/eem-service/${version}/attribute-dimension/tree/list?projectId=${data.projectId}`,
    method: "POST",
    data
  });
}

// 选择节点树列表中的某一颗，查询具体的多维度节点树
export function queryAttributedimensionTreeNodeinfo(params) {
  return fetch({
    url: `/eem-service/${version}/attribute-dimension/tree/nodeinfo`,
    method: "POST",
    params
  });
}
